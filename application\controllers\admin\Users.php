<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_role('admin'); // <PERSON>ya admin yang bisa akses
        
        $this->load->model('User_model');
        $this->load->library('pagination');
    }

    /**
     * Halaman utama manajemen user
     */
    public function index()
    {
        $data['title'] = 'Manajemen User - PDAM System';
        
        // Pagination config
        $config['base_url'] = base_url('admin/users/index');
        $config['total_rows'] = $this->User_model->count_all_users();
        $config['per_page'] = 10;
        $config['uri_segment'] = 4;
        
        // Pagination styling
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_link'] = '&laquo; First';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_link'] = 'Last &raquo;';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $config['next_link'] = 'Next &raquo;';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['prev_link'] = '&laquo; Previous';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');

        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment(4)) ? $this->uri->segment(4) : 0;
        $search = $this->input->get('search');
        
        $data['users'] = $this->User_model->get_paginated($config['per_page'], $page, $search);
        $data['pagination'] = $this->pagination->create_links();
        $data['search'] = $search;
        
        // Statistics
        $data['total_admin'] = $this->User_model->count_by_role('admin');
        $data['total_penagih'] = $this->User_model->count_by_role('penagih');
        $data['total_pimpinan'] = $this->User_model->count_by_role('pimpinan');
        
        $this->load->view('admin/users/index', $data);
    }

    /**
     * Form tambah user baru
     */
    public function add()
    {
        $data['title'] = 'Tambah User - PDAM System';
        $data['action'] = 'add';
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Username', 'required|trim|is_unique[users.username]|min_length[3]|max_length[50]');
            $this->form_validation->set_rules('email', 'Email', 'required|trim|valid_email|is_unique[users.email]');
            $this->form_validation->set_rules('password', 'Password', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[password]');
            $this->form_validation->set_rules('nama_lengkap', 'Nama Lengkap', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('role', 'Role', 'required|in_list[admin,penagih,pimpinan]');
            $this->form_validation->set_rules('no_hp', 'No HP', 'trim|max_length[15]');
            $this->form_validation->set_rules('alamat', 'Alamat', 'trim');

            if ($this->form_validation->run() == TRUE) {
                $user_data = array(
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'password' => $this->auth_lib->hash_password($this->input->post('password')),
                    'nama_lengkap' => $this->input->post('nama_lengkap'),
                    'role' => $this->input->post('role'),
                    'no_hp' => $this->input->post('no_hp'),
                    'alamat' => $this->input->post('alamat'),
                    'status' => 'aktif'
                );

                if ($this->User_model->insert($user_data)) {
                    // Log activity
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Menambah user baru: ' . $user_data['username'],
                        'users'
                    );
                    
                    set_flash_message('success', 'User berhasil ditambahkan');
                    redirect('admin/users');
                } else {
                    set_flash_message('error', 'Gagal menambahkan user');
                }
            }
        }

        $this->load->view('admin/users/form', $data);
    }

    /**
     * Form edit user
     */
    public function edit($id = null)
    {
        if (!$id) {
            show_404();
        }

        $user = $this->User_model->get_by_id($id);
        if (!$user) {
            show_404();
        }

        $data['title'] = 'Edit User - PDAM System';
        $data['action'] = 'edit';
        $data['user'] = $user;

        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Username', 'required|trim|min_length[3]|max_length[50]|callback_check_username_exists[' . $id . ']');
            $this->form_validation->set_rules('email', 'Email', 'required|trim|valid_email|callback_check_email_exists[' . $id . ']');
            $this->form_validation->set_rules('nama_lengkap', 'Nama Lengkap', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('role', 'Role', 'required|in_list[admin,penagih,pimpinan]');
            $this->form_validation->set_rules('no_hp', 'No HP', 'trim|max_length[15]');
            $this->form_validation->set_rules('alamat', 'Alamat', 'trim');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[aktif,nonaktif]');

            // Jika password diisi, validasi password
            if ($this->input->post('password')) {
                $this->form_validation->set_rules('password', 'Password', 'min_length[6]');
                $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'matches[password]');
            }

            if ($this->form_validation->run() == TRUE) {
                $user_data = array(
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'nama_lengkap' => $this->input->post('nama_lengkap'),
                    'role' => $this->input->post('role'),
                    'no_hp' => $this->input->post('no_hp'),
                    'alamat' => $this->input->post('alamat'),
                    'status' => $this->input->post('status')
                );

                // Update password jika diisi
                if ($this->input->post('password')) {
                    $user_data['password'] = $this->auth_lib->hash_password($this->input->post('password'));
                }

                if ($this->User_model->update($id, $user_data)) {
                    // Log activity
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Mengedit user: ' . $user_data['username'],
                        'users',
                        $id
                    );
                    
                    set_flash_message('success', 'User berhasil diupdate');
                    redirect('admin/users');
                } else {
                    set_flash_message('error', 'Gagal mengupdate user');
                }
            }
        }

        $this->load->view('admin/users/form', $data);
    }

    /**
     * Hapus user
     */
    public function delete($id = null)
    {
        if (!$id) {
            show_404();
        }

        $user = $this->User_model->get_by_id($id);
        if (!$user) {
            show_404();
        }

        // Tidak bisa hapus diri sendiri
        if ($id == $this->auth_lib->get_user_data('user_id')) {
            set_flash_message('error', 'Tidak dapat menghapus akun sendiri');
            redirect('admin/users');
        }

        if ($this->User_model->delete($id)) {
            // Log activity
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Menghapus user: ' . $user->username,
                'users',
                $id
            );
            
            set_flash_message('success', 'User berhasil dihapus');
        } else {
            set_flash_message('error', 'Gagal menghapus user');
        }

        redirect('admin/users');
    }

    /**
     * Reset password user
     */
    public function reset_password($id = null)
    {
        if (!$id) {
            show_404();
        }

        $user = $this->User_model->get_by_id($id);
        if (!$user) {
            show_404();
        }

        // Generate password baru
        $new_password = $this->auth_lib->generate_password(8);
        $hashed_password = $this->auth_lib->hash_password($new_password);

        if ($this->User_model->update($id, array('password' => $hashed_password))) {
            // Log activity
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Reset password user: ' . $user->username,
                'users',
                $id
            );
            
            set_flash_message('success', 'Password berhasil direset. Password baru: <strong>' . $new_password . '</strong>');
        } else {
            set_flash_message('error', 'Gagal reset password');
        }

        redirect('admin/users');
    }

    /**
     * Toggle status user (aktif/nonaktif)
     */
    public function toggle_status($id = null)
    {
        if (!$id) {
            show_404();
        }

        $user = $this->User_model->get_by_id($id);
        if (!$user) {
            show_404();
        }

        // Tidak bisa nonaktifkan diri sendiri
        if ($id == $this->auth_lib->get_user_data('user_id')) {
            set_flash_message('error', 'Tidak dapat mengubah status akun sendiri');
            redirect('admin/users');
        }

        $new_status = ($user->status == 'aktif') ? 'nonaktif' : 'aktif';

        if ($this->User_model->update($id, array('status' => $new_status))) {
            // Log activity
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Mengubah status user ' . $user->username . ' menjadi ' . $new_status,
                'users',
                $id
            );
            
            set_flash_message('success', 'Status user berhasil diubah menjadi ' . $new_status);
        } else {
            set_flash_message('error', 'Gagal mengubah status user');
        }

        redirect('admin/users');
    }

    /**
     * Custom validation untuk check username exists
     */
    public function check_username_exists($username, $exclude_id)
    {
        if ($this->User_model->is_username_exists($username, $exclude_id)) {
            $this->form_validation->set_message('check_username_exists', 'Username sudah digunakan');
            return FALSE;
        }
        return TRUE;
    }

    /**
     * Custom validation untuk check email exists
     */
    public function check_email_exists($email, $exclude_id)
    {
        if ($this->User_model->is_email_exists($email, $exclude_id)) {
            $this->form_validation->set_message('check_email_exists', 'Email sudah digunakan');
            return FALSE;
        }
        return TRUE;
    }

    /**
     * AJAX search users
     */
    public function ajax_search()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $search = $this->input->post('search');
        $users = $this->User_model->search_users($search, 10);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($users));
    }

    /**
     * Export users to Excel
     */
    public function export()
    {
        $users = $this->User_model->get_all_for_export();
        
        // Set headers untuk download
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="users_' . date('Y-m-d') . '.xls"');
        
        echo "<table border='1'>";
        echo "<tr>";
        echo "<th>No</th>";
        echo "<th>Username</th>";
        echo "<th>Email</th>";
        echo "<th>Nama Lengkap</th>";
        echo "<th>Role</th>";
        echo "<th>No HP</th>";
        echo "<th>Status</th>";
        echo "<th>Dibuat</th>";
        echo "</tr>";
        
        $no = 1;
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $no++ . "</td>";
            echo "<td>" . $user->username . "</td>";
            echo "<td>" . $user->email . "</td>";
            echo "<td>" . $user->nama_lengkap . "</td>";
            echo "<td>" . ucfirst($user->role) . "</td>";
            echo "<td>" . $user->no_hp . "</td>";
            echo "<td>" . ucfirst($user->status) . "</td>";
            echo "<td>" . format_tanggal($user->created_at) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
}