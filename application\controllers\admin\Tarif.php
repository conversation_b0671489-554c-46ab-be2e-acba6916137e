<?php
// ========================================
// File: application/controllers/admin/Tarif.php
// ========================================

defined('BASEPATH') OR exit('No direct script access allowed');

class Tarif extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_role('admin'); // Hanya admin yang bisa akses
        
        $this->load->model('Tarif_model');
        $this->load->model('Penggunaan_model');
    }

    /**
     * Halaman utama daftar tarif
     */
    public function index()
    {
        $data['title'] = 'Kelola Tarif Air - PDAM System';
        
        // Get search and filter parameters
        $search = $this->input->get('search') ?? '';
        $status = $this->input->get('status') ?? '';
        $sort = $this->input->get('sort') ?? 'created_at';
        $order = $this->input->get('order') ?? 'DESC';
        
        // Pagination config
        $config['base_url'] = base_url('admin/tarif/index');
        $config['total_rows'] = $this->Tarif_model->count_paginated($search, $status);
        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['query_string_segment'] = 'page';
        
        // Bootstrap pagination
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><span class="page-link">';
        $config['cur_tag_close'] = '</span></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');
        
        $this->load->library('pagination', $config);
        
        $page = $this->input->get('page') ?? 0;
        
        // Get tarif data
        $data['tarif_list'] = $this->Tarif_model->get_paginated($config['per_page'], $page, $search, $status);
        $data['pagination'] = $this->pagination->create_links();
        $data['total_rows'] = $config['total_rows'];
        
        // Get statistics
        $data['stats'] = $this->Tarif_model->get_statistics();
        
        // Filter data for view
        $data['search'] = $search;
        $data['status_filter'] = $status;
        $data['sort'] = $sort;
        $data['order'] = $order;
        
        $this->load->view('admin/tarif/index', $data);
    }

    /**
     * Form tambah/edit tarif
     */
    public function form($id = null)
    {
        $data['title'] = ($id) ? 'Edit Tarif Air' : 'Tambah Tarif Air';
        $data['action'] = ($id) ? 'edit' : 'add';
        
        if ($id) {
            $data['tarif'] = $this->Tarif_model->get_by_id($id);
            if (!$data['tarif']) {
                show_404();
            }
        }

        if ($this->input->post()) {
            $this->form_validation->set_rules('nama_tarif', 'Nama Tarif', 'required|trim|max_length[50]');
            $this->form_validation->set_rules('tarif_per_m3', 'Tarif per m³', 'required|numeric|greater_than[0]');
            $this->form_validation->set_rules('batas_minimum', 'Batas Minimum', 'required|integer|greater_than[0]');
            $this->form_validation->set_rules('biaya_admin', 'Biaya Admin', 'required|numeric|greater_than_equal_to[0]');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[aktif,nonaktif]');

            // Check unique name
            $nama_tarif = $this->input->post('nama_tarif');
            if ($this->Tarif_model->is_name_exists($nama_tarif, $id)) {
                $this->form_validation->set_rules('nama_tarif', 'Nama Tarif', 'callback_check_unique_name');
            }

            if ($this->form_validation->run() == TRUE) {
                $tarif_data = array(
                    'nama_tarif' => $this->input->post('nama_tarif'),
                    'tarif_per_m3' => $this->input->post('tarif_per_m3'),
                    'batas_minimum' => $this->input->post('batas_minimum'),
                    'biaya_admin' => $this->input->post('biaya_admin'),
                    'status' => $this->input->post('status')
                );

                if ($id) {
                    // Update
                    if ($this->Tarif_model->update($id, $tarif_data)) {
                        $this->auth_lib->log_activity(
                            $this->auth_lib->get_user_data('user_id'),
                            'Mengupdate tarif: ' . $tarif_data['nama_tarif'],
                            'tarif',
                            $id
                        );
                        set_flash_message('success', 'Tarif berhasil diupdate');
                    } else {
                        set_flash_message('error', 'Gagal mengupdate tarif');
                    }
                } else {
                    // Insert
                    if ($this->Tarif_model->insert($tarif_data)) {
                        $this->auth_lib->log_activity(
                            $this->auth_lib->get_user_data('user_id'),
                            'Menambah tarif baru: ' . $tarif_data['nama_tarif'],
                            'tarif'
                        );
                        set_flash_message('success', 'Tarif berhasil ditambahkan');
                    } else {
                        set_flash_message('error', 'Gagal menambahkan tarif');
                    }
                }
                
                redirect('admin/tarif');
            }
        }

        $this->load->view('admin/tarif/form', $data);
    }

    /**
     * Custom validation - check unique name
     */
    public function check_unique_name($nama_tarif)
    {
        $this->form_validation->set_message('check_unique_name', 'Nama tarif sudah digunakan');
        return FALSE;
    }

    /**
     * Delete tarif
     */
    public function delete($id = null)
    {
        if (!$id) {
            show_404();
        }

        $tarif = $this->Tarif_model->get_by_id($id);
        if (!$tarif) {
            show_404();
        }

        // Cek apakah tarif sedang digunakan
        $usage_count = $this->Penggunaan_model->count_by_tarif($id);
        
        if ($usage_count > 0) {
            set_flash_message('error', 'Tidak dapat menghapus tarif. Masih ada ' . $usage_count . ' data penggunaan yang menggunakan tarif ini.');
            redirect('admin/tarif');
        }

        if ($this->Tarif_model->delete($id)) {
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Menghapus tarif: ' . $tarif->nama_tarif,
                'tarif',
                $id
            );
            set_flash_message('success', 'Tarif berhasil dihapus');
        } else {
            set_flash_message('error', 'Gagal menghapus tarif');
        }

        redirect('admin/tarif');
    }

    /**
     * Toggle Status Tarif
     */
    public function toggle_status($id = null)
    {
        if (!$id) {
            show_404();
        }

        $tarif = $this->Tarif_model->get_by_id($id);
        if (!$tarif) {
            show_404();
        }

        $new_status = ($tarif->status == 'aktif') ? 'nonaktif' : 'aktif';

        if ($this->Tarif_model->update($id, array('status' => $new_status))) {
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Mengubah status tarif ' . $tarif->nama_tarif . ' menjadi ' . $new_status,
                'tarif',
                $id
            );
            set_flash_message('success', 'Status tarif berhasil diubah');
        } else {
            set_flash_message('error', 'Gagal mengubah status tarif');
        }

        redirect('admin/tarif');
    }

    /**
     * Bulk Actions
     */
    public function bulk_action()
    {
        if (!$this->input->post()) {
            redirect('admin/tarif');
        }

        $action = $this->input->post('bulk_action');
        $ids = $this->input->post('selected_ids');

        if (empty($ids) || !is_array($ids)) {
            set_flash_message('error', 'Pilih minimal satu tarif');
            redirect('admin/tarif');
        }

        switch ($action) {
            case 'activate':
                if ($this->Tarif_model->bulk_update_status($ids, 'aktif')) {
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Mengaktifkan ' . count($ids) . ' tarif secara bulk',
                        'tarif'
                    );
                    set_flash_message('success', count($ids) . ' tarif berhasil diaktifkan');
                } else {
                    set_flash_message('error', 'Gagal mengaktifkan tarif');
                }
                break;

            case 'deactivate':
                if ($this->Tarif_model->bulk_update_status($ids, 'nonaktif')) {
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Menonaktifkan ' . count($ids) . ' tarif secara bulk',
                        'tarif'
                    );
                    set_flash_message('success', count($ids) . ' tarif berhasil dinonaktifkan');
                } else {
                    set_flash_message('error', 'Gagal menonaktifkan tarif');
                }
                break;

            case 'delete':
                $result = $this->Tarif_model->bulk_delete($ids);
                if ($result['success']) {
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Menghapus ' . count($ids) . ' tarif secara bulk',
                        'tarif'
                    );
                    set_flash_message('success', count($ids) . ' tarif berhasil dihapus');
                } else {
                    set_flash_message('error', $result['message']);
                }
                break;

            default:
                set_flash_message('error', 'Aksi tidak valid');
        }

        redirect('admin/tarif');
    }

    /**
     * Export data tarif
     */
    public function export($format = 'excel')
    {
        // Load library untuk export
        if ($format == 'excel') {
            $this->load->library('excel');
        }

        $tarifs = $this->Tarif_model->export_data();
        $filename = 'tarif_air_' . date('Y-m-d_H-i-s');

        switch ($format) {
            case 'excel':
                $this->_export_excel($tarifs, $filename);
                break;
            case 'csv':
                $this->_export_csv($tarifs, $filename);
                break;
            case 'pdf':
                $this->_export_pdf($tarifs, $filename);
                break;
            default:
                show_404();
        }
    }

    /**
     * Export to Excel
     */
    private function _export_excel($data, $filename)
    {
        // Implementation for Excel export
        // Using PhpSpreadsheet or similar library
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        
        // Create Excel content
        $output = "Nama Tarif\tTarif per m³\tBatas Minimum\tBiaya Admin\tStatus\tDibuat\n";
        foreach ($data as $row) {
            $output .= $row->nama_tarif . "\t";
            $output .= $row->tarif_per_m3 . "\t";
            $output .= $row->batas_minimum . "\t";
            $output .= $row->biaya_admin . "\t";
            $output .= $row->status . "\t";
            $output .= $row->created_at . "\n";
        }
        
        echo $output;
    }

    /**
     * Export to CSV
     */
    private function _export_csv($data, $filename)
    {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="' . $filename . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // Header
        fputcsv($output, array('Nama Tarif', 'Tarif per m³', 'Batas Minimum', 'Biaya Admin', 'Status', 'Dibuat'));
        
        // Data
        foreach ($data as $row) {
            fputcsv($output, array(
                $row->nama_tarif,
                $row->tarif_per_m3,
                $row->batas_minimum,
                $row->biaya_admin,
                $row->status,
                $row->created_at
            ));
        }
        
        fclose($output);
    }

    /**
     * Export to PDF
     */
    private function _export_pdf($data, $filename)
    {
        // Load PDF library (TCPDF, mPDF, etc.)
        $this->load->library('pdf');
        
        $html = '<h1>Daftar Tarif Air PDAM</h1>';
        $html .= '<table border="1" cellpadding="5">';
        $html .= '<tr><th>Nama Tarif</th><th>Tarif per m³</th><th>Batas Minimum</th><th>Biaya Admin</th><th>Status</th></tr>';
        
        foreach ($data as $row) {
            $html .= '<tr>';
            $html .= '<td>' . $row->nama_tarif . '</td>';
            $html .= '<td>Rp ' . number_format($row->tarif_per_m3, 0, ',', '.') . '</td>';
            $html .= '<td>' . $row->batas_minimum . ' m³</td>';
            $html .= '<td>Rp ' . number_format($row->biaya_admin, 0, ',', '.') . '</td>';
            $html .= '<td>' . ucfirst($row->status) . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</table>';
        
        $this->pdf->createPDF($html, $filename, true);
    }

    /**
     * AJAX Search
     */
    public function ajax_search()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $keyword = $this->input->post('keyword');
        $results = $this->Tarif_model->search($keyword);

        $output = array();
        foreach ($results as $tarif) {
            $output[] = array(
                'id' => $tarif->id,
                'nama_tarif' => $tarif->nama_tarif,
                'tarif_per_m3' => $tarif->tarif_per_m3,
                'status' => $tarif->status
            );
        }

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($output));
    }

    /**
     * Import tarif dari Excel/CSV
     */
    public function import()
    {
        $data['title'] = 'Import Tarif Air - PDAM System';

        if ($this->input->post()) {
            $config['upload_path'] = './uploads/temp/';
            $config['allowed_types'] = 'csv|xlsx|xls';
            $config['max_size'] = 2048; // 2MB
            $config['file_name'] = 'import_tarif_' . time();

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('import_file')) {
                $file_data = $this->upload->data();
                $file_path = $file_data['full_path'];

                // Process import based on file type
                $import_result = $this->_process_import($file_path, $file_data['file_ext']);

                // Delete temp file
                unlink($file_path);

                if ($import_result['success']) {
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Import tarif: ' . $import_result['imported'] . ' data berhasil diimport',
                        'tarif'
                    );
                    set_flash_message('success', $import_result['message']);
                } else {
                    set_flash_message('error', $import_result['message']);
                }

                redirect('admin/tarif');
            } else {
                set_flash_message('error', $this->upload->display_errors('', ''));
            }
        }

        $this->load->view('admin/tarif/import', $data);
    }

    /**
     * Process import file
     */
    private function _process_import($file_path, $file_ext)
    {
        $imported = 0;
        $errors = array();

        try {
            if ($file_ext == '.csv') {
                // Process CSV
                if (($handle = fopen($file_path, "r")) !== FALSE) {
                    $header = fgetcsv($handle); // Skip header
                    
                    while (($data = fgetcsv($handle)) !== FALSE) {
                        if (count($data) >= 4) {
                            $tarif_data = array(
                                'nama_tarif' => $data[0],
                                'tarif_per_m3' => floatval($data[1]),
                                'batas_minimum' => intval($data[2]),
                                'biaya_admin' => floatval($data[3]),
                                'status' => 'aktif'
                            );

                            // Validate data
                            $validation_errors = $this->Tarif_model->validate_data($tarif_data);
                            if (empty($validation_errors)) {
                                if ($this->Tarif_model->insert($tarif_data)) {
                                    $imported++;
                                }
                            } else {
                                $errors[] = 'Baris ' . ($imported + 1) . ': ' . implode(', ', $validation_errors);
                            }
                        }
                    }
                    
                    fclose($handle);
                }
            } else {
                // Process Excel (requires additional library)
                return array(
                    'success' => false,
                    'message' => 'Format Excel belum didukung. Gunakan CSV.'
                );
            }

            if ($imported > 0) {
                return array(
                    'success' => true,
                    'imported' => $imported,
                    'message' => $imported . ' tarif berhasil diimport.' . (count($errors) > 0 ? ' Dengan ' . count($errors) . ' error.' : '')
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Tidak ada data yang berhasil diimport. ' . implode(' ', $errors)
                );
            }

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Error saat import: ' . $e->getMessage()
            );
        }
    }

    /**
     * Detail tarif dengan usage statistics
     */
    public function detail($id = null)
    {
        if (!$id) {
            show_404();
        }

        $data['title'] = 'Detail Tarif Air - PDAM System';
        $data['tarif'] = $this->Tarif_model->get_by_id($id);
        
        if (!$data['tarif']) {
            show_404();
        }

        // Get usage statistics for this tarif
        $data['usage_stats'] = $this->Penggunaan_model->get_stats_by_tarif($id);
        $data['monthly_usage'] = $this->Penggunaan_model->get_monthly_usage_by_tarif($id);
        
        $this->load->view('admin/tarif/detail', $data);
    }
}