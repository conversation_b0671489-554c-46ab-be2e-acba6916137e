<?php
// ========================================
// File: application/models/Penggunaan_model.php
// ========================================

defined('BASEPATH') OR exit('No direct script access allowed');

class Penggunaan_model extends CI_Model {

    protected $table = 'penggunaan_air';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all penggunaan
     */
    public function get_all($bulan = null, $tahun = null)
    {
        $this->db->select('penggunaan_air.*, pelanggan.nama_pelanggan, pelanggan.no_pelanggan, pelanggan.alamat, tarif.nama_tarif, users.nama_lengkap as petugas_nama');
        $this->db->from($this->table);
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->join('tarif', 'tarif.id = penggunaan_air.id_tarif');
        $this->db->join('users', 'users.id = penggunaan_air.petugas_catat');
        
        if ($bulan) {
            $this->db->where('penggunaan_air.bulan', $bulan);
        }
        if ($tahun) {
            $this->db->where('penggunaan_air.tahun', $tahun);
        }
        
        $this->db->order_by('penggunaan_air.tanggal_catat', 'DESC');
        return $this->db->get()->result();
    }

    /**
     * Get by ID
     */
    public function get_by_id($id)
    {
        $this->db->select('penggunaan_air.*, pelanggan.nama_pelanggan, pelanggan.no_pelanggan, pelanggan.alamat, tarif.nama_tarif, tarif.tarif_per_m3, users.nama_lengkap as petugas_nama');
        $this->db->from($this->table);
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->join('tarif', 'tarif.id = penggunaan_air.id_tarif');
        $this->db->join('users', 'users.id = penggunaan_air.petugas_catat');
        $this->db->where('penggunaan_air.id', $id);
        return $this->db->get()->row();
    }

    /**
     * Get by pelanggan dan periode
     */
    public function get_by_pelanggan_periode($id_pelanggan, $bulan, $tahun)
    {
        $this->db->where('id_pelanggan', $id_pelanggan);
        $this->db->where('bulan', $bulan);
        $this->db->where('tahun', $tahun);
        return $this->db->get($this->table)->row();
    }

    /**
     * Insert penggunaan
     */
    public function insert($data)
    {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update penggunaan
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete penggunaan
     */
    public function delete($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Sum tagihan by month
     */
    public function sum_tagihan_by_month($bulan, $tahun)
    {
        $this->db->select_sum('total_tagihan');
        $this->db->where('bulan', $bulan);
        $this->db->where('tahun', $tahun);
        $result = $this->db->get($this->table)->row();
        return $result->total_tagihan ? $result->total_tagihan : 0;
    }

    /**
     * Count by petugas and month
     */
    public function count_by_petugas_month($petugas_id, $bulan, $tahun)
    {
        $this->db->where('petugas_catat', $petugas_id);
        $this->db->where('bulan', $bulan);
        $this->db->where('tahun', $tahun);
        return $this->db->count_all_results($this->table);
    }

    /**
     * Count by month
     */
    public function count_by_month($bulan, $tahun)
    {
        $this->db->where('bulan', $bulan);
        $this->db->where('tahun', $tahun);
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get pelanggan yang belum dicatat penggunaannya
     */
    public function get_pelanggan_belum_dicatat($bulan, $tahun)
    {
        $this->db->select('pelanggan.*');
        $this->db->from('pelanggan');
        $this->db->where('pelanggan.status_langganan', 'aktif');
        $this->db->where('pelanggan.id NOT IN (
            SELECT id_pelanggan FROM penggunaan_air 
            WHERE bulan = ' . $bulan . ' AND tahun = ' . $tahun . '
        )');
        return $this->db->get()->result();
    }

    /**
     * Get meter terakhir pelanggan
     */
    public function get_meter_terakhir($id_pelanggan)
    {
        $this->db->select('meter_akhir');
        $this->db->where('id_pelanggan', $id_pelanggan);
        $this->db->order_by('tahun', 'DESC');
        $this->db->order_by('bulan', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }

    /**
     * Get recent payments
     */
    public function get_recent($limit = 5)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, pelanggan.nama_pelanggan, pelanggan.no_pelanggan');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->order_by('pembayaran.created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }

    /**
     * Count verified today by petugas
     */
    public function count_verified_today_by_petugas($petugas_id)
    {
        $this->db->where('petugas_verifikasi', $petugas_id);
        $this->db->where('status_verifikasi', 'verified');
        $this->db->where('DATE(tanggal_verifikasi)', date('Y-m-d'));
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get payment chart data (for last N months)
     */
    public function get_payment_chart_data($months = 6)
    {
        $data = array();
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i months"));
            $bulan = date('m', strtotime($date));
            $tahun = date('Y', strtotime($date));
            
            $this->db->select_sum('pembayaran.jumlah_bayar');
            $this->db->from($this->table);
            $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
            $this->db->where('penggunaan_air.bulan', $bulan);
            $this->db->where('penggunaan_air.tahun', $tahun);
            $this->db->where('pembayaran.status_verifikasi', 'verified');
            $result = $this->db->get()->row();
            
            $data[] = array(
                'bulan' => nama_bulan($bulan) . ' ' . $tahun,
                'bulan_num' => $bulan,
                'tahun' => $tahun,
                'total' => $result->jumlah_bayar ? $result->jumlah_bayar : 0
            );
        }
        
        return $data;
    }

    /**
     * Get top payment areas
     */
    public function get_top_payment_areas($limit = 5)
    {
        $bulan_ini = date('m');
        $tahun_ini = date('Y');
        
        $this->db->select('SUBSTRING_INDEX(pelanggan.alamat, ",", 1) as area, COUNT(*) as total_pembayaran, SUM(pembayaran.jumlah_bayar) as total_nilai');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->where('penggunaan_air.bulan', $bulan_ini);
        $this->db->where('penggunaan_air.tahun', $tahun_ini);
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        $this->db->group_by('area');
        $this->db->order_by('total_nilai', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }

    /**
     * Check if payment exists for penggunaan
     */
    public function is_payment_exists($id_penggunaan)
    {
        $this->db->where('id_penggunaan', $id_penggunaan);
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Get pembayaran dengan pagination
     */
    public function get_paginated($limit, $offset, $search = null, $status = null)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, pelanggan.nama_pelanggan, pelanggan.no_pelanggan');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('pembayaran.no_kwitansi', $search);
            $this->db->or_like('pelanggan.nama_pelanggan', $search);
            $this->db->or_like('pelanggan.no_pelanggan', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('pembayaran.status_verifikasi', $status);
        }
        
        $this->db->order_by('pembayaran.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get()->result();
    }

    /**
     * Count total untuk pagination
     */
    public function count_total($search = null, $status = null)
    {
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('pembayaran.no_kwitansi', $search);
            $this->db->or_like('pelanggan.nama_pelanggan', $search);
            $this->db->or_like('pelanggan.no_pelanggan', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('pembayaran.status_verifikasi', $status);
        }
        
        return $this->db->count_all_results();
    }
    
    
    public function get_by_pelanggan($id_pelanggan, $limit = 12)
    {
        $this->db->select('penggunaan_air.*, tarif.nama_tarif, tarif.tarif_per_m3, users.nama_lengkap as petugas_nama');
        $this->db->from($this->table);
        $this->db->join('tarif', 'tarif.id = penggunaan_air.id_tarif');
        $this->db->join('users', 'users.id = penggunaan_air.petugas_catat');
        $this->db->where('penggunaan_air.id_pelanggan', $id_pelanggan);
        $this->db->order_by('penggunaan_air.tahun', 'DESC');
        $this->db->order_by('penggunaan_air.bulan', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }
    
    /**
     * Sum total tagihan by pelanggan
     */
    public function sum_tagihan_by_pelanggan($id_pelanggan)
    {
        $this->db->select_sum('total_tagihan');
        $this->db->where('id_pelanggan', $id_pelanggan);
        $result = $this->db->get($this->table)->row();
        return $result->total_tagihan ? $result->total_tagihan : 0;
    }
    
    /**
     * Count penggunaan by pelanggan
     */
    public function count_by_pelanggan($id_pelanggan)
    {
        $this->db->where('id_pelanggan', $id_pelanggan);
        return $this->db->count_all_results($this->table);
    }
    
    public function get_tagihan_by_pelanggan($pelanggan_id)
    {
        $this->db->select('pa.*, t.nama_tarif, t.tarif_per_m3, p.nama_pelanggan, p.no_pelanggan');
        $this->db->from('penggunaan_air pa');
        $this->db->join('tarif t', 'pa.id_tarif = t.id', 'left');
        $this->db->join('pelanggan p', 'pa.id_pelanggan = p.id', 'left');
        $this->db->where('pa.id_pelanggan', $pelanggan_id);
        $this->db->order_by('pa.tahun', 'DESC');
        $this->db->order_by('pa.bulan', 'DESC');
        
        return $this->db->get()->result();
    }
    
    /**
     * Get penggunaan with full details
     */
    public function get_with_details($penggunaan_id)
    {
        $this->db->select('pa.*, t.nama_tarif, t.tarif_per_m3, t.biaya_admin, p.nama_pelanggan, p.no_pelanggan, p.email, p.alamat');
        $this->db->from('penggunaan_air pa');
        $this->db->join('tarif t', 'pa.id_tarif = t.id', 'left');
        $this->db->join('pelanggan p', 'pa.id_pelanggan = p.id', 'left');
        $this->db->where('pa.id', $penggunaan_id);
        
        return $this->db->get()->row();
    }
    
}