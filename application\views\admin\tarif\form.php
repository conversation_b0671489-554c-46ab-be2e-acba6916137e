<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tag me-2"></i>
        <?php echo ($action == 'edit') ? 'Edit Tarif Air' : 'Tambah Tarif Air'; ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/tarif/'); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <?php if ($action == 'edit'): ?>
            <button type="button" class="btn btn-outline-info" onclick="previewTarif()">
                <i class="fas fa-eye"></i> Preview
            </button>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-lg-8">
        <!-- Main Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <?php echo ($action == 'edit') ? 'Edit Informasi Tarif' : 'Informasi Tarif Baru'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open(current_url(), 'id="tarifForm" novalidate'); ?>
                
                <!-- Nama Tarif -->
                <div class="mb-3">
                    <label for="nama_tarif" class="form-label">
                        Nama Tarif <span class="text-danger">*</span>
                    </label>
                    <input type="text" 
                           class="form-control <?php echo form_error('nama_tarif') ? 'is-invalid' : ''; ?>" 
                           id="nama_tarif" 
                           name="nama_tarif" 
                           value="<?php echo set_value('nama_tarif', isset($tarif) ? $tarif->nama_tarif : ''); ?>"
                           placeholder="Contoh: Tarif Rumah Tangga"
                           required>
                    <?php echo form_error('nama_tarif', '<div class="invalid-feedback">', '</div>'); ?>
                    <div class="form-text">Nama tarif yang mudah dikenali dan dipahami</div>
                </div>
                
                <div class="row">
                    <!-- Tarif per m³ -->
                    <div class="col-md-6 mb-3">
                        <label for="tarif_per_m3" class="form-label">
                            Tarif per m³ <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" 
                                   class="form-control <?php echo form_error('tarif_per_m3') ? 'is-invalid' : ''; ?>" 
                                   id="tarif_per_m3" 
                                   name="tarif_per_m3" 
                                   value="<?php echo set_value('tarif_per_m3', isset($tarif) ? $tarif->tarif_per_m3 : ''); ?>"
                                   min="1"
                                   step="100"
                                   placeholder="2500"
                                   required>
                            <span class="input-group-text">per m³</span>
                        </div>
                        <?php echo form_error('tarif_per_m3', '<div class="invalid-feedback d-block">', '</div>'); ?>
                        <div class="form-text">Harga yang dikenakan untuk setiap meter kubik air</div>
                    </div>
                    
                    <!-- Batas Minimum -->
                    <div class="col-md-6 mb-3">
                        <label for="batas_minimum" class="form-label">
                            Batas Minimum <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="number" 
                                   class="form-control <?php echo form_error('batas_minimum') ? 'is-invalid' : ''; ?>" 
                                   id="batas_minimum" 
                                   name="batas_minimum" 
                                   value="<?php echo set_value('batas_minimum', isset($tarif) ? $tarif->batas_minimum : '10'); ?>"
                                   min="1"
                                   placeholder="10"
                                   required>
                            <span class="input-group-text">m³</span>
                        </div>
                        <?php echo form_error('batas_minimum', '<div class="invalid-feedback d-block">', '</div>'); ?>
                        <div class="form-text">Minimum pemakaian yang akan ditagih</div>
                    </div>
                </div>
                
                <!-- Biaya Admin -->
                <div class="mb-3">
                    <label for="biaya_admin" class="form-label">
                        Biaya Administrasi <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                        <span class="input-group-text">Rp</span>
                        <input type="number" 
                               class="form-control <?php echo form_error('biaya_admin') ? 'is-invalid' : ''; ?>" 
                               id="biaya_admin" 
                               name="biaya_admin" 
                               value="<?php echo set_value('biaya_admin', isset($tarif) ? $tarif->biaya_admin : '5000'); ?>"
                               min="0"
                               step="500"
                               placeholder="5000"
                               required>
                    </div>
                    <?php echo form_error('biaya_admin', '<div class="invalid-feedback d-block">', '</div>'); ?>
                    <div class="form-text">Biaya tetap yang dikenakan setiap bulan (bisa diisi 0)</div>
                </div>
                
                <!-- Status -->
                <div class="mb-4">
                    <label class="form-label">Status Tarif <span class="text-danger">*</span></label>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="radio" 
                                       name="status" 
                                       id="status_aktif" 
                                       value="aktif" 
                                       <?php echo set_radio('status', 'aktif', isset($tarif) ? ($tarif->status == 'aktif') : true); ?>>
                                <label class="form-check-label" for="status_aktif">
                                    <i class="fas fa-check-circle text-success me-1"></i>
                                    <strong>Aktif</strong>
                                    <br><small class="text-muted">Tarif dapat digunakan untuk tagihan</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="radio" 
                                       name="status" 
                                       id="status_nonaktif" 
                                       value="nonaktif" 
                                       <?php echo set_radio('status', 'nonaktif', isset($tarif) ? ($tarif->status == 'nonaktif') : false); ?>>
                                <label class="form-check-label" for="status_nonaktif">
                                    <i class="fas fa-pause-circle text-warning me-1"></i>
                                    <strong>Nonaktif</strong>
                                    <br><small class="text-muted">Tarif tidak dapat digunakan</small>
                                </label>
                            </div>
                        </div>
                    </div>
                    <?php echo form_error('status', '<div class="text-danger mt-1">', '</div>'); ?>
                </div>
                
                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                        <i class="fas fa-undo"></i> Reset
                    </button>
                    <div>
                        <button type="button" class="btn btn-outline-info me-2" onclick="calculatePreview()">
                            <i class="fas fa-calculator"></i> Hitung Preview
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            <?php echo ($action == 'edit') ? 'Update Tarif' : 'Simpan Tarif'; ?>
                        </button>
                    </div>
                </div>
                
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Calculator Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>Kalkulator Tagihan
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="calc_usage" class="form-label">Simulasi Pemakaian</label>
                    <div class="input-group">
                        <input type="number" 
                               class="form-control" 
                               id="calc_usage" 
                               value="15" 
                               min="1" 
                               onchange="updateCalculation()">
                        <span class="input-group-text">m³</span>
                    </div>
                </div>
                
                <div class="calculation-result">
                    <div class="row text-sm">
                        <div class="col-7">Pemakaian:</div>
                        <div class="col-5 text-end"><span id="calc_actual_usage">15</span> m³</div>
                    </div>
                    <div class="row text-sm">
                        <div class="col-7">Ditagih:</div>
                        <div class="col-5 text-end"><span id="calc_billed_usage">15</span> m³</div>
                    </div>
                    <div class="row text-sm">
                        <div class="col-7">Tarif per m³:</div>
                        <div class="col-5 text-end">Rp <span id="calc_rate">0</span></div>
                    </div>
                    <div class="row text-sm">
                        <div class="col-7">Subtotal:</div>
                        <div class="col-5 text-end">Rp <span id="calc_subtotal">0</span></div>
                    </div>
                    <div class="row text-sm">
                        <div class="col-7">Biaya Admin:</div>
                        <div class="col-5 text-end">Rp <span id="calc_admin">0</span></div>
                    </div>
                    <hr>
                    <div class="row fw-bold">
                        <div class="col-7">Total Tagihan:</div>
                        <div class="col-5 text-end text-primary">Rp <span id="calc_total">0</span></div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Perhitungan otomatis berdasarkan input form
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Comparison with Existing Tarifs -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Perbandingan Tarif
                </h6>
            </div>
            <div class="card-body">
                <div id="comparison-chart" class="text-center">
                    <small class="text-muted">Masukkan data tarif untuk melihat perbandingan</small>
                </div>
            </div>
        </div>
        
        <!-- Tips & Guidelines -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Tips & Panduan
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Gunakan nama tarif yang jelas dan mudah dipahami</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Sesuaikan tarif dengan standar PDAM setempat</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Batas minimum biasanya 10-15 m³</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>Biaya admin bisa disesuaikan dengan kebijakan</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Preview Tarif</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be populated here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" onclick="saveFromPreview()">
                    <i class="fas fa-save"></i> Simpan Tarif
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Setup form validation
    setupFormValidation();
    
    // Initial calculation update
    updateCalculation();
    
    // Setup auto-save draft
    setupAutoSave();
    
    // Setup real-time updates
    setupRealTimeUpdates();
});

// Form validation
function setupFormValidation() {
    const form = document.getElementById('tarifForm');
    
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // Real-time validation
    const inputs = form.querySelectorAll('input[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            // Clear previous error state
            this.classList.remove('is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.style.display = 'none';
            }
        });
    });
}

// Validate individual field
function validateField(field) {
    let isValid = true;
    let message = '';
    
    const value = field.value.trim();
    const fieldName = field.name;
    
    switch (fieldName) {
        case 'nama_tarif':
            if (!value) {
                isValid = false;
                message = 'Nama tarif wajib diisi';
            } else if (value.length < 3) {
                isValid = false;
                message = 'Nama tarif minimal 3 karakter';
            }
            break;
            
        case 'tarif_per_m3':
            if (!value || isNaN(value) || parseFloat(value) <= 0) {
                isValid = false;
                message = 'Tarif per m³ harus berupa angka positif';
            }
            break;
            
        case 'batas_minimum':
            if (!value || isNaN(value) || parseInt(value) <= 0) {
                isValid = false;
                message = 'Batas minimum harus berupa angka positif';
            }
            break;
            
        case 'biaya_admin':
            if (!value || isNaN(value) || parseFloat(value) < 0) {
                isValid = false;
                message = 'Biaya admin harus berupa angka non-negatif';
            }
            break;
    }
    
    // Update field state
    if (isValid) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    } else {
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
        
        // Show custom error message
        let feedback = field.parentNode.querySelector('.invalid-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            field.parentNode.appendChild(feedback);
        }
        feedback.textContent = message;
        feedback.style.display = 'block';
    }
    
    // Update calculation when values change
    if (isValid && ['tarif_per_m3', 'batas_minimum', 'biaya_admin'].includes(fieldName)) {
        updateCalculation();
    }
    
    return isValid;
}

// Validate entire form
function validateForm() {
    const form = document.getElementById('tarifForm');
    const inputs = form.querySelectorAll('input[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // Check if status is selected
    const statusRadios = form.querySelectorAll('input[name="status"]');
    const statusSelected = Array.from(statusRadios).some(radio => radio.checked);
    
    if (!statusSelected) {
        isValid = false;
        const statusError = document.createElement('div');
        statusError.className = 'text-danger mt-1';
        statusError.textContent = 'Status tarif wajib dipilih';
        
        const existingError = form.querySelector('input[name="status"]').closest('.mb-4').querySelector('.text-danger');
        if (!existingError) {
            form.querySelector('input[name="status"]').closest('.mb-4').appendChild(statusError);
        }
    }
    
    return isValid;
}

// Update calculation
function updateCalculation() {
    const tarif = parseFloat(document.getElementById('tarif_per_m3').value) || 0;
    const minimum = parseInt(document.getElementById('batas_minimum').value) || 0;
    const admin = parseFloat(document.getElementById('biaya_admin').value) || 0;
    const usage = parseInt(document.getElementById('calc_usage').value) || 0;
    
    const billedUsage = Math.max(usage, minimum);
    const subtotal = billedUsage * tarif;
    const total = subtotal + admin;
    
    // Update display
    document.getElementById('calc_actual_usage').textContent = usage;
    document.getElementById('calc_billed_usage').textContent = billedUsage;
    document.getElementById('calc_rate').textContent = formatNumber(tarif);
    document.getElementById('calc_subtotal').textContent = formatNumber(subtotal);
    document.getElementById('calc_admin').textContent = formatNumber(admin);
    document.getElementById('calc_total').textContent = formatNumber(total);
    
    // Update comparison chart
    updateComparisonChart(tarif);
}

// Format number for display
function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

// Update comparison chart
function updateComparisonChart(currentTarif) {
    if (currentTarif <= 0) return;
    
    // Sample existing tarifs (in real implementation, get from server)
    const existingTarifs = [
        { nama: 'Rumah Tangga', tarif: 2500 },
        { nama: 'Niaga Kecil', tarif: 3500 },
        { nama: 'Niaga Besar', tarif: 4000 }
    ];
    
    const chartContainer = document.getElementById('comparison-chart');
    
    let chartHTML = '<div class="mb-3"><small class="text-muted">Perbandingan dengan tarif yang ada:</small></div>';
    
    existingTarifs.forEach(tarif => {
        const percentage = ((currentTarif - tarif.tarif) / tarif.tarif * 100);
        const isHigher = percentage > 0;
        const color = isHigher ? 'danger' : 'success';
        const icon = isHigher ? 'fa-arrow-up' : 'fa-arrow-down';
        
        chartHTML += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <small>${tarif.nama}</small>
                <div class="text-${color}">
                    <i class="fas ${icon} me-1"></i>
                    <small>${Math.abs(percentage).toFixed(1)}%</small>
                </div>
            </div>
        `;
    });
    
    chartContainer.innerHTML = chartHTML;
}

// Calculate preview
function calculatePreview() {
    if (!validateForm()) {
        alert('Mohon lengkapi semua field yang wajib diisi');
        return;
    }
    
    const formData = new FormData(document.getElementById('tarifForm'));
    const data = Object.fromEntries(formData);
    
    // Sample calculations for different usage scenarios
    const scenarios = [5, 10, 15, 20, 25, 30];
    let previewHTML = `
        <div class="row mb-3">
            <div class="col-md-6">
                <h6>Informasi Tarif</h6>
                <table class="table table-sm">
                    <tr><td>Nama:</td><td><strong>${data.nama_tarif}</strong></td></tr>
                    <tr><td>Tarif per m³:</td><td>Rp ${formatNumber(data.tarif_per_m3)}</td></tr>
                    <tr><td>Minimum:</td><td>${data.batas_minimum} m³</td></tr>
                    <tr><td>Biaya Admin:</td><td>Rp ${formatNumber(data.biaya_admin)}</td></tr>
                    <tr><td>Status:</td><td><span class="badge bg-${data.status === 'aktif' ? 'success' : 'warning'}">${data.status}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Simulasi Tagihan</h6>
                <table class="table table-sm">
                    <thead>
                        <tr><th>Pemakaian</th><th>Total Tagihan</th></tr>
                    </thead>
                    <tbody>
    `;
    
    scenarios.forEach(usage => {
        const billedUsage = Math.max(usage, parseInt(data.batas_minimum));
        const total = (billedUsage * parseFloat(data.tarif_per_m3)) + parseFloat(data.biaya_admin);
        previewHTML += `<tr><td>${usage} m³</td><td>Rp ${formatNumber(total)}</td></tr>`;
    });
    
    previewHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = previewHTML;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Save from preview
function saveFromPreview() {
    bootstrap.Modal.getInstance(document.getElementById('previewModal')).hide();
    document.getElementById('tarifForm').submit();
}

// Preview tarif (for edit mode)
function previewTarif() {
    calculatePreview();
}

// Reset form
function resetForm() {
    if (confirm('Reset semua perubahan? Data yang belum disimpan akan hilang.')) {
        document.getElementById('tarifForm').reset();
        
        // Clear validation states
        const form = document.getElementById('tarifForm');
        form.classList.remove('was-validated');
        form.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
            el.classList.remove('is-valid', 'is-invalid');
        });
        
        // Reset calculation
        updateCalculation();
        
        // Clear auto-save
        localStorage.removeItem('tarif_draft');
    }
}

// Setup real-time updates
function setupRealTimeUpdates() {
    const inputs = ['tarif_per_m3', 'batas_minimum', 'biaya_admin'];
    
    inputs.forEach(inputId => {
        document.getElementById(inputId).addEventListener('input', function() {
            updateCalculation();
        });
    });
    
    document.getElementById('calc_usage').addEventListener('input', updateCalculation);
}

// Auto-save draft
function setupAutoSave() {
    const form = document.getElementById('tarifForm');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('change', saveDraft);
        input.addEventListener('input', debounce(saveDraft, 1000));
    });
    
    // Load draft on page load
    loadDraft();
}

function saveDraft() {
    const formData = new FormData(document.getElementById('tarifForm'));
    const draftData = Object.fromEntries(formData);
    localStorage.setItem('tarif_draft', JSON.stringify(draftData));
}

function loadDraft() {
    const draft = localStorage.getItem('tarif_draft');
    if (draft && confirm('Ditemukan draft yang belum disimpan. Muat draft?')) {
        const draftData = JSON.parse(draft);
        
        Object.keys(draftData).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'radio') {
                    if (element.value === draftData[key]) {
                        element.checked = true;
                    }
                } else {
                    element.value = draftData[key];
                }
            }
        });
        
        updateCalculation();
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + S = Save
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.getElementById('tarifForm').submit();
    }
    
    // Ctrl + R = Reset
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        resetForm();
    }
    
    // Ctrl + P = Preview
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        calculatePreview();
    }
});

// Clear draft when form is successfully submitted
document.getElementById('tarifForm').addEventListener('submit', function() {
    localStorage.removeItem('tarif_draft');
});

// Format currency inputs on blur
document.querySelectorAll('input[type="number"]').forEach(input => {
    input.addEventListener('blur', function() {
        if (this.value) {
            // Add thousands separator visual feedback
            const value = parseFloat(this.value);
            this.setAttribute('data-formatted', formatNumber(value));
        }
    });
});
</script>

<?php $this->load->view('templates/footer'); ?>