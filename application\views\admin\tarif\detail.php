
<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tag me-2"></i>Detail Tarif: <?php echo htmlspecialchars($tarif->nama_tarif); ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/tarif'); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo base_url('admin/tarif/form/' . $tarif->id); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Tarif
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Informasi Tarif</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td width="40%"><strong>Nama Tarif:</strong></td>
                        <td><?php echo htmlspecialchars($tarif->nama_tarif); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Tarif per m³:</strong></td>
                        <td>Rp <?php echo number_format($tarif->tarif_per_m3, 0, ',', '.'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Batas Minimum:</strong></td>
                        <td><?php echo $tarif->batas_minimum; ?> m³</td>
                    </tr>
                    <tr>
                        <td><strong>Biaya Admin:</strong></td>
                        <td>Rp <?php echo number_format($tarif->biaya_admin, 0, ',', '.'); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-<?php echo ($tarif->status == 'aktif') ? 'success' : 'warning'; ?>">
                                <?php echo ucfirst($tarif->status); ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Dibuat:</strong></td>
                        <td><?php echo date('d/m/Y H:i', strtotime($tarif->created_at)); ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Simulasi Tagihan</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">Contoh perhitungan untuk berbagai pemakaian:</p>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Pemakaian</th>
                            <th>Ditagih</th>
                            <th>Total Tagihan</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $scenarios = [5, 10, 15, 20, 25, 30];
                        foreach($scenarios as $usage): 
                            $billed = max($usage, $tarif->batas_minimum);
                            $total = ($billed * $tarif->tarif_per_m3) + $tarif->biaya_admin;
                        ?>
                        <tr>
                            <td><?php echo $usage; ?> m³</td>
                            <td><?php echo $billed; ?> m³</td>
                            <td>Rp <?php echo number_format($total, 0, ',', '.'); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('templates/footer'); ?>