<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-edit me-2"></i>Edit Pembayaran</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pembayaran'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo base_url('admin/pembayaran/detail/' . $pembayaran->id); ?>" class="btn btn-sm btn-info">
                <i class="fas fa-eye"></i> Detail
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Form Edit Pembayaran</h5>
            </div>
            <div class="card-body">
                <?php echo form_open('', ['id' => 'form-edit-pembayaran']); ?>
                
                <!-- Info Pembayaran -->
                <div class="alert alert-info">
                    <div class="row">
                        <div class="col-md-6">
                            <strong><i class="fas fa-receipt me-2"></i>No. Kwitansi:</strong> <?php echo $pembayaran->no_kwitansi; ?><br>
                            <strong><i class="fas fa-user me-2"></i>Pelanggan:</strong> <?php echo $pembayaran->nama_pelanggan; ?><br>
                            <strong><i class="fas fa-calendar me-2"></i>Periode:</strong> <?php echo nama_bulan($pembayaran->bulan) . ' ' . $pembayaran->tahun; ?>
                        </div>
                        <div class="col-md-6">
                            <strong><i class="fas fa-tint me-2"></i>Pemakaian:</strong> <?php echo $pembayaran->pemakaian; ?> m³<br>
                            <strong><i class="fas fa-money-bill me-2"></i>Total Tagihan:</strong> <?php echo format_rupiah($pembayaran->total_tagihan); ?><br>
                            <strong><i class="fas fa-info-circle me-2"></i>Status:</strong> 
                            <?php
                            $status_class = '';
                            switch ($pembayaran->status_verifikasi) {
                                case 'pending': $status_class = 'warning'; break;
                                case 'verified': $status_class = 'success'; break;
                                case 'rejected': $status_class = 'danger'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $status_class; ?>"><?php echo ucfirst($pembayaran->status_verifikasi); ?></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="jumlah_bayar" class="form-label">Jumlah Bayar <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="jumlah_bayar" name="jumlah_bayar" 
                                   min="0" step="1000" value="<?php echo $pembayaran->jumlah_bayar; ?>" required>
                            <?php echo form_error('jumlah_bayar', '<small class="text-danger">', '</small>'); ?>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i> Tagihan: <?php echo format_rupiah($pembayaran->total_tagihan); ?>
                            </small>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="metode_bayar" class="form-label">Metode Pembayaran <span class="text-danger">*</span></label>
                            <select class="form-select" id="metode_bayar" name="metode_bayar" required>
                                <option value="">-- Pilih Metode --</option>
                                <option value="tunai" <?php echo ($pembayaran->metode_bayar == 'tunai') ? 'selected' : ''; ?>>💵 Tunai</option>
                                <option value="transfer" <?php echo ($pembayaran->metode_bayar == 'transfer') ? 'selected' : ''; ?>>🏦 Transfer Bank</option>
                                <option value="qris" <?php echo ($pembayaran->metode_bayar == 'qris') ? 'selected' : ''; ?>>📱 QRIS</option>
                                <option value="debit" <?php echo ($pembayaran->metode_bayar == 'debit') ? 'selected' : ''; ?>>💳 Kartu Debit</option>
                            </select>
                            <?php echo form_error('metode_bayar', '<small class="text-danger">', '</small>'); ?>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="tanggal_bayar" class="form-label">Tanggal Bayar <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal_bayar" name="tanggal_bayar" 
                                   value="<?php echo $pembayaran->tanggal_bayar; ?>" required>
                            <?php echo form_error('tanggal_bayar', '<small class="text-danger">', '</small>'); ?>
                        </div>
                    </div>
                </div>

                <!-- Selisih Calculation -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="selisih" class="form-label">Selisih Pembayaran</label>
                            <input type="text" class="form-control" id="selisih" readonly 
                                   style="background-color: #e9ecef; font-weight: bold;">
                            <small class="form-text text-muted">
                                <i class="fas fa-calculator"></i> Otomatis: Jumlah Bayar - Total Tagihan
                            </small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="keterangan" class="form-label">Keterangan</label>
                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                              placeholder="Keterangan tambahan (opsional)"><?php echo isset($pembayaran->keterangan) ? $pembayaran->keterangan : ''; ?></textarea>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Pembayaran
                    </button>
                    <a href="<?php echo base_url('admin/pembayaran'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Batal
                    </a>
                    <a href="<?php echo base_url('admin/pembayaran/detail/' . $pembayaran->id); ?>" class="btn btn-info">
                        <i class="fas fa-eye me-2"></i>Lihat Detail
                    </a>
                </div>

                <?php echo form_close(); ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Info Pembayaran -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Info Pembayaran</h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    <tr>
                        <td class="text-muted">No. Kwitansi:</td>
                        <td><strong class="text-primary"><?php echo $pembayaran->no_kwitansi; ?></strong></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Pelanggan:</td>
                        <td>
                            <strong><?php echo $pembayaran->nama_pelanggan; ?></strong><br>
                            <small class="text-muted"><?php echo $pembayaran->no_pelanggan; ?></small>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">Alamat:</td>
                        <td><?php echo $pembayaran->alamat; ?></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Periode:</td>
                        <td><strong><?php echo nama_bulan($pembayaran->bulan) . ' ' . $pembayaran->tahun; ?></strong></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Pemakaian:</td>
                        <td><span class="badge bg-primary"><?php echo $pembayaran->pemakaian; ?> m³</span></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Total Tagihan:</td>
                        <td><strong class="text-success"><?php echo format_rupiah($pembayaran->total_tagihan); ?></strong></td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Status & History -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-history me-2"></i>Status & History</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-light p-3">
                    <div class="mb-2">
                        <strong>Status Saat Ini:</strong>
                        <span class="badge bg-<?php echo $status_class; ?> ms-2"><?php echo ucfirst($pembayaran->status_verifikasi); ?></span>
                    </div>
                    
                    <?php if (isset($pembayaran->created_at) && $pembayaran->created_at): ?>
                    <div class="mb-2">
                        <strong>Tanggal Input:</strong><br>
                        <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($pembayaran->created_at)); ?></small>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($pembayaran->petugas_input) && $pembayaran->petugas_input): ?>
                    <div class="mb-2">
                        <strong>Petugas Input:</strong><br>
                        <small class="text-muted"><?php echo $pembayaran->petugas_input; ?></small>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($pembayaran->status_verifikasi != 'pending'): ?>
                    <div class="mb-2">
                        <strong>Verifikator:</strong><br>
                        <small class="text-muted"><?php echo isset($pembayaran->verifikator_nama) ? $pembayaran->verifikator_nama : 'Unknown'; ?></small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Warning -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Peringatan</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning p-3">
                    <ul class="mb-0" style="font-size: 14px;">
                        <li>Hati-hati saat mengubah data pembayaran</li>
                        <li>Perubahan akan mempengaruhi laporan</li>
                        <li>Pastikan data yang diinput sudah benar</li>
                        <?php if ($pembayaran->status_verifikasi == 'verified'): ?>
                        <li><strong>Pembayaran sudah terverifikasi!</strong></li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Edit pembayaran form loaded');
    
    // Calculate selisih on page load
    hitungSelisih();
    
    // Event jumlah bayar change
    document.getElementById('jumlah_bayar').addEventListener('input', function() {
        hitungSelisih();
    });
    
    function hitungSelisih() {
        const tagihan = <?php echo $pembayaran->total_tagihan; ?>;
        const bayar = parseInt(document.getElementById('jumlah_bayar').value) || 0;
        const selisih = bayar - tagihan;
        
        let selisihText = '';
        let selisihColor = '';
        
        if (selisih > 0) {
            selisihText = '+Rp ' + selisih.toLocaleString('id-ID') + ' (Lebih Bayar)';
            selisihColor = '#28a745'; // Green
        } else if (selisih < 0) {
            selisihText = 'Rp ' + selisih.toLocaleString('id-ID') + ' (Kurang Bayar)';
            selisihColor = '#dc3545'; // Red
        } else {
            selisihText = 'Rp 0 (Pas)';
            selisihColor = '#6c757d'; // Gray
        }
        
        const selisihField = document.getElementById('selisih');
        selisihField.value = selisihText;
        selisihField.style.color = selisihColor;
        
        console.log('Selisih calculated:', selisihText);
    }
    
    // Form submit validation
    document.getElementById('form-edit-pembayaran').addEventListener('submit', function(e) {
        const jumlahBayar = parseInt(document.getElementById('jumlah_bayar').value) || 0;
        const metodeBayar = document.getElementById('metode_bayar').value;
        const tanggalBayar = document.getElementById('tanggal_bayar').value;
        
        if (!jumlahBayar || !metodeBayar || !tanggalBayar) {
            e.preventDefault();
            alert('Mohon lengkapi semua field yang wajib diisi!');
            return false;
        }
        
        if (jumlahBayar <= 0) {
            e.preventDefault();
            alert('Jumlah bayar harus lebih dari 0!');
            return false;
        }
        
        console.log('Form submitted with valid data');
    });
});
</script>

<?php $this->load->view('templates/footer'); ?>
