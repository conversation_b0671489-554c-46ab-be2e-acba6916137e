<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-check-circle me-2"></i>Verifikasi Pembayaran</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?php echo base_url('penagih/pembayaran'); ?>" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-md-8">
        <!-- Detail Pembayaran -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Detail Pembayaran</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted">No. Kwitansi:</td>
                                <td><strong class="text-primary"><?php echo $pembayaran->no_kwitansi; ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Pelanggan:</td>
                                <td>
                                    <strong><?php echo $pembayaran->nama_pelanggan; ?></strong><br>
                                    <small class="text-muted"><?php echo $pembayaran->no_pelanggan; ?></small>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Periode:</td>
                                <td><strong><?php echo nama_bulan($pembayaran->bulan) . ' ' . $pembayaran->tahun; ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Pemakaian:</td>
                                <td><span class="badge bg-primary"><?php echo $pembayaran->pemakaian; ?> m³</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted">Total Tagihan:</td>
                                <td><strong class="text-success"><?php echo format_rupiah($pembayaran->total_tagihan); ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Jumlah Bayar:</td>
                                <td><strong class="text-primary"><?php echo format_rupiah($pembayaran->jumlah_bayar); ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Selisih:</td>
                                <td>
                                    <?php 
                                    $selisih = $pembayaran->jumlah_bayar - $pembayaran->total_tagihan;
                                    $selisih_class = $selisih >= 0 ? 'success' : 'danger';
                                    ?>
                                    <strong class="text-<?php echo $selisih_class; ?>">
                                        <?php echo format_rupiah($selisih); ?>
                                    </strong>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Metode Bayar:</td>
                                <td>
                                    <?php
                                    $metode_class = '';
                                    switch ($pembayaran->metode_bayar) {
                                        case 'tunai': $metode_class = 'success'; break;
                                        case 'transfer': $metode_class = 'primary'; break;
                                        case 'qris': $metode_class = 'info'; break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $metode_class; ?> fs-6">
                                        <?php echo ucfirst($pembayaran->metode_bayar); ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <strong>Alamat:</strong> <?php echo $pembayaran->alamat; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Verifikasi -->
        <?php if ($pembayaran->status_verifikasi == 'pending'): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Form Verifikasi</h5>
            </div>
            <div class="card-body">
                <?php echo form_open(); ?>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Status Verifikasi <span class="text-danger">*</span></label>
                            <select class="form-select" name="status_verifikasi" required>
                                <option value="">-- Pilih Status --</option>
                                <option value="verified">Verified (Terima)</option>
                                <option value="rejected">Rejected (Tolak)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Tanggal Bayar</label>
                            <input type="text" class="form-control" value="<?php echo format_tanggal($pembayaran->tanggal_bayar); ?>" readonly>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Catatan Verifikasi</label>
                    <textarea class="form-control" name="catatan" rows="3" placeholder="Catatan tambahan (opsional)"></textarea>
                </div>
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Simpan Verifikasi
                    </button>
                    <a href="<?php echo base_url('penagih/pembayaran'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Batal
                    </a>
                </div>
                <?php echo form_close(); ?>
            </div>
        </div>
        <?php else: ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Status Verifikasi</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-<?php echo ($pembayaran->status_verifikasi == 'verified') ? 'success' : 'danger'; ?>">
                    <h6><i class="fas fa-<?php echo ($pembayaran->status_verifikasi == 'verified') ? 'check-circle' : 'times-circle'; ?> me-2"></i>
                        Pembayaran sudah <?php echo ($pembayaran->status_verifikasi == 'verified') ? 'DIVERIFIKASI' : 'DITOLAK'; ?>
                    </h6>
                    <?php if ($pembayaran->tanggal_verifikasi): ?>
                    <small>Tanggal: <?php echo format_tanggal($pembayaran->tanggal_verifikasi, true); ?></small>
                    <?php endif; ?>
                    <?php if ($pembayaran->catatan): ?>
                    <hr>
                    <strong>Catatan:</strong> <?php echo $pembayaran->catatan; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="col-md-4">
        <!-- Status Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Status Saat Ini</h6>
            </div>
            <div class="card-body text-center">
                <?php
                $status_class = '';
                $status_icon = '';
                $status_text = '';
                switch ($pembayaran->status_verifikasi) {
                    case 'verified':
                        $status_class = 'success';
                        $status_icon = 'check-circle';
                        $status_text = 'VERIFIED';
                        break;
                    case 'pending':
                        $status_class = 'warning';
                        $status_icon = 'clock';
                        $status_text = 'PENDING';
                        break;
                    case 'rejected':
                        $status_class = 'danger';
                        $status_icon = 'times-circle';
                        $status_text = 'REJECTED';
                        break;
                }
                ?>
                <i class="fas fa-<?php echo $status_icon; ?> fa-4x text-<?php echo $status_class; ?> mb-3"></i>
                <h4 class="text-<?php echo $status_class; ?>"><?php echo $status_text; ?></h4>
                
                <?php if ($pembayaran->status_verifikasi != 'pending'): ?>
                <hr>
                <small class="text-muted">
                    Diverifikasi: <?php echo format_tanggal($pembayaran->tanggal_verifikasi, true); ?>
                </small>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('penagih/pelanggan/detail/' . $pembayaran->id_pelanggan); ?>" 
                       class="btn btn-info">
                        <i class="fas fa-user me-2"></i>Detail Pelanggan
                    </a>
                    <a href="<?php echo base_url('penagih/pembayaran'); ?>" 
                       class="btn btn-secondary">
                        <i class="fas fa-list me-2"></i>Daftar Pembayaran
                    </a>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>Print Detail
                    </button>
                </div>
            </div>
        </div>

        <!-- Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips Verifikasi</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info p-3">
                    <ul class="mb-0" style="font-size: 14px;">
                        <li>Periksa jumlah pembayaran dengan tagihan</li>
                        <li>Verifikasi metode pembayaran</li>
                        <li>Pastikan tanggal pembayaran sesuai</li>
                        <li>Berikan catatan jika ada yang perlu dicatat</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<?php $this->load->view('templates/footer'); ?>