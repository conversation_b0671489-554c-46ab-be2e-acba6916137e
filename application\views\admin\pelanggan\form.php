<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <?php echo ($action == 'add') ? 'Tambah Pelanggan' : 'Edit Pelanggan'; ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-<?php echo ($action == 'add') ? 'plus' : 'edit'; ?>"></i>
                    <?php echo ($action == 'add') ? 'Tambah Pelanggan Baru' : 'Edit Data Pelanggan'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open('', 'id="pelangganForm"'); ?>
                
                <div class="row">
                    <!-- No Pelanggan -->
                    <div class="col-md-6 mb-3">
                        <label for="no_pelanggan" class="form-label">No Pelanggan <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-id-card"></i>
                            </span>
                            <input type="text" class="form-control <?php echo form_error('no_pelanggan') ? 'is-invalid' : ''; ?>" 
                                   id="no_pelanggan" name="no_pelanggan" 
                                   value="<?php echo set_value('no_pelanggan', isset($pelanggan) ? $pelanggan->no_pelanggan : (isset($auto_no_pelanggan) ? $auto_no_pelanggan : '')); ?>"
                                   placeholder="Nomor pelanggan unik" required>
                            <?php if ($action == 'add'): ?>
                            <button type="button" class="btn btn-outline-secondary" id="generateNoPelanggan" title="Generate Otomatis">
                                <i class="fas fa-magic"></i>
                            </button>
                            <?php endif; ?>
                        </div>
                        <?php echo form_error('no_pelanggan', '<div class="invalid-feedback">', '</div>'); ?>
                        <div class="form-text">Nomor pelanggan harus unik dan tidak boleh sama</div>
                    </div>
                    
                    <!-- Nama Pelanggan -->
                    <div class="col-md-6 mb-3">
                        <label for="nama_pelanggan" class="form-label">Nama Pelanggan <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control <?php echo form_error('nama_pelanggan') ? 'is-invalid' : ''; ?>" 
                                   id="nama_pelanggan" name="nama_pelanggan" 
                                   value="<?php echo set_value('nama_pelanggan', isset($pelanggan) ? $pelanggan->nama_pelanggan : ''); ?>"
                                   placeholder="Nama lengkap pelanggan" required>
                        </div>
                        <?php echo form_error('nama_pelanggan', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                </div>
                
                <!-- Alamat -->
                <div class="mb-3">
                    <label for="alamat" class="form-label">Alamat Lengkap <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-map-marker-alt"></i>
                        </span>
                        <textarea class="form-control <?php echo form_error('alamat') ? 'is-invalid' : ''; ?>" 
                                  id="alamat" name="alamat" rows="3" 
                                  placeholder="Alamat lengkap dengan RT/RW, Kelurahan, Kecamatan" required><?php echo set_value('alamat', isset($pelanggan) ? $pelanggan->alamat : ''); ?></textarea>
                    </div>
                    <?php echo form_error('alamat', '<div class="invalid-feedback">', '</div>'); ?>
                </div>
                
                <div class="row">
                    <!-- No HP -->
                    <div class="col-md-6 mb-3">
                        <label for="no_hp" class="form-label">No HP/WhatsApp</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-phone"></i>
                            </span>
                            <input type="tel" class="form-control <?php echo form_error('no_hp') ? 'is-invalid' : ''; ?>" 
                                   id="no_hp" name="no_hp" 
                                   value="<?php echo set_value('no_hp', isset($pelanggan) ? $pelanggan->no_hp : ''); ?>"
                                   placeholder="08xxxxxxxxxx">
                        </div>
                        <?php echo form_error('no_hp', '<div class="invalid-feedback">', '</div>'); ?>
                        <div class="form-text">Format: 08xxxxxxxxxx</div>
                    </div>
                    
                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control <?php echo form_error('email') ? 'is-invalid' : ''; ?>" 
                                   id="email" name="email" 
                                   value="<?php echo set_value('email', isset($pelanggan) ? $pelanggan->email : ''); ?>"
                                   placeholder="<EMAIL>">
                        </div>
                        <?php echo form_error('email', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Tanggal Pemasangan -->
                    <div class="col-md-6 mb-3">
                        <label for="tanggal_pemasangan" class="form-label">Tanggal Pemasangan <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-calendar"></i>
                            </span>
                            <input type="date" class="form-control <?php echo form_error('tanggal_pemasangan') ? 'is-invalid' : ''; ?>" 
                                   id="tanggal_pemasangan" name="tanggal_pemasangan" 
                                   value="<?php echo set_value('tanggal_pemasangan', isset($pelanggan) ? $pelanggan->tanggal_pemasangan : date('Y-m-d')); ?>"
                                   required>
                        </div>
                        <?php echo form_error('tanggal_pemasangan', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                    
                    <!-- Status (hanya untuk edit) -->
                    <?php if ($action == 'edit'): ?>
                    <div class="col-md-6 mb-3">
                        <label for="status_langganan" class="form-label">Status Langganan <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-toggle-on"></i>
                            </span>
                            <select class="form-select <?php echo form_error('status_langganan') ? 'is-invalid' : ''; ?>" 
                                    id="status_langganan" name="status_langganan" required>
                                <option value="aktif" <?php echo set_select('status_langganan', 'aktif', isset($pelanggan) && $pelanggan->status_langganan == 'aktif'); ?>>
                                    Aktif
                                </option>
                                <option value="nonaktif" <?php echo set_select('status_langganan', 'nonaktif', isset($pelanggan) && $pelanggan->status_langganan == 'nonaktif'); ?>>
                                    Nonaktif
                                </option>
                                <option value="putus" <?php echo set_select('status_langganan', 'putus', isset($pelanggan) && $pelanggan->status_langganan == 'putus'); ?>>
                                    Putus
                                </option>
                            </select>
                        </div>
                        <?php echo form_error('status_langganan', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <hr>
                
                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <div>
                        <button type="reset" class="btn btn-outline-warning me-2">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            <?php echo ($action == 'add') ? 'Simpan' : 'Update'; ?>
                        </button>
                    </div>
                </div>
                
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Informasi
                </h6>
            </div>
            <div class="card-body">
                <h6>Status Langganan:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-success me-2">Aktif</span>
                        Pelanggan aktif dan dapat menggunakan layanan
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-warning me-2">Nonaktif</span>
                        Pelanggan sementara tidak aktif
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-danger me-2">Putus</span>
                        Langganan sudah dihentikan/putus
                    </li>
                </ul>
                
                <hr>
                
                <h6>Catatan Penting:</h6>
                <ul class="small text-muted">
                    <li>No Pelanggan harus unik</li>
                    <li>Nama pelanggan wajib diisi</li>
                    <li>Alamat harus lengkap dan jelas</li>
                    <li>No HP/Email opsional tapi direkomendasikan</li>
                    <li>Tanggal pemasangan tidak boleh kosong</li>
                </ul>
                
                <?php if ($action == 'edit' && isset($pelanggan)): ?>
                <hr>
                <h6>Info Pelanggan:</h6>
                <ul class="small">
                    <li><strong>Dibuat:</strong> <?php echo format_tanggal($pelanggan->created_at, 'd/m/Y H:i'); ?></li>
                    <li><strong>Diupdate:</strong> <?php echo format_tanggal($pelanggan->updated_at, 'd/m/Y H:i'); ?></li>
                    <li><strong>Status:</strong> 
                        <span class="badge bg-<?php echo ($pelanggan->status_langganan == 'aktif') ? 'success' : (($pelanggan->status_langganan == 'nonaktif') ? 'warning' : 'danger'); ?>">
                            <?php echo ucfirst($pelanggan->status_langganan); ?>
                        </span>
                    </li>
                </ul>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if ($action == 'edit' && isset($pelanggan)): ?>
                    <a href="<?php echo base_url('admin/pelanggan/detail/' . $pelanggan->id); ?>" 
                       class="btn btn-outline-info btn-sm">
                        <i class="fas fa-eye"></i> Lihat Detail
                    </a>
                    <a href="<?php echo base_url('admin/pelanggan/toggle_status/' . $pelanggan->id); ?>" 
                       class="btn btn-outline-warning btn-sm"
                       onclick="return confirm('Yakin ingin mengubah status pelanggan ini?')">
                        <i class="fas fa-toggle-on"></i> Toggle Status
                    </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo base_url('admin/pelanggan/add'); ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus"></i> Tambah Pelanggan Lain
                    </a>
                    <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list"></i> Lihat Semua Pelanggan
                    </a>
                    <a href="<?php echo base_url('admin/pelanggan/import'); ?>" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-upload"></i> Import Excel
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Preview Card -->
        <?php if ($action == 'add'): ?>
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye"></i> Preview
                </h6>
            </div>
            <div class="card-body">
                <div id="previewCard" class="text-center">
                    <div class="avatar bg-primary text-white rounded-circle mx-auto mb-2" style="width: 50px; height: 50px; line-height: 50px; font-size: 20px;">
                        <span id="previewInitial">?</span>
                    </div>
                    <h6 id="previewNama" class="mb-1 text-muted">Nama Pelanggan</h6>
                    <small id="previewNo" class="text-muted">No Pelanggan</small>
                    <hr>
                    <small id="previewAlamat" class="text-muted">Alamat pelanggan</small>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
// Auto-format no HP
document.getElementById('no_hp').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.startsWith('0')) {
        // Keep Indonesian format
        this.value = value;
    } else if (value.startsWith('62')) {
        // Convert international to local
        this.value = '0' + value.substring(2);
    }
});

// Generate no pelanggan otomatis
<?php if ($action == 'add'): ?>
document.getElementById('generateNoPelanggan').addEventListener('click', function() {
    const year = new Date().getFullYear();
    const random = Math.floor(Math.random() * 999999) + 100000;
    document.getElementById('no_pelanggan').value = 'PLG' + year + random.toString().padStart(6, '0');
    updatePreview();
});
<?php endif; ?>

// Form validation
document.getElementById('pelangganForm').addEventListener('submit', function(e) {
    const noPelanggan = document.getElementById('no_pelanggan').value;
    const namaPelanggan = document.getElementById('nama_pelanggan').value;
    const alamat = document.getElementById('alamat').value;
    
    if (!noPelanggan || !namaPelanggan || !alamat) {
        e.preventDefault();
        alert('Mohon isi semua field yang wajib diisi!');
        return false;
    }
    
    // Validate no pelanggan format
    if (!/^PLG\d{10}$/.test(noPelanggan)) {
        e.preventDefault();
        alert('Format nomor pelanggan tidak valid! (Contoh: PLG2024123456)');
        return false;
    }
});

// Auto-capitalize nama
document.getElementById('nama_pelanggan').addEventListener('input', function() {
    this.value = this.value.replace(/\b\w/g, function(l) {
        return l.toUpperCase();
    });
    updatePreview();
});

// Real-time preview (untuk add)
<?php if ($action == 'add'): ?>
function updatePreview() {
    const nama = document.getElementById('nama_pelanggan').value || 'Nama Pelanggan';
    const no = document.getElementById('no_pelanggan').value || 'No Pelanggan';
    const alamat = document.getElementById('alamat').value || 'Alamat pelanggan';
    
    document.getElementById('previewNama').textContent = nama;
    document.getElementById('previewNo').textContent = no;
    document.getElementById('previewAlamat').textContent = alamat.substring(0, 50) + (alamat.length > 50 ? '...' : '');
    document.getElementById('previewInitial').textContent = nama.charAt(0).toUpperCase() || '?';
}

// Update preview on input
document.getElementById('nama_pelanggan').addEventListener('input', updatePreview);
document.getElementById('no_pelanggan').addEventListener('input', updatePreview);
document.getElementById('alamat').addEventListener('input', updatePreview);

// Initial preview update
updatePreview();
<?php endif; ?>

// Auto-save draft (localStorage)
function saveDraft() {
    if (typeof(Storage) !== "undefined") {
        const formData = {
            no_pelanggan: document.getElementById('no_pelanggan').value,
            nama_pelanggan: document.getElementById('nama_pelanggan').value,
            alamat: document.getElementById('alamat').value,
            no_hp: document.getElementById('no_hp').value,
            email: document.getElementById('email').value,
            tanggal_pemasangan: document.getElementById('tanggal_pemasangan').value
        };
        localStorage.setItem('pelanggan_draft', JSON.stringify(formData));
    }
}

// Load draft
function loadDraft() {
    if (typeof(Storage) !== "undefined" && localStorage.getItem('pelanggan_draft')) {
        const formData = JSON.parse(localStorage.getItem('pelanggan_draft'));
        if (confirm('Ada draft data yang tersimpan. Ingin memuat draft tersebut?')) {
            Object.keys(formData).forEach(key => {
                const element = document.getElementById(key);
                if (element && !element.value) {
                    element.value = formData[key];
                }
            });
            <?php if ($action == 'add'): ?>
            updatePreview();
            <?php endif; ?>
        }
    }
}

// Clear draft when form submitted successfully
document.getElementById('pelangganForm').addEventListener('submit', function() {
    localStorage.removeItem('pelanggan_draft');
});

// Auto-save every 30 seconds
<?php if ($action == 'add'): ?>
setInterval(saveDraft, 30000);

// Load draft on page load
window.addEventListener('load', loadDraft);
<?php endif; ?>

// Validate email format
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        this.classList.add('is-invalid');
        this.setCustomValidity('Format email tidak valid');
    } else {
        this.classList.remove('is-invalid');
        this.setCustomValidity('');
    }
});
</script>

<?php $this->load->view('templates/footer'); ?>