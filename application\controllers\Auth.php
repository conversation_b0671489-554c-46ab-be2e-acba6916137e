<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Auth extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->load->model('User_model');
    }

    /**
     * Default method - redirect to login
     */
    public function index()
    {
        if ($this->auth_lib->is_logged_in()) {
            redirect('dashboard');
        } else {
            redirect('auth/login');
        }
    }

    /**
     * Login page
     */
    public function login()
    {
        // Jika sudah login, redirect ke dashboard
        if ($this->auth_lib->is_logged_in()) {
            redirect('dashboard');
        }

        // Jika form disubmit
        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Username', 'required|trim');
            $this->form_validation->set_rules('password', 'Password', 'required');

            if ($this->form_validation->run() == TRUE) {
                $username = $this->input->post('username');
                $password = $this->input->post('password');

                $login_result = $this->auth_lib->login($username, $password);

                if ($login_result === TRUE) {
                    set_flash_message('success', 'Login berhasil! Selamat datang ' . $this->auth_lib->get_user_data('nama_lengkap'));
                    redirect('dashboard');
                } else {
                    set_flash_message('error', $login_result);
                    redirect('auth/login');
                }
            }
        }

        $data['title'] = 'Login - PDAM System';
        $this->load->view('auth/login', $data);
    }

    /**
     * Logout
     */
    public function logout()
    {
        $this->auth_lib->logout();
        set_flash_message('info', 'Anda telah logout dari sistem');
        redirect('auth/login');
    }

    /**
     * Register (hanya untuk admin)
     */
    public function register()
    {
        // Hanya admin yang bisa mengakses
        $this->auth_lib->require_role('admin');

        if ($this->input->post()) {
            $this->form_validation->set_rules('username', 'Username', 'required|trim|is_unique[users.username]');
            $this->form_validation->set_rules('email', 'Email', 'required|trim|valid_email|is_unique[users.email]');
            $this->form_validation->set_rules('password', 'Password', 'required|min_length[6]');
            $this->form_validation->set_rules('confirm_password', 'Konfirmasi Password', 'required|matches[password]');
            $this->form_validation->set_rules('nama_lengkap', 'Nama Lengkap', 'required|trim');
            $this->form_validation->set_rules('role', 'Role', 'required|in_list[admin,penagih,pimpinan]');

            if ($this->form_validation->run() == TRUE) {
                $data = array(
                    'username' => $this->input->post('username'),
                    'email' => $this->input->post('email'),
                    'password' => $this->auth_lib->hash_password($this->input->post('password')),
                    'nama_lengkap' => $this->input->post('nama_lengkap'),
                    'no_hp' => $this->input->post('no_hp'),
                    'alamat' => $this->input->post('alamat'),
                    'role' => $this->input->post('role'),
                    'status' => 'aktif'
                );

                if ($this->User_model->insert($data)) {
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Menambah user baru: ' . $data['username'],
                        'users'
                    );
                    set_flash_message('success', 'User berhasil ditambahkan');
                } else {
                    set_flash_message('error', 'Gagal menambahkan user');
                }
                redirect('admin/users');
            }
        }

        $data['title'] = 'Tambah User - PDAM System';
        $this->load->view('admin/users/form', $data);
    }
}