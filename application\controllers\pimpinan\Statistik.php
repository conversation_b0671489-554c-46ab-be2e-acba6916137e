<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Statistik extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->helper(['format', 'flash_message']);
        
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
        
        if ($this->session->userdata('role') !== 'pimpinan') {
            redirect('dashboard');
        }
    }

    public function index()
    {
        // Query database real
        $data['title'] = 'Statistik PDAM';
        $data['total_pelanggan'] = $this->db->count_all('pelanggan');
        
        $this->db->where('status_langganan', 'aktif');
        $data['pelanggan_aktif'] = $this->db->count_all_results('pelanggan');

        // Total pembayaran verified tahun ini
        $this->db->select('COUNT(*) as total, SUM(jumlah_bayar) as nilai');
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->where('penggunaan_air.tahun', date('Y'));
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        $result = $this->db->get('pembayaran')->row();
        
        $data['total_pembayaran_tahun_ini'] = $result ? $result->total : 0;
        $data['total_nilai_pembayaran'] = $result ? $result->nilai : 0;
        $data['rata_rata_pembayaran'] = $data['pelanggan_aktif'] > 0 ? $data['total_nilai_pembayaran'] / $data['pelanggan_aktif'] : 0;

        // Grafik bulanan
        $data['grafik_bulanan'] = [];
        $bulan_nama = ['', 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
        
        for($i = 1; $i <= 12; $i++) {
            $this->db->select('COUNT(*) as total, COALESCE(SUM(jumlah_bayar), 0) as nilai');
            $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
            $this->db->where('penggunaan_air.bulan', $i);
            $this->db->where('penggunaan_air.tahun', date('Y'));
            $this->db->where('pembayaran.status_verifikasi', 'verified');
            $bulan_result = $this->db->get('pembayaran')->row();
            
            $data['grafik_bulanan'][] = [
                'bulan' => $bulan_nama[$i],
                'total' => $bulan_result ? $bulan_result->total : 0,
                'nilai' => $bulan_result ? $bulan_result->nilai : 0
            ];
        }

        // Metode pembayaran
        $this->db->select('metode_bayar, COUNT(*) as total');
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        $this->db->where('penggunaan_air.tahun', date('Y'));
        $this->db->group_by('metode_bayar');
        $data['metode_pembayaran'] = $this->db->get('pembayaran')->result();

        // Top pelanggan
        $this->db->select('pelanggan.nama_pelanggan, COUNT(*) as total_bayar, SUM(pembayaran.jumlah_bayar) as total_nilai');
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        $this->db->where('penggunaan_air.tahun', date('Y'));
        $this->db->group_by('pelanggan.id');
        $this->db->order_by('total_nilai', 'DESC');
        $this->db->limit(5);
        $data['top_pelanggan'] = $this->db->get('pembayaran')->result();

        $this->load->view('templates/header', $data);
        $this->load->view('templates/sidebar');
        $this->load->view('pimpinan/statistik/index', $data);
        $this->load->view('templates/footer');
    }
}