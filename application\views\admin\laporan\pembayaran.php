<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i>Laporan Pembayaran</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/laporan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo base_url('admin/laporan/export/pembayaran?bulan=' . $bulan_filter . '&tahun=' . $tahun_filter . '&status=' . $status_filter); ?>" 
               class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Export CSV
            </a>
            <button onclick="window.print()" class="btn btn-sm btn-info">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Laporan</h5>
    </div>
    <div class="card-body">
        <?php echo form_open('', ['method' => 'GET', 'class' => 'row g-3']); ?>
        
        <div class="col-md-2">
            <label for="bulan" class="form-label">Bulan</label>
            <select class="form-select" id="bulan" name="bulan">
                <?php for ($i = 1; $i <= 12; $i++): ?>
                    <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>" 
                            <?php echo ($bulan_filter == str_pad($i, 2, '0', STR_PAD_LEFT)) ? 'selected' : ''; ?>>
                        <?php echo nama_bulan($i); ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-2">
            <label for="tahun" class="form-label">Tahun</label>
            <select class="form-select" id="tahun" name="tahun">
                <?php for ($i = date('Y') - 2; $i <= date('Y') + 1; $i++): ?>
                    <option value="<?php echo $i; ?>" <?php echo ($tahun_filter == $i) ? 'selected' : ''; ?>>
                        <?php echo $i; ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-2">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="all" <?php echo ($status_filter == 'all') ? 'selected' : ''; ?>>Semua</option>
                <option value="verified" <?php echo ($status_filter == 'verified') ? 'selected' : ''; ?>>Verified</option>
                <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>Pending</option>
                <option value="rejected" <?php echo ($status_filter == 'rejected') ? 'selected' : ''; ?>>Rejected</option>
            </select>
        </div>
        
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
            </div>
        </div>
        
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <a href="<?php echo base_url('admin/laporan/pembayaran?bulan=' . date('m') . '&tahun=' . date('Y') . '&status=verified'); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-sync"></i> Reset
                </a>
            </div>
        </div>
        
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Record</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_records); ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Terbayar</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($total_terbayar); ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Tagihan</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($total_tagihan); ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Status Filter</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo ucfirst($status_filter); ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Data Pembayaran - <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?>
            <span class="badge bg-secondary"><?php echo ucfirst($status_filter); ?></span>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($pembayaran)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th width="50">No</th>
                        <th>No. Kwitansi</th>
                        <th>Pelanggan</th>
                        <th>Total Tagihan</th>
                        <th>Jumlah Bayar</th>
                        <th>Selisih</th>
                        <th>Metode</th>
                        <th>Tanggal Bayar</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pembayaran as $index => $p): ?>
                    <?php $selisih = $p->jumlah_bayar - $p->total_tagihan; ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><strong class="text-primary"><?php echo $p->no_kwitansi; ?></strong></td>
                        <td>
                            <strong><?php echo $p->nama_pelanggan; ?></strong><br>
                            <small class="text-muted"><?php echo $p->no_pelanggan; ?></small>
                        </td>
                        <td class="text-end"><?php echo format_rupiah($p->total_tagihan); ?></td>
                        <td class="text-end">
                            <strong class="text-success"><?php echo format_rupiah($p->jumlah_bayar); ?></strong>
                        </td>
                        <td class="text-end">
                            <?php if ($selisih > 0): ?>
                                <span class="text-success">+<?php echo format_rupiah($selisih); ?></span>
                            <?php elseif ($selisih < 0): ?>
                                <span class="text-danger"><?php echo format_rupiah($selisih); ?></span>
                            <?php else: ?>
                                <span class="text-muted">Rp 0</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $metode_class = '';
                            switch ($p->metode_bayar) {
                                case 'tunai': $metode_class = 'success'; break;
                                case 'transfer': $metode_class = 'primary'; break;
                                case 'qris': $metode_class = 'info'; break;
                                case 'debit': $metode_class = 'warning'; break;
                                default: $metode_class = 'secondary'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $metode_class; ?>"><?php echo ucfirst($p->metode_bayar); ?></span>
                        </td>
                        <td><?php echo date('d/m/Y', strtotime($p->tanggal_bayar)); ?></td>
                        <td>
                            <?php
                            $status_class = '';
                            switch ($p->status_verifikasi) {
                                case 'pending': $status_class = 'warning'; break;
                                case 'verified': $status_class = 'success'; break;
                                case 'rejected': $status_class = 'danger'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $status_class; ?>"><?php echo ucfirst($p->status_verifikasi); ?></span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-secondary">
                    <tr>
                        <th colspan="3" class="text-end">TOTAL:</th>
                        <th class="text-end"><?php echo format_rupiah($total_tagihan); ?></th>
                        <th class="text-end">
                            <strong class="text-success"><?php echo format_rupiah($total_terbayar); ?></strong>
                        </th>
                        <th class="text-end">
                            <?php $total_selisih = $total_terbayar - $total_tagihan; ?>
                            <?php if ($total_selisih > 0): ?>
                                <strong class="text-success">+<?php echo format_rupiah($total_selisih); ?></strong>
                            <?php elseif ($total_selisih < 0): ?>
                                <strong class="text-danger"><?php echo format_rupiah($total_selisih); ?></strong>
                            <?php else: ?>
                                <strong class="text-muted">Rp 0</strong>
                            <?php endif; ?>
                        </th>
                        <th colspan="3"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada data pembayaran</h5>
            <p class="text-muted">Belum ada data pembayaran untuk periode dan filter yang dipilih</p>
            <a href="<?php echo base_url('admin/pembayaran/add'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Input Pembayaran
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table {
        font-size: 12px !important;
    }
    .badge {
        background-color: #6c757d !important;
        color: white !important;
    }
}
</style>

<?php $this->load->view('templates/footer'); ?>
