<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON>unaan extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Pelanggan_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Tarif_model');
        $this->load->helper(['form', 'url', 'date']);
        $this->load->library(['form_validation', 'session']);
        
        // Check login
        if (!$this->session->userdata('logged_in')) {
            redirect('auth/login');
        }
        
        // Check role penagih
        if ($this->session->userdata('role') !== 'penagih') {
            redirect('dashboard');
        }
    }

    public function index()
    {
        $data['title'] = 'Data Penggunaan Air';
        
        // Filter
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        $search = $this->input->get('search');
        
        // Get data penggunaan dengan join
        $this->db->select('pa.*, p.nama_pelanggan, p.no_pelanggan, p.alamat, t.nama_tarif, u.nama_lengkap as petugas');
        $this->db->from('penggunaan_air pa');
        $this->db->join('pelanggan p', 'p.id = pa.id_pelanggan');
        $this->db->join('tarif t', 't.id = pa.id_tarif');
        $this->db->join('users u', 'u.id = pa.petugas_catat');
        $this->db->where('pa.bulan', $bulan);
        $this->db->where('pa.tahun', $tahun);
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('p.nama_pelanggan', $search);
            $this->db->or_like('p.no_pelanggan', $search);
            $this->db->group_end();
        }
        
        $this->db->order_by('pa.tanggal_catat', 'DESC');
        $data['penggunaan'] = $this->db->get()->result();
        
        $data['bulan'] = $bulan;
        $data['tahun'] = $tahun;
        $data['search'] = $search;
        
        // Stats
        $this->db->where('bulan', $bulan);
        $this->db->where('tahun', $tahun);
        $data['total_dicatat'] = $this->db->count_all_results('penggunaan_air');
        
        $this->db->where('status_langganan', 'aktif');
        $data['total_pelanggan'] = $this->db->count_all_results('pelanggan');
        
        $this->load->view('penagih/penggunaan/index', $data);
    }

    public function add()
    {
        $data['title'] = 'Input Penggunaan Air';
        
        // Get pelanggan yang belum dicatat bulan ini
        $bulan = date('m');
        $tahun = date('Y');
        
        // Query dengan LEFT JOIN untuk mencari yang belum dicatat
        $this->db->select('p.*');
        $this->db->from('pelanggan p');
        $this->db->join('penggunaan_air pa', "pa.id_pelanggan = p.id AND pa.bulan = {$bulan} AND pa.tahun = {$tahun}", 'left');
        $this->db->where('p.status_langganan', 'aktif');
        $this->db->where('pa.id IS NULL');
        $this->db->order_by('p.nama_pelanggan', 'ASC');
        $data['pelanggan_list'] = $this->db->get()->result();
        
        $data['tarif_list'] = $this->Tarif_model->get_all();
        
        // Pre-select pelanggan from URL
        $selected_pelanggan = $this->input->get('pelanggan');
        $data['selected_pelanggan'] = $selected_pelanggan;
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('id_pelanggan', 'Pelanggan', 'required');
            $this->form_validation->set_rules('meter_akhir', 'Meter Akhir', 'required|numeric');
            $this->form_validation->set_rules('id_tarif', 'Tarif', 'required');
            
            if ($this->form_validation->run() == TRUE) {
                $id_pelanggan = $this->input->post('id_pelanggan');
                $meter_akhir = $this->input->post('meter_akhir');
                $id_tarif = $this->input->post('id_tarif');
                
                // Cek apakah sudah ada pencatatan bulan ini
                $this->db->where([
                    'id_pelanggan' => $id_pelanggan,
                    'bulan' => $bulan,
                    'tahun' => $tahun
                ]);
                $existing = $this->db->get('penggunaan_air')->row();
                
                if ($existing) {
                    $this->session->set_flashdata('error', 'Pelanggan ini sudah dicatat untuk bulan ' . nama_bulan($bulan) . ' ' . $tahun);
                } else {
                    // Get meter awal (dari penggunaan terakhir atau 0)
                    $this->db->select('meter_akhir');
                    $this->db->from('penggunaan_air');
                    $this->db->where('id_pelanggan', $id_pelanggan);
                    $this->db->order_by('tahun DESC, bulan DESC');
                    $this->db->limit(1);
                    $last_meter = $this->db->get()->row();
                    $meter_awal = $last_meter ? $last_meter->meter_akhir : 0;
                    
                    $pemakaian = $meter_akhir - $meter_awal;
                    
                    if ($pemakaian < 0) {
                        $this->session->set_flashdata('error', 'Meter akhir (' . number_format($meter_akhir) . ') tidak boleh lebih kecil dari meter awal (' . number_format($meter_awal) . ')');
                    } else {
                        // Calculate total tagihan
                        $tarif = $this->Tarif_model->get_by_id($id_tarif);
                        $pemakaian_tagihan = max($pemakaian, $tarif->batas_minimum);
                        $total_tagihan = $pemakaian_tagihan * $tarif->tarif_per_m3 + $tarif->biaya_admin;
                        
                        $penggunaan_data = [
                            'id_pelanggan' => $id_pelanggan,
                            'bulan' => $bulan,
                            'tahun' => $tahun,
                            'meter_awal' => $meter_awal,
                            'meter_akhir' => $meter_akhir,
                            'pemakaian' => $pemakaian,
                            'id_tarif' => $id_tarif,
                            'total_tagihan' => $total_tagihan,
                            'tanggal_catat' => date('Y-m-d'),
                            'petugas_catat' => $this->session->userdata('user_id')
                        ];
                        
                        if ($this->db->insert('penggunaan_air', $penggunaan_data)) {
                            // Log aktivitas
                            $pelanggan = $this->Pelanggan_model->get_by_id($id_pelanggan);
                            $insert_id = $this->db->insert_id();
                            
                            $log_data = [
                                'id_user' => $this->session->userdata('user_id'),
                                'aktivitas' => 'Mencatat penggunaan air untuk pelanggan: ' . $pelanggan->nama_pelanggan . ' (' . $pelanggan->no_pelanggan . ') - ' . nama_bulan($bulan) . ' ' . $tahun,
                                'tabel_terkait' => 'penggunaan_air',
                                'id_record' => $insert_id,
                                'ip_address' => $this->input->ip_address(),
                                'user_agent' => $this->input->user_agent()
                            ];
                            $this->db->insert('log_aktivitas', $log_data);
                            
                            $this->session->set_flashdata('success', 'Data penggunaan berhasil dicatat untuk ' . $pelanggan->nama_pelanggan . ' - ' . nama_bulan($bulan) . ' ' . $tahun);
                            redirect('penagih/penggunaan');
                        } else {
                            $this->session->set_flashdata('error', 'Gagal menyimpan data penggunaan.');
                        }
                    }
                }
            }
        }
        
        $this->load->view('penagih/penggunaan/add', $data);
    }

    // AJAX: Get meter terakhir
    public function get_meter_terakhir()
    {
        $id_pelanggan = $this->input->post('id_pelanggan');
        
        $this->db->select('meter_akhir, bulan, tahun');
        $this->db->from('penggunaan_air');
        $this->db->where('id_pelanggan', $id_pelanggan);
        $this->db->order_by('tahun DESC, bulan DESC');
        $this->db->limit(1);
        $last_meter = $this->db->get()->row();
        
        $meter_terakhir = $last_meter ? $last_meter->meter_akhir : 0;
        $periode_terakhir = $last_meter ? nama_bulan($last_meter->bulan) . ' ' . $last_meter->tahun : 'Belum ada data';
        
        echo json_encode([
            'meter_terakhir' => $meter_terakhir,
            'periode_terakhir' => $periode_terakhir,
            'formatted_meter' => number_format($meter_terakhir)
        ]);
    }

    // AJAX: Calculate tagihan
    public function calculate_tagihan()
    {
        $meter_awal = (int)$this->input->post('meter_awal');
        $meter_akhir = (int)$this->input->post('meter_akhir');
        $id_tarif = $this->input->post('id_tarif');

        // Validasi input
        if (!$id_tarif || $meter_akhir <= 0) {
            echo json_encode(['success' => false, 'message' => 'Data tidak lengkap']);
            return;
        }

        $pemakaian = $meter_akhir - $meter_awal;

        if ($pemakaian < 0) {
            echo json_encode(['success' => false, 'message' => 'Meter akhir tidak boleh lebih kecil dari meter awal']);
            return;
        }

        $tarif = $this->Tarif_model->get_by_id($id_tarif);

        if (!$tarif) {
            echo json_encode(['success' => false, 'message' => 'Tarif tidak ditemukan']);
            return;
        }

        $pemakaian_tagihan = max($pemakaian, $tarif->batas_minimum);
        $biaya_pemakaian = $pemakaian_tagihan * $tarif->tarif_per_m3;
        $total_tagihan = $biaya_pemakaian + $tarif->biaya_admin;
        
        echo json_encode([
            'success' => true,
            'pemakaian' => $pemakaian,
            'pemakaian_tagihan' => $pemakaian_tagihan,
            'biaya_pemakaian' => $biaya_pemakaian,
            'biaya_admin' => $tarif->biaya_admin,
            'total_tagihan' => $total_tagihan,
            'formatted_pemakaian' => number_format($pemakaian),
            'formatted_biaya_pemakaian' => format_rupiah($biaya_pemakaian),
            'formatted_biaya_admin' => format_rupiah($tarif->biaya_admin),
            'formatted_total_tagihan' => format_rupiah($total_tagihan),
            'tarif_info' => $tarif->nama_tarif . ' - ' . format_rupiah($tarif->tarif_per_m3) . '/m³'
        ]);
    }

    public function edit($id)
    {
        $data['title'] = 'Edit Penggunaan Air';
        
        // Get data penggunaan
        $this->db->select('pa.*, p.nama_pelanggan, p.no_pelanggan');
        $this->db->from('penggunaan_air pa');
        $this->db->join('pelanggan p', 'p.id = pa.id_pelanggan');
        $this->db->where('pa.id', $id);
        $data['penggunaan'] = $this->db->get()->row();
        
        if (!$data['penggunaan']) {
            $this->session->set_flashdata('error', 'Data penggunaan tidak ditemukan.');
            redirect('penagih/penggunaan');
        }
        
        $data['tarif_list'] = $this->Tarif_model->get_all();
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('meter_akhir', 'Meter Akhir', 'required|numeric');
            $this->form_validation->set_rules('id_tarif', 'Tarif', 'required');
            
            if ($this->form_validation->run() == TRUE) {
                $meter_akhir = $this->input->post('meter_akhir');
                $id_tarif = $this->input->post('id_tarif');
                $meter_awal = $data['penggunaan']->meter_awal;
                
                $pemakaian = $meter_akhir - $meter_awal;
                
                if ($pemakaian < 0) {
                    $this->session->set_flashdata('error', 'Meter akhir tidak boleh lebih kecil dari meter awal (' . number_format($meter_awal) . ')');
                } else {
                    // Calculate total tagihan
                    $tarif = $this->Tarif_model->get_by_id($id_tarif);
                    $pemakaian_tagihan = max($pemakaian, $tarif->batas_minimum);
                    $total_tagihan = $pemakaian_tagihan * $tarif->tarif_per_m3 + $tarif->biaya_admin;
                    
                    $update_data = [
                        'meter_akhir' => $meter_akhir,
                        'pemakaian' => $pemakaian,
                        'id_tarif' => $id_tarif,
                        'total_tagihan' => $total_tagihan,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if ($this->db->where('id', $id)->update('penggunaan_air', $update_data)) {
                        // Log aktivitas
                        $log_data = [
                            'id_user' => $this->session->userdata('user_id'),
                            'aktivitas' => 'Mengupdate penggunaan air: ' . $data['penggunaan']->nama_pelanggan . ' (' . $data['penggunaan']->no_pelanggan . ') - ' . nama_bulan($data['penggunaan']->bulan) . ' ' . $data['penggunaan']->tahun,
                            'tabel_terkait' => 'penggunaan_air',
                            'id_record' => $id,
                            'ip_address' => $this->input->ip_address(),
                            'user_agent' => $this->input->user_agent()
                        ];
                        $this->db->insert('log_aktivitas', $log_data);
                        
                        $this->session->set_flashdata('success', 'Data penggunaan berhasil diupdate.');
                        redirect('penagih/penggunaan');
                    } else {
                        $this->session->set_flashdata('error', 'Gagal mengupdate data penggunaan.');
                    }
                }
            }
        }
        
        $this->load->view('penagih/penggunaan/edit', $data);
    }
}