<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Laporan extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_role('admin'); // <PERSON>ya admin yang bisa akses
        
        $this->load->model(['Pelanggan_model', 'Penggunaan_model', 'Pembayaran_model', 'Tarif_model']);
        $this->load->helper(['form', 'url', 'date']);
        $this->load->library(['form_validation', 'session']);
    }

    /**
     * Halaman utama laporan
     */
    public function index()
    {
        $data['title'] = 'Laporan - PDAM System';
        
        // Default periode
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        
        $data['bulan_filter'] = $bulan;
        $data['tahun_filter'] = $tahun;
        
        // Summary Statistics dengan query langsung
        try {
            // Total pelanggan
            $data['total_pelanggan'] = $this->db->count_all('pelanggan');

            // Pelanggan aktif
            $this->db->where('status', 'aktif');
            $data['pelanggan_aktif'] = $this->db->count_all_results('pelanggan');

            // Penggunaan bulan ini
            $this->db->where('bulan', $bulan);
            $this->db->where('tahun', $tahun);
            $data['total_penggunaan'] = $this->db->count_all_results('penggunaan_air');

            // Total pemakaian bulan ini
            $this->db->select_sum('pemakaian');
            $this->db->where('bulan', $bulan);
            $this->db->where('tahun', $tahun);
            $result = $this->db->get('penggunaan_air')->row();
            $data['total_pemakaian'] = $result->pemakaian ?: 0;

            // Total tagihan bulan ini
            $this->db->select_sum('total_tagihan');
            $this->db->where('bulan', $bulan);
            $this->db->where('tahun', $tahun);
            $result = $this->db->get('penggunaan_air')->row();
            $data['total_tagihan'] = $result->total_tagihan ?: 0;

            // Pembayaran verified bulan ini
            $this->db->select('COUNT(*) as total');
            $this->db->from('pembayaran p');
            $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->where('p.status_verifikasi', 'verified');
            $result = $this->db->get()->row();
            $data['total_pembayaran'] = $result->total ?: 0;

            // Total terbayar bulan ini
            $this->db->select('SUM(p.jumlah_bayar) as total_bayar');
            $this->db->from('pembayaran p');
            $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->where('p.status_verifikasi', 'verified');
            $result = $this->db->get()->row();
            $data['total_terbayar'] = $result->total_bayar ?: 0;

            // Sisa piutang
            $data['sisa_piutang'] = $data['total_tagihan'] - $data['total_terbayar'];

        } catch (Exception $e) {
            log_message('error', 'Error in laporan statistics: ' . $e->getMessage());
            // Set default values
            $data['total_pelanggan'] = 0;
            $data['pelanggan_aktif'] = 0;
            $data['total_penggunaan'] = 0;
            $data['total_pemakaian'] = 0;
            $data['total_pembayaran'] = 0;
            $data['total_terbayar'] = 0;
            $data['total_tagihan'] = 0;
            $data['sisa_piutang'] = 0;
        }
        
        $this->load->view('admin/laporan/index', $data);
    }

    /**
     * Laporan Penggunaan Air
     */
    public function penggunaan()
    {
        $data['title'] = 'Laporan Penggunaan Air';
        
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        
        $data['bulan_filter'] = $bulan;
        $data['tahun_filter'] = $tahun;
        
        // Get data penggunaan
        $this->db->select('pa.*, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat, t.nama_tarif');
        $this->db->from('penggunaan_air pa');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        $this->db->join('tarif t', 't.id = pa.id_tarif');
        $this->db->where('pa.bulan', $bulan);
        $this->db->where('pa.tahun', $tahun);
        $this->db->order_by('pel.nama_pelanggan', 'ASC');
        $data['penggunaan'] = $this->db->get()->result();
        
        // Summary
        $data['total_records'] = count($data['penggunaan']);
        $data['total_pemakaian'] = array_sum(array_column($data['penggunaan'], 'pemakaian'));
        $data['total_tagihan'] = array_sum(array_column($data['penggunaan'], 'total_tagihan'));
        
        $this->load->view('admin/laporan/penggunaan', $data);
    }

    /**
     * Laporan Pembayaran
     */
    public function pembayaran()
    {
        $data['title'] = 'Laporan Pembayaran';
        
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        $status = $this->input->get('status') ?: 'verified';
        
        $data['bulan_filter'] = $bulan;
        $data['tahun_filter'] = $tahun;
        $data['status_filter'] = $status;
        
        // Get data pembayaran
        $this->db->select('p.*, pa.bulan, pa.tahun, pa.total_tagihan, pa.pemakaian, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat');
        $this->db->from('pembayaran p');
        $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        $this->db->where('pa.bulan', $bulan);
        $this->db->where('pa.tahun', $tahun);
        if ($status != 'all') {
            $this->db->where('p.status_verifikasi', $status);
        }
        $this->db->order_by('p.tanggal_bayar', 'ASC');
        $data['pembayaran'] = $this->db->get()->result();
        
        // Summary
        $data['total_records'] = count($data['pembayaran']);
        $data['total_terbayar'] = array_sum(array_column($data['pembayaran'], 'jumlah_bayar'));
        $data['total_tagihan'] = array_sum(array_column($data['pembayaran'], 'total_tagihan'));
        
        $this->load->view('admin/laporan/pembayaran', $data);
    }

    /**
     * Laporan Pelanggan
     */
    public function pelanggan()
    {
        $data['title'] = 'Laporan Data Pelanggan';
        
        $status = $this->input->get('status') ?: 'all';
        $search = $this->input->get('search');
        
        $data['status_filter'] = $status;
        $data['search'] = $search;
        
        // Get data pelanggan
        $this->db->select('pel.*, COUNT(pa.id) as total_penggunaan, SUM(pa.total_tagihan) as total_tagihan, SUM(pb.jumlah_bayar) as total_terbayar');
        $this->db->from('pelanggan pel');
        $this->db->join('penggunaan_air pa', 'pa.id_pelanggan = pel.id', 'LEFT');
        $this->db->join('pembayaran pb', 'pb.id_penggunaan = pa.id AND pb.status_verifikasi = "verified"', 'LEFT');
        
        if ($status != 'all') {
            $this->db->where('pel.status', $status);
        }
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('pel.nama_pelanggan', $search);
            $this->db->or_like('pel.no_pelanggan', $search);
            $this->db->or_like('pel.alamat', $search);
            $this->db->group_end();
        }
        
        $this->db->group_by('pel.id');
        $this->db->order_by('pel.nama_pelanggan', 'ASC');
        $data['pelanggan'] = $this->db->get()->result();
        
        // Summary
        $data['total_records'] = count($data['pelanggan']);
        $data['total_aktif'] = count(array_filter($data['pelanggan'], function($p) { return $p->status == 'aktif'; }));
        $data['total_nonaktif'] = count(array_filter($data['pelanggan'], function($p) { return $p->status == 'nonaktif'; }));
        
        $this->load->view('admin/laporan/pelanggan', $data);
    }

    /**
     * Export laporan ke Excel (sederhana)
     */
    public function export($type = 'penggunaan')
    {
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        
        // Set headers untuk download
        $filename = 'laporan_' . $type . '_' . $bulan . '_' . $tahun . '.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        if ($type == 'penggunaan') {
            // Header CSV
            fputcsv($output, ['No', 'No Pelanggan', 'Nama Pelanggan', 'Alamat', 'Periode', 'Meter Awal', 'Meter Akhir', 'Pemakaian', 'Tarif', 'Total Tagihan']);
            
            // Data
            $this->db->select('pa.*, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat, t.nama_tarif');
            $this->db->from('penggunaan_air pa');
            $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
            $this->db->join('tarif t', 't.id = pa.id_tarif');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->order_by('pel.nama_pelanggan', 'ASC');
            $penggunaan = $this->db->get()->result();
            
            $no = 1;
            foreach ($penggunaan as $p) {
                fputcsv($output, [
                    $no++,
                    $p->no_pelanggan,
                    $p->nama_pelanggan,
                    $p->alamat,
                    nama_bulan($p->bulan) . ' ' . $p->tahun,
                    $p->meter_awal,
                    $p->meter_akhir,
                    $p->pemakaian,
                    $p->nama_tarif,
                    $p->total_tagihan
                ]);
            }
        } elseif ($type == 'pembayaran') {
            // Header CSV
            fputcsv($output, ['No', 'No Kwitansi', 'No Pelanggan', 'Nama Pelanggan', 'Periode', 'Total Tagihan', 'Jumlah Bayar', 'Metode', 'Tanggal Bayar', 'Status']);
            
            // Data
            $this->db->select('p.*, pa.bulan, pa.tahun, pa.total_tagihan, pel.nama_pelanggan, pel.no_pelanggan');
            $this->db->from('pembayaran p');
            $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
            $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->order_by('p.tanggal_bayar', 'ASC');
            $pembayaran = $this->db->get()->result();
            
            $no = 1;
            foreach ($pembayaran as $p) {
                fputcsv($output, [
                    $no++,
                    $p->no_kwitansi,
                    $p->no_pelanggan,
                    $p->nama_pelanggan,
                    nama_bulan($p->bulan) . ' ' . $p->tahun,
                    $p->total_tagihan,
                    $p->jumlah_bayar,
                    ucfirst($p->metode_bayar),
                    date('d/m/Y', strtotime($p->tanggal_bayar)),
                    ucfirst($p->status_verifikasi)
                ]);
            }
        }
        
        fclose($output);
    }

    // Helper functions removed - using direct queries instead
}
