<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pembayaran extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Pembayaran_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Pelanggan_model');
        $this->load->helper(['form', 'url', 'date']);
        $this->load->library(['form_validation', 'session']);
        
        if (!$this->session->userdata('logged_in')) {
            redirect('auth/login');
        }
        
        if ($this->session->userdata('role') !== 'penagih') {
            redirect('dashboard');
        }
    }

    public function index()
    {
        $data['title'] = 'Verifikasi Pembayaran';
        
        $status = $this->input->get('status') ?: 'pending';
        $search = $this->input->get('search');
        
        $this->db->select('p.*, pa.bulan, pa.tahun, pa.total_tagihan, pel.nama_pelanggan, pel.no_pelanggan');
        $this->db->from('pembayaran p');
        $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        
        if ($status != 'all') {
            $this->db->where('p.status_verifikasi', $status);
        }
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('pel.nama_pelanggan', $search);
            $this->db->or_like('pel.no_pelanggan', $search);
            $this->db->or_like('p.no_kwitansi', $search);
            $this->db->group_end();
        }
        
        $this->db->order_by('p.created_at', 'DESC');
        $data['pembayaran'] = $this->db->get()->result();
        
        $data['status_filter'] = $status;
        $data['search'] = $search;
        
        $this->db->where('status_verifikasi', 'pending');
        $data['total_pending'] = $this->db->count_all_results('pembayaran');
        
        $this->db->where('status_verifikasi', 'verified');
        $data['total_verified'] = $this->db->count_all_results('pembayaran');
        
        $this->db->where('status_verifikasi', 'rejected');
        $data['total_rejected'] = $this->db->count_all_results('pembayaran');
        
        $this->load->view('penagih/pembayaran/index', $data);
    }

    public function verifikasi($id)
    {
        $data['title'] = 'Verifikasi Pembayaran';
        
        $this->db->select('p.*, pa.bulan, pa.tahun, pa.total_tagihan, pa.pemakaian, pa.id_pelanggan, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat');
        $this->db->from('pembayaran p');
        $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        $this->db->where('p.id', $id);
        $data['pembayaran'] = $this->db->get()->row();
        
        if (!$data['pembayaran']) {
            $this->session->set_flashdata('error', 'Data pembayaran tidak ditemukan.');
            redirect('penagih/pembayaran');
        }
        
        if ($this->input->post()) {
            $status = $this->input->post('status_verifikasi');
            $catatan = $this->input->post('catatan');
            
            $update_data = [
                'status_verifikasi' => $status,
                'petugas_verifikasi' => $this->session->userdata('user_id'),
                'tanggal_verifikasi' => date('Y-m-d H:i:s'),
                'catatan' => $catatan
            ];
            
            $this->db->where('id', $id);
            if ($this->db->update('pembayaran', $update_data)) {
                $log_data = [
                    'id_user' => $this->session->userdata('user_id'),
                    'aktivitas' => 'Verifikasi pembayaran: ' . $data['pembayaran']->no_kwitansi . ' - Status: ' . $status,
                    'tabel_terkait' => 'pembayaran',
                    'id_record' => $id,
                    'ip_address' => $this->input->ip_address(),
                    'user_agent' => $this->input->user_agent()
                ];
                $this->db->insert('log_aktivitas', $log_data);
                
                $this->session->set_flashdata('success', 'Pembayaran berhasil diverifikasi.');
                redirect('penagih/pembayaran');
            } else {
                $this->session->set_flashdata('error', 'Gagal memverifikasi pembayaran.');
            }
        }
        
        $this->load->view('penagih/pembayaran/verifikasi', $data);
    }
}