<?php

if (!function_exists('set_flash_message')) {
    function set_flash_message($type, $message) {
        $CI =& get_instance();
        $CI->session->set_flashdata('flash_message', array(
            'type' => $type,
            'message' => $message
        ));
    }
}

if (!function_exists('show_flash_message')) {
    function show_flash_message() {
        $CI =& get_instance();
        $flash = $CI->session->flashdata('flash_message');
        
        if ($flash) {
            $alert_class = '';
            switch ($flash['type']) {
                case 'success':
                    $alert_class = 'alert-success';
                    $icon = 'fas fa-check-circle';
                    break;
                case 'error':
                    $alert_class = 'alert-danger';
                    $icon = 'fas fa-exclamation-circle';
                    break;
                case 'warning':
                    $alert_class = 'alert-warning';
                    $icon = 'fas fa-exclamation-triangle';
                    break;
                case 'info':
                    $alert_class = 'alert-info';
                    $icon = 'fas fa-info-circle';
                    break;
                default:
                    $alert_class = 'alert-primary';
                    $icon = 'fas fa-info-circle';
            }
            
            return '<div class="alert ' . $alert_class . ' alert-dismissible fade show" role="alert">
                        <i class="' . $icon . ' me-2"></i>' . $flash['message'] . '
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>';
        }
        
        return '';
    }
}