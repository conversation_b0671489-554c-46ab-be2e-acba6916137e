<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-users me-2"></i>Data Pelanggan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Filter & Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label"><PERSON><PERSON></label>
                <input type="text" class="form-control" name="search" value="<?php echo $search; ?>" 
                       placeholder="Nama, No. pelanggan, alamat...">
            </div>
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select class="form-select" name="status">
                    <option value="">Semua Status</option>
                    <option value="aktif" <?php echo ($status_filter == 'aktif') ? 'selected' : ''; ?>>Aktif</option>
                    <option value="nonaktif" <?php echo ($status_filter == 'nonaktif') ? 'selected' : ''; ?>>Non Aktif</option>
                    <option value="putus" <?php echo ($status_filter == 'putus') ? 'selected' : ''; ?>>Putus</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Cari
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?php echo base_url('penagih/pelanggan'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_pelanggan); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Pelanggan Aktif</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format(count(array_filter($pelanggan, function($p) { return $p->status_langganan == 'aktif'; }))); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Non Aktif</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format(count(array_filter($pelanggan, function($p) { return $p->status_langganan != 'aktif'; }))); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Daftar Pelanggan
            <?php if ($search || $status_filter): ?>
                <small class="text-muted">
                    (<?php echo count($pelanggan); ?> hasil pencarian)
                </small>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($pelanggan)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th width="5%">No</th>
                        <th width="12%">No. Pelanggan</th>
                        <th width="20%">Nama Pelanggan</th>
                        <th width="25%">Alamat</th>
                        <th width="12%">No. HP</th>
                        <th width="10%">Status</th>
                        <th width="10%">Pemasangan</th>
                        <th width="16%">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pelanggan as $index => $p): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td>
                            <strong class="text-primary"><?php echo $p->no_pelanggan; ?></strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong><?php echo $p->nama_pelanggan; ?></strong>
                                    <?php if ($p->email): ?>
                                        <br><small class="text-muted"><i class="fas fa-envelope"></i> <?php echo $p->email; ?></small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <small class="text-muted"><?php echo $p->alamat; ?></small>
                        </td>
                        <td>
                            <?php if ($p->no_hp): ?>
                                <a href="tel:<?php echo $p->no_hp; ?>" class="text-decoration-none">
                                    <i class="fas fa-phone text-success"></i> <?php echo $p->no_hp; ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php
                            $status_class = '';
                            $status_text = '';
                            $status_icon = '';
                            switch ($p->status_langganan) {
                                case 'aktif':
                                    $status_class = 'success';
                                    $status_text = 'Aktif';
                                    $status_icon = 'check-circle';
                                    break;
                                case 'nonaktif':
                                    $status_class = 'warning';
                                    $status_text = 'Non Aktif';
                                    $status_icon = 'pause-circle';
                                    break;
                                case 'putus':
                                    $status_class = 'danger';
                                    $status_text = 'Putus';
                                    $status_icon = 'times-circle';
                                    break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $status_class; ?>">
                                <i class="fas fa-<?php echo $status_icon; ?>"></i> <?php echo $status_text; ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted"><?php echo format_tanggal($p->tanggal_pemasangan); ?></small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('penagih/pelanggan/detail/' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-success" title="Input Penggunaan">
                                    <i class="fas fa-tachometer-alt"></i>
                                </a>
                                <?php if ($p->no_hp): ?>
                                <a href="https://wa.me/62<?php echo substr($p->no_hp, 1); ?>?text=Halo%20<?php echo urlencode($p->nama_pelanggan); ?>,%20ini%20dari%20PDAM%20Tirta%20Sejahtera" 
                                   target="_blank" class="btn btn-sm btn-outline-success" title="WhatsApp">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada data pelanggan</h5>
            <?php if ($search || $status_filter): ?>
                <p class="text-muted">Coba ubah kata kunci pencarian atau filter</p>
                <a href="<?php echo base_url('penagih/pelanggan'); ?>" class="btn btn-primary">
                    <i class="fas fa-refresh"></i> Reset Pencarian
                </a>
            <?php else: ?>
                <p class="text-muted">Belum ada data pelanggan yang terdaftar</p>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-tachometer-alt me-2"></i>Input Penggunaan
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo base_url('penagih/pembayaran'); ?>" class="btn btn-warning w-100 mb-2">
                            <i class="fas fa-money-bill-wave me-2"></i>Verifikasi Pembayaran
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo base_url('penagih/penggunaan'); ?>" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-list me-2"></i>Data Penggunaan
                        </a>
                    </div>
                    <div class="col-md-3">
                        <button onclick="exportData()" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-download me-2"></i>Export Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
}

@media print {
    .btn-toolbar, .card-header, .btn-group, .navbar, .sidebar, .border-bottom {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table th, .table td {
        font-size: 12px !important;
        padding: 4px !important;
    }
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}
</style>

<script>
function exportData() {
    // Simple export functionality
    window.open('<?php echo base_url("penagih/pelanggan/export"); ?>', '_blank');
}

// Real-time search
$('input[name="search"]').on('keyup', function() {
    let search = $(this).val();
    if (search.length > 2) {
        // Could implement AJAX search here
    }
});
</script>

<?php $this->load->view('templates/footer'); ?>