<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Pengaturan Sistem</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pengaturan/info'); ?>" class="btn btn-sm btn-outline-info">
                <i class="fas fa-info-circle"></i> Info Sistem
            </a>
            <a href="<?php echo base_url('admin/pengaturan/backup'); ?>" class="btn btn-sm btn-outline-warning">
                <i class="fas fa-database"></i> Backup
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Navigation Tabs -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="settingTabs">
            <li class="nav-item">
                <button class="nav-link active" id="perusahaan-tab" data-bs-toggle="tab" data-bs-target="#perusahaan">
                    <i class="fas fa-building me-1"></i>Perusahaan
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" id="sistem-tab" data-bs-toggle="tab" data-bs-target="#sistem">
                    <i class="fas fa-cogs me-1"></i>Sistem
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" id="notifikasi-tab" data-bs-toggle="tab" data-bs-target="#notifikasi">
                    <i class="fas fa-bell me-1"></i>Notifikasi
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced">
                    <i class="fas fa-tools me-1"></i>Advanced
                </button>
            </li>
        </ul>
    </div>
    
    <div class="card-body">
        <?php echo form_open('admin/pengaturan/sistem', 'id="settingsForm"'); ?>
        
        <div class="tab-content">
            <!-- Tab Perusahaan -->
            <div class="tab-pane fade show active" id="perusahaan">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="nama_perusahaan" class="form-label">Nama Perusahaan <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_perusahaan" name="nama_perusahaan" 
                                   value="<?php echo $settings['nama_perusahaan'] ?? 'PDAM Tirta Sejahtera'; ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="alamat_perusahaan" class="form-label">Alamat Perusahaan</label>
                            <textarea class="form-control" id="alamat_perusahaan" name="alamat_perusahaan" rows="3"><?php echo $settings['alamat_perusahaan'] ?? ''; ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="no_telepon" class="form-label">No Telepon</label>
                                <input type="tel" class="form-control" id="no_telepon" name="no_telepon" 
                                       value="<?php echo $settings['no_telepon'] ?? ''; ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email_perusahaan" class="form-label">Email Perusahaan</label>
                                <input type="email" class="form-control" id="email_perusahaan" name="email_perusahaan" 
                                       value="<?php echo $settings['email_perusahaan'] ?? ''; ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="website" class="form-label">Website</label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="<?php echo $settings['website'] ?? ''; ?>" placeholder="https://example.com">
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6>Logo Perusahaan</h6>
                                <div class="mb-3">
                                    <div class="logo-preview bg-white border rounded p-3" style="height: 150px; display: flex; align-items: center; justify-content: center;">
                                        <?php if (!empty($settings['logo_perusahaan'])): ?>
                                            <img src="<?php echo base_url('uploads/logo/' . $settings['logo_perusahaan']); ?>" 
                                                 alt="Logo" class="img-fluid" style="max-height: 120px;">
                                        <?php else: ?>
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <input type="file" class="form-control-file d-none" id="logo_upload" accept="image/*">
                                <input type="hidden" name="logo_perusahaan" value="<?php echo $settings['logo_perusahaan'] ?? ''; ?>">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="document.getElementById('logo_upload').click()">
                                    <i class="fas fa-upload"></i> Upload Logo
                                </button>
                                <div class="form-text">Format: JPG, PNG. Max: 2MB</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tab Sistem -->
            <div class="tab-pane fade" id="sistem">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Pengaturan Tagihan</h6>
                        
                        <div class="mb-3">
                            <label for="jatuh_tempo_hari" class="form-label">Jatuh Tempo Pembayaran (Hari)</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="jatuh_tempo_hari" name="jatuh_tempo_hari" 
                                       value="<?php echo $settings['jatuh_tempo_hari'] ?? '30'; ?>" min="1" max="365">
                                <span class="input-group-text">hari</span>
                            </div>
                            <div class="form-text">Jumlah hari setelah tagihan diterbitkan</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="denda_per_hari" class="form-label">Denda Keterlambatan per Hari</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" class="form-control" id="denda_per_hari" name="denda_per_hari" 
                                       value="<?php echo $settings['denda_per_hari'] ?? '0'; ?>" min="0">
                            </div>
                            <div class="form-text">Denda yang dikenakan per hari keterlambatan</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="timezone" class="form-label">Timezone</label>
                            <select class="form-select" id="timezone" name="timezone">
                                <option value="Asia/Jakarta" <?php echo (($settings['timezone'] ?? 'Asia/Jakarta') == 'Asia/Jakarta') ? 'selected' : ''; ?>>Asia/Jakarta (WIB)</option>
                                <option value="Asia/Makassar" <?php echo (($settings['timezone'] ?? '') == 'Asia/Makassar') ? 'selected' : ''; ?>>Asia/Makassar (WITA)</option>
                                <option value="Asia/Jayapura" <?php echo (($settings['timezone'] ?? '') == 'Asia/Jayapura') ? 'selected' : ''; ?>>Asia/Jayapura (WIT)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Fitur Otomatis</h6>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto_generate_invoice" name="auto_generate_invoice" 
                                       value="aktif" <?php echo (($settings['auto_generate_invoice'] ?? '') == 'aktif') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="auto_generate_invoice">
                                    <strong>Auto Generate Invoice</strong><br>
                                    <small class="text-muted">Otomatis membuat tagihan setiap bulan</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="backup_otomatis" name="backup_otomatis" 
                                       value="aktif" <?php echo (($settings['backup_otomatis'] ?? '') == 'aktif') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="backup_otomatis">
                                    <strong>Backup Otomatis</strong><br>
                                    <small class="text-muted">Backup database otomatis setiap minggu</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                       value="aktif" <?php echo (($settings['maintenance_mode'] ?? '') == 'aktif') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="maintenance_mode">
                                    <strong>Mode Maintenance</strong><br>
                                    <small class="text-muted text-warning">⚠️ Sistem tidak dapat diakses user biasa</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tab Notifikasi -->
            <div class="tab-pane fade" id="notifikasi">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">Email Notification</h6>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="email_pengingat" name="email_pengingat" 
                                       value="aktif" <?php echo (($settings['email_pengingat'] ?? '') == 'aktif') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_pengingat">
                                    <strong>Email Pengingat Pembayaran</strong><br>
                                    <small class="text-muted">Kirim email otomatis untuk mengingatkan pembayaran</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Pengaturan Email SMTP</strong><br>
                            Untuk mengaktifkan email notification, pastikan sudah mengkonfigurasi 
                            <a href="<?php echo base_url('admin/pengaturan/email'); ?>" class="alert-link">pengaturan email SMTP</a>.
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">SMS Notification</h6>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="sms_pengingat" name="sms_pengingat" 
                                       value="aktif" <?php echo (($settings['sms_pengingat'] ?? '') == 'aktif') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_pengingat">
                                    <strong>SMS Pengingat Pembayaran</strong><br>
                                    <small class="text-muted">Kirim SMS otomatis untuk mengingatkan pembayaran</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>SMS Gateway</strong><br>
                            Fitur SMS memerlukan konfigurasi SMS Gateway. Hubungi administrator untuk setup.
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted mb-3">Template Notifikasi</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Template Email Pengingat</h6>
                                    </div>
                                    <div class="card-body">
                                        <small class="text-muted">
                                            <strong>Subject:</strong> Pengingat Pembayaran Air - [NO_PELANGGAN]<br>
                                            <strong>Content:</strong><br>
                                            Yth. [NAMA_PELANGGAN],<br><br>
                                            Tagihan air Anda untuk periode [PERIODE] sebesar [JUMLAH_TAGIHAN] akan jatuh tempo pada [TANGGAL_JATUH_TEMPO].<br><br>
                                            Mohon segera lakukan pembayaran. Terima kasih.
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Template SMS Pengingat</h6>
                                    </div>
                                    <div class="card-body">
                                        <small class="text-muted">
                                            PDAM: Tagihan air [NO_PELANGGAN] periode [PERIODE] sebesar [JUMLAH_TAGIHAN] jatuh tempo [TANGGAL_JATUH_TEMPO]. Mohon segera bayar.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tab Advanced -->
            <div class="tab-pane fade" id="advanced">
                <div class="row">
                    <div class="col-md-8">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Peringatan!</strong> Pengaturan di tab ini dapat mempengaruhi kinerja sistem. 
                            Pastikan Anda memahami konsekuensinya sebelum mengubah.
                        </div>
                        
                        <h6 class="text-muted mb-3">Database & Performance</h6>
                        
                        <div class="card border">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Auto Cleanup</h6>
                                        <p class="text-muted small">Bersihkan data lama secara otomatis</p>
                                        
                                        <div class="mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="cleanup_logs">
                                                <label class="form-check-label" for="cleanup_logs">
                                                    Hapus log aktivitas > 6 bulan
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="cleanup_sessions">
                                                <label class="form-check-label" for="cleanup_sessions">
                                                    Hapus session expired
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <h6>Cache Settings</h6>
                                        <p class="text-muted small">Pengaturan cache sistem</p>
                                        
                                        <div class="mb-2">
                                            <label class="form-label">Cache Duration</label>
                                            <select class="form-select form-select-sm">
                                                <option value="3600">1 Jam</option>
                                                <option value="7200">2 Jam</option>
                                                <option value="86400" selected>24 Jam</option>
                                            </select>
                                        </div>
                                        
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearCache()">
                                            <i class="fas fa-trash"></i> Clear Cache
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6 class="text-muted mb-3">System Actions</h6>
                            <div class="d-flex gap-2">
                                <a href="<?php echo base_url('admin/pengaturan/backup/create'); ?>" class="btn btn-outline-warning" 
                                   onclick="return confirm('Buat backup database sekarang?')">
                                    <i class="fas fa-database"></i> Backup Database
                                </a>
                                <a href="<?php echo base_url('admin/pengaturan/clear_cache'); ?>" class="btn btn-outline-info"
                                   onclick="return confirm('Bersihkan semua cache?')">
                                    <i class="fas fa-broom"></i> Clear Cache
                                </a>
                                <button type="button" class="btn btn-outline-success" onclick="optimizeDatabase()">
                                    <i class="fas fa-tools"></i> Optimize DB
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">System Status</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-2">
                                    <small class="text-muted">Database:</small>
                                    <span class="badge bg-success float-end">Online</span>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Email Service:</small>
                                    <span class="badge bg-success float-end">Active</span>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Cache:</small>
                                    <span class="badge bg-info float-end">Enabled</span>
                                </div>
                                <div class="mb-2">
                                    <small class="text-muted">Last Backup:</small>
                                    <span class="badge bg-warning float-end">2 days ago</span>
                                </div>
                                
                                <hr>
                                
                                <div class="text-center">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="checkSystemHealth()">
                                        <i class="fas fa-heartbeat"></i> Check Health
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Submit Buttons -->
        <hr>
        <div class="d-flex justify-content-between">
            <div>
                <button type="button" class="btn btn-outline-secondary" onclick="resetToDefault()">
                    <i class="fas fa-undo"></i> Reset Default
                </button>
            </div>
            <div>
                <button type="button" class="btn btn-outline-warning me-2" onclick="previewChanges()">
                    <i class="fas fa-eye"></i> Preview
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Simpan Pengaturan
                </button>
            </div>
        </div>
        
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Modal Preview -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Preview Pengaturan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save"></i> Simpan Perubahan
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Form submission
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
    submitBtn.disabled = true;
    
    // Simulate save (in real implementation, submit the form)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible';
        alertDiv.innerHTML = `
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            <i class="fas fa-check-circle me-2"></i>Pengaturan berhasil disimpan!
        `;
        
        const container = document.querySelector('.card-body');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto hide after 3 seconds
        setTimeout(() => {
            alertDiv.remove();
        }, 3000);
        
        // Submit the actual form
        this.submit();
    }, 1000);
});

// Logo upload handling
document.getElementById('logo_upload').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file
        if (!file.type.startsWith('image/')) {
            alert('File harus berupa gambar');
            return;
        }
        
        if (file.size > 2 * 1024 * 1024) { // 2MB
            alert('Ukuran file maksimal 2MB');
            return;
        }
        
        // Preview image
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('.logo-preview');
            preview.innerHTML = `<img src="${e.target.result}" alt="Logo Preview" class="img-fluid" style="max-height: 120px;">`;
        };
        reader.readAsDataURL(file);
        
        // Upload file (in real implementation)
        uploadLogo(file);
    }
});

function uploadLogo(file) {
    // Simulate upload
    const formData = new FormData();
    formData.append('logo', file);
    
    // In real implementation, use AJAX to upload
    console.log('Uploading logo:', file.name);
}

// Preview changes
function previewChanges() {
    const formData = new FormData(document.getElementById('settingsForm'));
    const changes = [];
    
    for (let [key, value] of formData.entries()) {
        changes.push(`<strong>${key.replace('_', ' ').toUpperCase()}:</strong> ${value}`);
    }
    
    document.getElementById('previewContent').innerHTML = `
        <h6>Perubahan yang akan disimpan:</h6>
        <ul class="list-unstyled">
            ${changes.map(change => `<li class="mb-1">${change}</li>`).join('')}
        </ul>
    `;
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Save settings from modal
function saveSettings() {
    bootstrap.Modal.getInstance(document.getElementById('previewModal')).hide();
    document.getElementById('settingsForm').dispatchEvent(new Event('submit'));
}

// Reset to default
function resetToDefault() {
    if (confirm('Reset semua pengaturan ke nilai default?')) {
        // Reset form values
        document.getElementById('nama_perusahaan').value = 'PDAM Tirta Sejahtera';
        document.getElementById('jatuh_tempo_hari').value = '30';
        document.getElementById('denda_per_hari').value = '0';
        
        // Uncheck all checkboxes
        document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
        
        alert('Pengaturan telah direset ke nilai default. Klik "Simpan Pengaturan" untuk menyimpan.');
    }
}

// Clear cache
function clearCache() {
    if (confirm('Bersihkan semua cache sistem?')) {
        // Show loading
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Clearing...';
        btn.disabled = true;
        
        // Simulate cache clearing
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('Cache berhasil dibersihkan!');
        }, 2000);
    }
}

// Optimize database
function optimizeDatabase() {
    if (confirm('Optimasi database? Proses ini mungkin memakan waktu beberapa menit.')) {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimizing...';
        btn.disabled = true;
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('Database berhasil dioptimasi!');
        }, 3000);
    }
}

// Check system health
function checkSystemHealth() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    btn.disabled = true;
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        
        // Show health report
        alert('System Health Check:\n✅ Database: OK\n✅ Files: OK\n✅ Permissions: OK\n⚠️ Disk Space: 75% used');
    }, 2000);
}

// Auto-save draft
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(() => {
        const formData = new FormData(document.getElementById('settingsForm'));
        localStorage.setItem('settings_draft', JSON.stringify(Object.fromEntries(formData)));
        console.log('Settings auto-saved to localStorage');
    }, 30000); // Save every 30 seconds
}

// Load draft on page load
window.addEventListener('load', () => {
    const draft = localStorage.getItem('settings_draft');
    if (draft) {
        // Option to load draft
        if (confirm('Ditemukan draft pengaturan yang belum disimpan. Muat draft?')) {
            const draftData = JSON.parse(draft);
            Object.keys(draftData).forEach(key => {
                const element = document.querySelector(`[name="${key}"]`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = draftData[key] === 'aktif';
                    } else {
                        element.value = draftData[key];
                    }
                }
            });
        }
    }
    
    // Start auto-save
    startAutoSave();
});

// Clear draft when form is successfully submitted
document.getElementById('settingsForm').addEventListener('submit', () => {
    localStorage.removeItem('settings_draft');
    clearInterval(autoSaveInterval);
});
</script>

<?php $this->load->view('templates/footer'); ?>