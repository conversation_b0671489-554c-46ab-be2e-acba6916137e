<?php
// ========================================
// File: application/controllers/admin/Pengaturan.php
// ========================================

defined('BASEPATH') OR exit('No direct script access allowed');

class Pengaturan extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_role('admin'); // Hanya admin yang bisa akses
        
        $this->load->model('Pengaturan_model');
        $this->load->model('Tarif_model');
        $this->load->model('User_model');
    }

    /**
     * Halaman utama pengaturan - redirect ke sistem
     */
    public function index()
    {
        redirect('admin/pengaturan/sistem');
    }

    /**
     * Pengaturan Sistem
     */
    public function sistem()
    {
        $data['title'] = 'Pengaturan Sistem - PDAM System';
        
        if ($this->input->post()) {
            $settings_data = array(
                'nama_perusahaan' => $this->input->post('nama_perusahaan'),
                'alamat_perusahaan' => $this->input->post('alamat_perusahaan'),
                'no_telepon' => $this->input->post('no_telepon'),
                'email_perusahaan' => $this->input->post('email_perusahaan'),
                'website' => $this->input->post('website'),
                'logo_perusahaan' => $this->input->post('logo_perusahaan'),
                'jatuh_tempo_hari' => $this->input->post('jatuh_tempo_hari'),
                'denda_per_hari' => $this->input->post('denda_per_hari'),
                'email_pengingat' => $this->input->post('email_pengingat'),
                'sms_pengingat' => $this->input->post('sms_pengingat'),
                'auto_generate_invoice' => $this->input->post('auto_generate_invoice'),
                'backup_otomatis' => $this->input->post('backup_otomatis'),
                'maintenance_mode' => $this->input->post('maintenance_mode'),
                'timezone' => $this->input->post('timezone')
            );

            if ($this->Pengaturan_model->update_multiple($settings_data)) {
                // Log activity
                $this->auth_lib->log_activity(
                    $this->auth_lib->get_user_data('user_id'),
                    'Mengupdate pengaturan sistem',
                    'pengaturan'
                );
                
                set_flash_message('success', 'Pengaturan sistem berhasil diupdate');
            } else {
                set_flash_message('error', 'Gagal mengupdate pengaturan sistem');
            }
            
            redirect('admin/pengaturan/sistem');
        }

        // Get current settings
        $settings_keys = array(
            'nama_perusahaan', 'alamat_perusahaan', 'no_telepon', 'email_perusahaan',
            'website', 'logo_perusahaan', 'jatuh_tempo_hari', 'denda_per_hari',
            'email_pengingat', 'sms_pengingat', 'auto_generate_invoice',
            'backup_otomatis', 'maintenance_mode', 'timezone'
        );
        
        $data['settings'] = $this->Pengaturan_model->get_settings($settings_keys);
        
        $this->load->view('admin/pengaturan/sistem', $data);
    }

    /**
     * Pengaturan Tarif Air
     */
    public function tarif()
    {
        $data['title'] = 'Pengaturan Tarif Air - PDAM System';
        $data['tarif_list'] = $this->Tarif_model->get_all();
        
        $this->load->view('admin/pengaturan/tarif', $data);
    }

    /**
     * Add/Edit Tarif Air
     */
    public function tarif_form($id = null)
    {
        $data['title'] = ($id) ? 'Edit Tarif Air' : 'Tambah Tarif Air';
        $data['action'] = ($id) ? 'edit' : 'add';
        
        if ($id) {
            $data['tarif'] = $this->Tarif_model->get_by_id($id);
            if (!$data['tarif']) {
                show_404();
            }
        }

        if ($this->input->post()) {
            $this->form_validation->set_rules('nama_tarif', 'Nama Tarif', 'required|trim|max_length[50]');
            $this->form_validation->set_rules('tarif_per_m3', 'Tarif per m³', 'required|numeric|greater_than[0]');
            $this->form_validation->set_rules('batas_minimum', 'Batas Minimum', 'required|integer|greater_than[0]');
            $this->form_validation->set_rules('biaya_admin', 'Biaya Admin', 'required|numeric|greater_than_equal_to[0]');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[aktif,nonaktif]');

            if ($this->form_validation->run() == TRUE) {
                $tarif_data = array(
                    'nama_tarif' => $this->input->post('nama_tarif'),
                    'tarif_per_m3' => $this->input->post('tarif_per_m3'),
                    'batas_minimum' => $this->input->post('batas_minimum'),
                    'biaya_admin' => $this->input->post('biaya_admin'),
                    'status' => $this->input->post('status')
                );

                if ($id) {
                    // Update
                    if ($this->Tarif_model->update($id, $tarif_data)) {
                        $this->auth_lib->log_activity(
                            $this->auth_lib->get_user_data('user_id'),
                            'Mengupdate tarif: ' . $tarif_data['nama_tarif'],
                            'tarif',
                            $id
                        );
                        set_flash_message('success', 'Tarif berhasil diupdate');
                    } else {
                        set_flash_message('error', 'Gagal mengupdate tarif');
                    }
                } else {
                    // Insert
                    if ($this->Tarif_model->insert($tarif_data)) {
                        $this->auth_lib->log_activity(
                            $this->auth_lib->get_user_data('user_id'),
                            'Menambah tarif baru: ' . $tarif_data['nama_tarif'],
                            'tarif'
                        );
                        set_flash_message('success', 'Tarif berhasil ditambahkan');
                    } else {
                        set_flash_message('error', 'Gagal menambahkan tarif');
                    }
                }
                
                redirect('admin/pengaturan/tarif');
            }
        }

        $this->load->view('admin/pengaturan/tarif_form', $data);
    }

    /**
     * Delete Tarif
     */
    public function delete_tarif($id = null)
    {
        if (!$id) {
            show_404();
        }

        $tarif = $this->Tarif_model->get_by_id($id);
        if (!$tarif) {
            show_404();
        }

        // Cek apakah tarif sedang digunakan
        $this->load->model('Penggunaan_model');
        $usage_count = $this->Penggunaan_model->count_by_tarif($id);
        
        if ($usage_count > 0) {
            set_flash_message('error', 'Tidak dapat menghapus tarif. Masih ada ' . $usage_count . ' data penggunaan yang menggunakan tarif ini.');
            redirect('admin/pengaturan/tarif');
        }

        if ($this->Tarif_model->delete($id)) {
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Menghapus tarif: ' . $tarif->nama_tarif,
                'tarif',
                $id
            );
            set_flash_message('success', 'Tarif berhasil dihapus');
        } else {
            set_flash_message('error', 'Gagal menghapus tarif');
        }

        redirect('admin/pengaturan/tarif');
    }

    /**
     * Toggle Status Tarif
     */
    public function toggle_tarif($id = null)
    {
        if (!$id) {
            show_404();
        }

        $tarif = $this->Tarif_model->get_by_id($id);
        if (!$tarif) {
            show_404();
        }

        $new_status = ($tarif->status == 'aktif') ? 'nonaktif' : 'aktif';

        if ($this->Tarif_model->update($id, array('status' => $new_status))) {
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Mengubah status tarif ' . $tarif->nama_tarif . ' menjadi ' . $new_status,
                'tarif',
                $id
            );
            set_flash_message('success', 'Status tarif berhasil diubah');
        } else {
            set_flash_message('error', 'Gagal mengubah status tarif');
        }

        redirect('admin/pengaturan/tarif');
    }

    /**
     * Pengaturan Email
     */
    public function email()
    {
        $data['title'] = 'Pengaturan Email - PDAM System';
    
        if ($this->input->post()) {
            // Validation rules
            $this->form_validation->set_rules('smtp_host', 'SMTP Host', 'required|trim');
            $this->form_validation->set_rules('smtp_port', 'SMTP Port', 'required|integer');
            $this->form_validation->set_rules('smtp_user', 'Username Email', 'required|valid_email');
            $this->form_validation->set_rules('smtp_pass', 'Password Email', 'required');
            $this->form_validation->set_rules('email_from_name', 'Nama Pengirim', 'required|trim');
    
            if ($this->form_validation->run() == TRUE) {
                $email_settings = array(
                    'smtp_host' => $this->input->post('smtp_host'),
                    'smtp_port' => $this->input->post('smtp_port'),
                    'smtp_user' => $this->input->post('smtp_user'),
                    'smtp_pass' => $this->input->post('smtp_pass'),
                    'smtp_crypto' => $this->input->post('smtp_crypto'),
                    'email_from_name' => $this->input->post('email_from_name'),
                    'email_from_address' => $this->input->post('email_from_address'),
                    'email_template_header' => $this->input->post('email_template_header'),
                    'email_template_footer' => $this->input->post('email_template_footer')
                );
    
                if ($this->Pengaturan_model->update_multiple($email_settings)) {
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Mengupdate pengaturan email',
                        'pengaturan'
                    );
                    set_flash_message('success', 'Pengaturan email berhasil disimpan');
                } else {
                    set_flash_message('error', 'Gagal menyimpan pengaturan email');
                }
                
                redirect('admin/pengaturan/email');
            }
        }
    
        // Get current email settings
        $email_keys = array(
            'smtp_host', 'smtp_port', 'smtp_user', 'smtp_pass', 'smtp_crypto',
            'email_from_name', 'email_from_address', 'email_template_header',
            'email_template_footer'
        );
        
        $data['email_settings'] = $this->Pengaturan_model->get_settings($email_keys);
        
        $this->load->view('admin/pengaturan/email', $data);
    }

    /**
     * Backup Database
     */
    public function backup()
    {
        $data['title'] = 'Backup & Restore - PDAM System';
        
        // Get backup history
        $backup_dir = FCPATH . 'backups/';
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $backups = array();
        if (is_dir($backup_dir)) {
            $files = scandir($backup_dir);
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
                    $backups[] = array(
                        'filename' => $file,
                        'size' => filesize($backup_dir . $file),
                        'date' => date('d/m/Y H:i:s', filemtime($backup_dir . $file))
                    );
                }
            }
            // Sort by date descending
            usort($backups, function($a, $b) {
                return filemtime(FCPATH . 'backups/' . $b['filename']) - filemtime(FCPATH . 'backups/' . $a['filename']);
            });
        }
        
        $data['backups'] = $backups;
        
        $this->load->view('admin/pengaturan/backup', $data);
    }

    /**
     * Create Database Backup
     */
    public function create_backup()
    {
        $this->load->dbutil();

        // Get backup preferences
        $prefs = array(
            'tables' => array(), // Empty array = all tables
            'ignore' => array(),
            'format' => 'txt',
            'filename' => 'pdam_backup_' . date('Y-m-d_H-i-s') . '.sql',
            'add_drop' => TRUE,
            'add_insert' => TRUE,
            'newline' => "\n"
        );

        $backup = $this->dbutil->backup($prefs);

        // Save to file
        $backup_dir = FCPATH . 'backups/';
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }

        $filename = $prefs['filename'];
        $filepath = $backup_dir . $filename;

        if (write_file($filepath, $backup)) {
            // Log activity
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Membuat backup database: ' . $filename,
                'backup'
            );
            
            set_flash_message('success', 'Backup database berhasil dibuat: ' . $filename);
        } else {
            set_flash_message('error', 'Gagal membuat backup database');
        }

        redirect('admin/pengaturan/backup');
    }

    /**
     * Download Backup File
     */
    public function download_backup($filename = null)
    {
        if (!$filename) {
            show_404();
        }

        $filepath = FCPATH . 'backups/' . $filename;
        
        if (!file_exists($filepath)) {
            show_404();
        }

        // Log activity
        $this->auth_lib->log_activity(
            $this->auth_lib->get_user_data('user_id'),
            'Download backup: ' . $filename,
            'backup'
        );

        $this->load->helper('download');
        force_download($filename, file_get_contents($filepath));
    }

    /**
     * Delete Backup File
     */
    public function delete_backup($filename = null)
    {
        if (!$filename) {
            show_404();
        }

        $filepath = FCPATH . 'backups/' . $filename;
        
        if (file_exists($filepath)) {
            if (unlink($filepath)) {
                $this->auth_lib->log_activity(
                    $this->auth_lib->get_user_data('user_id'),
                    'Menghapus backup: ' . $filename,
                    'backup'
                );
                set_flash_message('success', 'File backup berhasil dihapus');
            } else {
                set_flash_message('error', 'Gagal menghapus file backup');
            }
        } else {
            set_flash_message('error', 'File backup tidak ditemukan');
        }

        redirect('admin/pengaturan/backup');
    }

    /**
     * System Information
     */
    public function info()
    {
        $data['title'] = 'Informasi Sistem - PDAM System';
        
        // System info
        $data['php_version'] = PHP_VERSION;
        $data['ci_version'] = CI_VERSION;
        $data['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
        $data['max_upload'] = ini_get('upload_max_filesize');
        $data['max_post'] = ini_get('post_max_size');
        $data['memory_limit'] = ini_get('memory_limit');
        $data['max_execution_time'] = ini_get('max_execution_time');
        
        // Database info
        $data['db_version'] = $this->db->version();
        $data['db_platform'] = $this->db->platform();
        
        // Application info
        $data['total_users'] = $this->User_model->count_all();
        $data['total_pelanggan'] = $this->db->count_all('pelanggan');
        $data['total_penggunaan'] = $this->db->count_all('penggunaan_air');
        $data['total_pembayaran'] = $this->db->count_all('pembayaran');
        
        // Disk space
        $data['disk_total'] = disk_total_space('.');
        $data['disk_free'] = disk_free_space('.');
        $data['disk_used'] = $data['disk_total'] - $data['disk_free'];
        
        $this->load->view('admin/pengaturan/info', $data);
    }

    /**
     * Clear Cache/Sessions
     */
    public function clear_cache()
    {
        // Clear CI cache
        $this->output->delete_cache();
        
        // Clear custom cache if any
        $cache_dir = FCPATH . 'cache/';
        if (is_dir($cache_dir)) {
            $files = glob($cache_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
        
        $this->auth_lib->log_activity(
            $this->auth_lib->get_user_data('user_id'),
            'Membersihkan cache sistem',
            'system'
        );
        
        set_flash_message('success', 'Cache sistem berhasil dibersihkan');
        redirect('admin/pengaturan/info');
    }
    
    
    public function test_email()
    {
        // Ensure this is an AJAX request
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
    
        $test_email = $this->input->post('test_email');
        
       if (empty($test_email) || !filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'success' => false,
                    'message' => 'Email tidak valid'
                )));
            return;
        }
    
        // Get email settings from database
        $email_keys = array('smtp_host', 'smtp_port', 'smtp_user', 'smtp_pass', 'smtp_crypto', 'email_from_name');
        $email_settings = $this->Pengaturan_model->get_settings($email_keys);
    
        // Check if settings are complete
        if (empty($email_settings['smtp_host']) || empty($email_settings['smtp_user']) || empty($email_settings['smtp_pass'])) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array(
                    'success' => false,
                    'message' => 'Pengaturan SMTP belum lengkap. Silakan lengkapi konfigurasi terlebih dahulu.'
                )));
            return;
        }
    
        // Configure email with current settings
        $config = array(
            'protocol' => 'mail',
            'mailtype' => 'html',
            'charset' => 'utf-8',
            'newline' => "\r\n",
            'wordwrap' => TRUE
        );
    
        $this->load->library('email');
        $this->email->initialize($config);
    
        // Clear any previous email
        $this->email->clear();
    
        // Set email details
        $from_name = $email_settings['email_from_name'] ?: 'PDAM System';
        $from_email = $email_settings['smtp_user'];
        
        $this->email->from($from_email, $from_name);
        $this->email->to($test_email);
        $this->email->subject('Test Email Configuration - PDAM System');
        
        // Create test email content
        $message = '
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
            <div style="background: #667eea; color: white; padding: 20px; text-align: center;">
                <h2>🎉 Test Email Berhasil!</h2>
            </div>
            <div style="padding: 20px; background: #f8f9fa;">
                <h3>Konfigurasi Email PDAM System</h3>
                <p>Selamat! Email ini menunjukkan bahwa konfigurasi SMTP Anda sudah benar dan berfungsi dengan baik.</p>
                
                <div style="background: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>Detail Konfigurasi:</h4>
                    <ul>
                        <li><strong>SMTP Host:</strong> ' . $email_settings['smtp_host'] . '</li>
                        <li><strong>SMTP Port:</strong> ' . $email_settings['smtp_port'] . '</li>
                        <li><strong>Enkripsi:</strong> ' . strtoupper($email_settings['smtp_crypto']) . '</li>
                        <li><strong>Waktu Test:</strong> ' . date('d/m/Y H:i:s') . '</li>
                    </ul>
                </div>
                
                <p>Sistem sekarang siap untuk mengirim email otomatis seperti:</p>
                <ul>
                    <li>Pengingat pembayaran</li>
                    <li>Notifikasi tagihan</li>
                    <li>Konfirmasi pembayaran</li>
                    <li>Dan email lainnya</li>
                </ul>
                
                <p style="color: #666; font-size: 12px; margin-top: 30px;">
                    Email ini dikirim secara otomatis dari sistem PDAM. Jangan membalas email ini.
                </p>
            </div>
        </div>';
        
        $this->email->message($message);
    
        // Log the attempt
        $log_data = array(
            'to_email' => $test_email,
            'subject' => 'Test Email Configuration - PDAM System',
            'message' => $message,
            'status' => 'pending'
        );
    
        try {
            // Attempt to send email
            if ($this->email->send()) {
                // Success - update log
                $log_data['status'] = 'sent';
                $log_data['sent_at'] = date('Y-m-d H:i:s');
                
                $this->db->insert('email_logs', $log_data);
                
                // Log activity
                $this->auth_lib->log_activity(
                    $this->auth_lib->get_user_data('user_id'),
                    'Test email berhasil dikirim ke: ' . $test_email,
                    'email_logs'
                );
                
                $response = array(
                    'success' => true,
                    'message' => 'Email test berhasil dikirim ke ' . $test_email . '. Silakan cek inbox/spam folder.'
                );
            } else {
                // Failed - get error and log
                $error = $this->email->print_debugger();
                $log_data['status'] = 'failed';
                $log_data['error_message'] = $error;
                
                $this->db->insert('email_logs', $log_data);
                
                $response = array(
                    'success' => false,
                    'message' => 'Gagal mengirim email. Error: ' . strip_tags($error)
                );
            }
        } catch (Exception $e) {
            // Exception occurred
            $log_data['status'] = 'failed';
            $log_data['error_message'] = $e->getMessage();
            
            $this->db->insert('email_logs', $log_data);
            
            $response = array(
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            );
        }
    
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response));
    }
    
    
    public function email_tagihan()
    {
        $data['title'] = 'Kirim Email Tagihan - PDAM System';
        
        // Get list pelanggan yang punya tagihan
        $this->load->model('Pelanggan_model');
        $this->load->model('Penggunaan_model');
        
        $data['pelanggan_list'] = $this->Pelanggan_model->get_with_tagihan();
        
        $this->load->view('admin/pengaturan/email_tagihan', $data);
    }
    
    /**
     * Get tagihan by pelanggan (AJAX)
     */
    public function get_tagihan_pelanggan()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $pelanggan_id = $this->input->post('pelanggan_id');
        
        if (empty($pelanggan_id)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array('success' => false, 'message' => 'ID pelanggan tidak valid')));
            return;
        }
        
        // Get tagihan pelanggan
        $this->load->model('Penggunaan_model');
        $tagihan_list = $this->Penggunaan_model->get_tagihan_by_pelanggan($pelanggan_id);
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode(array('success' => true, 'data' => $tagihan_list)));
    }
    
    /**
     * Kirim email tagihan ke pelanggan
     */
    public function kirim_email_tagihan()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }
        
        $pelanggan_id = $this->input->post('pelanggan_id');
        $penggunaan_id = $this->input->post('penggunaan_id');
        
        if (empty($pelanggan_id) || empty($penggunaan_id)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array('success' => false, 'message' => 'Data tidak lengkap')));
            return;
        }
        
        // Get data pelanggan dan tagihan
        $this->load->model('Pelanggan_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Tarif_model');
        
        $pelanggan = $this->Pelanggan_model->get_by_id($pelanggan_id);
        $penggunaan = $this->Penggunaan_model->get_with_details($penggunaan_id);
        
        if (!$pelanggan || !$penggunaan) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array('success' => false, 'message' => 'Data pelanggan atau tagihan tidak ditemukan')));
            return;
        }
        
        if (empty($pelanggan->email)) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(array('success' => false, 'message' => 'Email pelanggan tidak tersedia')));
            return;
        }
        
        // Configure email
        $config = array(
            'protocol' => 'mail',
            'mailtype' => 'html',
            'charset' => 'utf-8'
        );
        
        $this->load->library('email', $config);
        
        // Get email settings for sender info
        $from_name = $this->Pengaturan_model->get_setting('email_from_name', 'PDAM System');
        $from_email = '<EMAIL>'; // Hardcode aja


        
        // Calculate jatuh tempo
        $jatuh_tempo_hari = $this->Pengaturan_model->get_setting('jatuh_tempo_hari', 30);
        $tanggal_tagihan = new DateTime($penggunaan->tanggal_catat);
        $tanggal_jatuh_tempo = $tanggal_tagihan->add(new DateInterval('P' . $jatuh_tempo_hari . 'D'));
        
        // Prepare email content
        $periode = $this->_format_periode($penggunaan->bulan, $penggunaan->tahun);
        
        $subject = 'Tagihan Air PDAM - ' . $pelanggan->no_pelanggan . ' Periode ' . $periode;
        
        $message = $this->_generate_email_tagihan($pelanggan, $penggunaan, $tanggal_jatuh_tempo->format('d/m/Y'));
        
        // Send email
        $this->email->from($from_email, $from_name);
        $this->email->to($pelanggan->email);
        $this->email->subject($subject);
        $this->email->message($message);
        
        // Log the attempt
        $log_data = array(
            'to_email' => $pelanggan->email,
            'subject' => $subject,
            'message' => $message,
            'status' => 'pending'
        );
        
        try {
            if ($this->email->send()) {
                // Success
                $log_data['status'] = 'sent';
                $log_data['sent_at'] = date('Y-m-d H:i:s');
                
                $this->db->insert('email_logs', $log_data);
                
                // Log activity
                $this->auth_lib->log_activity(
                    $this->auth_lib->get_user_data('user_id'),
                    'Mengirim email tagihan ke: ' . $pelanggan->nama_pelanggan . ' (' . $pelanggan->email . ')',
                    'email_logs'
                );
                
                $response = array(
                    'success' => true,
                    'message' => 'Email tagihan berhasil dikirim ke ' . $pelanggan->nama_pelanggan . ' (' . $pelanggan->email . ')'
                );
            } else {
                // Failed
                $error = $this->email->print_debugger();
                $log_data['status'] = 'failed';
                $log_data['error_message'] = $error;
                
                $this->db->insert('email_logs', $log_data);
                
                $response = array(
                    'success' => false,
                    'message' => 'Gagal mengirim email: ' . strip_tags($error)
                );
            }
        } catch (Exception $e) {
            $log_data['status'] = 'failed';
            $log_data['error_message'] = $e->getMessage();
            
            $this->db->insert('email_logs', $log_data);
            
            $response = array(
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            );
        }
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response));
    }
    
    /**
     * Generate email template untuk tagihan
     */
    private function _generate_email_tagihan($pelanggan, $penggunaan, $jatuh_tempo)
    {
        $periode = $this->_format_periode($penggunaan->bulan, $penggunaan->tahun);
        
        $html = '
        <div style="max-width: 650px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 28px;">💧 PDAM Tirta Sejahtera</h1>
                <p style="margin: 5px 0 0 0; font-size: 16px; opacity: 0.9;">Tagihan Air Periode ' . $periode . '</p>
            </div>
            
            <!-- Content -->
            <div style="background: white; padding: 30px; border: 1px solid #e0e0e0;">
                <div style="margin-bottom: 25px;">
                    <h3 style="color: #333; margin-bottom: 15px;">Yth. ' . htmlspecialchars($pelanggan->nama_pelanggan) . ',</h3>
                    <p style="color: #666; margin-bottom: 20px;">
                        Berikut adalah rincian tagihan air Anda untuk periode <strong>' . $periode . '</strong>:
                    </p>
                </div>
                
                <!-- Info Pelanggan -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                    <h4 style="color: #495057; margin-top: 0; margin-bottom: 15px;">📋 Informasi Pelanggan</h4>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #666; width: 40%;">No. Pelanggan:</td>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">' . $pelanggan->no_pelanggan . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666;">Nama:</td>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">' . htmlspecialchars($pelanggan->nama_pelanggan) . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #666;">Alamat:</td>
                            <td style="padding: 8px 0; color: #333;">' . htmlspecialchars($pelanggan->alamat) . '</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Rincian Tagihan -->
                <div style="background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 5px solid #ffc107; margin-bottom: 25px;">
                    <h4 style="color: #856404; margin-top: 0; margin-bottom: 15px;">💰 Rincian Tagihan</h4>
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <td style="padding: 8px 0; color: #856404;">Periode:</td>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">' . $periode . '</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #856404;">Meter Awal:</td>
                            <td style="padding: 8px 0; color: #333;">' . number_format($penggunaan->meter_awal, 0, ',', '.') . ' m³</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #856404;">Meter Akhir:</td>
                            <td style="padding: 8px 0; color: #333;">' . number_format($penggunaan->meter_akhir, 0, ',', '.') . ' m³</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #856404;">Pemakaian:</td>
                            <td style="padding: 8px 0; font-weight: bold; color: #333;">' . number_format($penggunaan->pemakaian, 0, ',', '.') . ' m³</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; color: #856404;">Tarif:</td>
                            <td style="padding: 8px 0; color: #333;">' . htmlspecialchars($penggunaan->nama_tarif) . ' - Rp ' . number_format($penggunaan->tarif_per_m3, 0, ',', '.') . '/m³</td>
                        </tr>
                        <tr style="border-top: 2px solid #ffc107;">
                            <td style="padding: 15px 0 8px 0; color: #856404; font-size: 18px; font-weight: bold;">Total Tagihan:</td>
                            <td style="padding: 15px 0 8px 0; font-size: 24px; font-weight: bold; color: #dc3545;">Rp ' . number_format($penggunaan->total_tagihan, 0, ',', '.') . '</td>
                        </tr>
                    </table>
                </div>
                
                <!-- Jatuh Tempo -->
                <div style="background: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 5px solid #17a2b8; margin-bottom: 25px;">
                    <h4 style="color: #0c5460; margin-top: 0; margin-bottom: 10px;">⏰ Jatuh Tempo Pembayaran</h4>
                    <p style="margin: 0; font-size: 18px; font-weight: bold; color: #0c5460;">' . $jatuh_tempo . '</p>
                    <p style="margin: 5px 0 0 0; color: #0c5460; font-size: 14px;">Mohon lakukan pembayaran sebelum tanggal tersebut untuk menghindari denda keterlambatan.</p>
                </div>
                
                <!-- Cara Pembayaran -->
                <div style="background: #d4edda; padding: 20px; border-radius: 8px; border-left: 5px solid #28a745; margin-bottom: 25px;">
                    <h4 style="color: #155724; margin-top: 0; margin-bottom: 15px;">💳 Cara Pembayaran</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #155724;">
                        <li style="margin-bottom: 8px;"><strong>Transfer Bank:</strong> BCA 123-456-789 a.n. PDAM Tirta Sejahtera</li>
                        <li style="margin-bottom: 8px;"><strong>Kantor PDAM:</strong> Jl. Merdeka No. 123, Kota (08:00 - 16:00)</li>
                        <li style="margin-bottom: 8px;"><strong>Mobile Banking:</strong> Gunakan kode pelanggan: ' . $pelanggan->no_pelanggan . '</li>
                        <li style="margin-bottom: 8px;"><strong>QRIS:</strong> Scan QR Code di kantor PDAM atau melalui aplikasi</li>
                    </ul>
                </div>
                
                <!-- Penutup -->
                <div style="text-align: center; margin-top: 30px;">
                    <p style="color: #666; margin-bottom: 5px;">Terima kasih atas kepercayaan Anda menggunakan layanan PDAM Tirta Sejahtera.</p>
                    <p style="color: #666; margin: 0;">Untuk informasi lebih lanjut, hubungi: <strong>0821-2345-6789</strong></p>
                </div>
            </div>
            
            <!-- Footer -->
            <div style="background: #343a40; color: white; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 12px;">
                <p style="margin: 0; opacity: 0.8;">© 2025 PDAM Tirta Sejahtera. Email otomatis, mohon jangan membalas.</p>
                <p style="margin: 5px 0 0 0; opacity: 0.6;">Dikirim pada: ' . date('d/m/Y H:i:s') . '</p>
            </div>
        </div>';
        
        return $html;
    }
    
    /**
     * Format periode bulan/tahun
     */
    private function _format_periode($bulan, $tahun)
    {
        $nama_bulan = array(
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        );
        
        return $nama_bulan[$bulan] . ' ' . $tahun;
    }
    
    
    
}