<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard Admin</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i> <?php echo format_tanggal(date('Y-m-d')); ?>
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_pelanggan); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Pelanggan Aktif</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($pelanggan_aktif); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Pending Verifikasi</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($pending_verifikasi); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Penagih</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_penagih); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Ringkasan Keuangan Bulan Ini</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo format_rupiah($total_tagihan_bulan_ini); ?></h4>
                            <p class="text-muted mb-0">Total Tagihan</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="border-end">
                            <h4 class="text-success"><?php echo format_rupiah($total_pembayaran_bulan_ini); ?></h4>
                            <p class="text-muted mb-0">Terbayar</p>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <h4 class="text-warning"><?php echo format_rupiah($total_tagihan_bulan_ini - $total_pembayaran_bulan_ini); ?></h4>
                        <p class="text-muted mb-0">Tunggakan</p>
                    </div>
                </div>
                
                <?php $persentase = $total_tagihan_bulan_ini > 0 ? ($total_pembayaran_bulan_ini / $total_tagihan_bulan_ini) * 100 : 0; ?>
                <div class="mt-3">
                    <div class="d-flex justify-content-between">
                        <small>Persentase Pembayaran</small>
                        <small><?php echo number_format($persentase, 1); ?>%</small>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $persentase; ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('admin/pelanggan/add'); ?>" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>Tambah Pelanggan
                    </a>
                    <a href="<?php echo base_url('admin/users/add'); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>Tambah User
                    </a>
                    <a href="<?php echo base_url('penagih/pembayaran'); ?>" class="btn btn-warning">
                        <i class="fas fa-check me-2"></i>Verifikasi Pembayaran
                    </a>
                    <a href="<?php echo base_url('admin/laporan'); ?>" class="btn btn-info">
                        <i class="fas fa-file-alt me-2"></i>Lihat Laporan
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Payments -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Pembayaran Terbaru</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_payments)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>No. Kwitansi</th>
                                <th>Pelanggan</th>
                                <th>Tanggal Bayar</th>
                                <th>Jumlah</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_payments as $payment): ?>
                            <tr>
                                <td><?php echo $payment->no_kwitansi; ?></td>
                                <td>
                                    <strong><?php echo $payment->nama_pelanggan; ?></strong><br>
                                    <small class="text-muted"><?php echo $payment->no_pelanggan; ?></small>
                                </td>
                                <td><?php echo format_tanggal($payment->tanggal_bayar); ?></td>
                                <td><?php echo format_rupiah($payment->jumlah_bayar); ?></td>
                                <td>
                                    <?php if ($payment->status_verifikasi == 'verified'): ?>
                                        <span class="badge bg-success">Terverifikasi</span>
                                    <?php elseif ($payment->status_verifikasi == 'pending'): ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Ditolak</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo base_url('admin/pembayaran/detail/' . $payment->id); ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Belum ada pembayaran hari ini</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('templates/footer'); ?>