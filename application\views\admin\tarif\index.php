<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tags me-2"></i>Pengaturan Tarif Air
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/tarif/form'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Tarif
            </a>
            <button type="button" class="btn btn-outline-success" onclick="exportData()">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Tarif</h5>
                        <h3 class="mb-0"><?php echo count($tarif_list); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tags fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Tarif Aktif</h5>
                        <h3 class="mb-0"><?php echo count(array_filter($tarif_list, function($t) { return $t->status == 'aktif'; })); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Tarif Nonaktif</h5>
                        <h3 class="mb-0"><?php echo count(array_filter($tarif_list, function($t) { return $t->status == 'nonaktif'; })); ?></h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-pause-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Rata-rata Tarif</h5>
                        <h3 class="mb-0">
                            <?php 
                            $active_tarifs = array_filter($tarif_list, function($t) { return $t->status == 'aktif'; });
                            $avg = !empty($active_tarifs) ? array_sum(array_column($active_tarifs, 'tarif_per_m3')) / count($active_tarifs) : 0;
                            echo 'Rp ' . number_format($avg, 0, ',', '.');
                            ?>
                        </h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters & Search -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="Cari nama tarif...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">Semua Status</option>
                    <option value="aktif">Aktif</option>
                    <option value="nonaktif">Nonaktif</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="sortBy">
                    <option value="nama_tarif">Urutkan: Nama Tarif</option>
                    <option value="tarif_per_m3">Urutkan: Harga per m³</option>
                    <option value="created_at">Urutkan: Tanggal Dibuat</option>
                    <option value="updated_at">Urutkan: Terakhir Diupdate</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo"></i> Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tarif Table -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Daftar Tarif Air</h5>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="bulkSelectAll">
                <label class="form-check-label" for="bulkSelectAll">Pilih Semua</label>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Bulk Actions -->
        <div class="bg-light p-3 border-bottom d-none" id="bulkActions">
            <div class="d-flex justify-content-between align-items-center">
                <span><span id="selectedCount">0</span> item dipilih</span>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-success" onclick="bulkAction('activate')">
                        <i class="fas fa-check"></i> Aktifkan
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="bulkAction('deactivate')">
                        <i class="fas fa-pause"></i> Nonaktifkan
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="bulkAction('delete')">
                        <i class="fas fa-trash"></i> Hapus
                    </button>
                </div>
            </div>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="tarifTable">
                <thead class="bg-light">
                    <tr>
                        <th width="50">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAllHeader">
                            </div>
                        </th>
                        <th>Nama Tarif</th>
                        <th>Tarif per m³</th>
                        <th>Batas Minimum</th>
                        <th>Biaya Admin</th>
                        <th>Status</th>
                        <th>Dibuat</th>
                        <th width="120">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($tarif_list)): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p>Belum ada data tarif.</p>
                                <a href="<?php echo base_url('admin/tarif/form'); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Tambah Tarif Pertama
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($tarif_list as $tarif): ?>
                    <tr data-id="<?php echo $tarif->id; ?>" data-status="<?php echo $tarif->status; ?>" data-nama="<?php echo strtolower($tarif->nama_tarif); ?>">
                        <td>
                            <div class="form-check">
                                <input class="form-check-input bulk-checkbox" type="checkbox" value="<?php echo $tarif->id; ?>">
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                    <i class="fas fa-tag"></i>
                                </div>
                                <div>
                                    <strong><?php echo htmlspecialchars($tarif->nama_tarif); ?></strong>
                                    <?php if ($tarif->status == 'nonaktif'): ?>
                                    <br><small class="text-muted">Nonaktif</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="fw-bold text-success">
                                Rp <?php echo number_format($tarif->tarif_per_m3, 0, ',', '.'); ?>
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                <?php echo $tarif->batas_minimum; ?> m³
                            </span>
                        </td>
                        <td>
                            <span class="text-muted">
                                Rp <?php echo number_format($tarif->biaya_admin, 0, ',', '.'); ?>
                            </span>
                        </td>
                        <td>
                            <div class="form-check form-switch">
                                <input class="form-check-input status-toggle" type="checkbox" 
                                       <?php echo ($tarif->status == 'aktif') ? 'checked' : ''; ?>
                                       data-id="<?php echo $tarif->id; ?>"
                                       data-nama="<?php echo htmlspecialchars($tarif->nama_tarif); ?>">
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo date('d/m/Y', strtotime($tarif->created_at)); ?>
                                <br><?php echo date('H:i', strtotime($tarif->created_at)); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="<?php echo base_url('admin/tarif/form/' . $tarif->id); ?>" 
                                   class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteTarif(<?php echo $tarif->id; ?>, '<?php echo htmlspecialchars($tarif->nama_tarif); ?>')"
                                        title="Hapus">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Quick Add Modal -->
<div class="modal fade" id="quickAddModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Tarif Cepat</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickAddForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Nama Tarif</label>
                        <input type="text" class="form-control" name="nama_tarif" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tarif per m³</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp</span>
                                <input type="number" class="form-control" name="tarif_per_m3" required>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Batas Minimum</label>
                            <div class="input-group">
                                <input type="number" class="form-control" name="batas_minimum" required>
                                <span class="input-group-text">m³</span>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Biaya Admin</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="number" class="form-control" name="biaya_admin" value="0">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Global variables
let allTarifs = [];

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Store original table data
    storeTableData();
    
    // Setup event listeners
    setupEventListeners();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Store table data for filtering
function storeTableData() {
    const rows = document.querySelectorAll('#tarifTable tbody tr[data-id]');
    allTarifs = Array.from(rows).map(row => ({
        element: row,
        id: row.dataset.id,
        nama: row.dataset.nama,
        status: row.dataset.status,
        tarif: row.children[2].textContent.trim(),
        created: row.children[6].textContent.trim()
    }));
}

// Setup event listeners
function setupEventListeners() {
    // Search input
    document.getElementById('searchInput').addEventListener('input', filterTable);
    
    // Status filter
    document.getElementById('statusFilter').addEventListener('change', filterTable);
    
    // Sort by
    document.getElementById('sortBy').addEventListener('change', sortTable);
    
    // Bulk select all
    document.getElementById('bulkSelectAll').addEventListener('change', function() {
        toggleBulkSelect(this.checked);
    });
    
    // Header select all
    document.getElementById('selectAllHeader').addEventListener('change', function() {
        toggleBulkSelect(this.checked);
        document.getElementById('bulkSelectAll').checked = this.checked;
    });
    
    // Individual checkboxes
    document.querySelectorAll('.bulk-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    // Status toggles
    document.querySelectorAll('.status-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            toggleStatus(this.dataset.id, this.dataset.nama, this.checked);
        });
    });
}

// Filter table
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    allTarifs.forEach(tarif => {
        let show = true;
        
        // Search filter
        if (searchTerm && !tarif.nama.includes(searchTerm)) {
            show = false;
        }
        
        // Status filter
        if (statusFilter && tarif.status !== statusFilter) {
            show = false;
        }
        
        tarif.element.style.display = show ? '' : 'none';
    });
}

// Sort table
function sortTable() {
    const sortBy = document.getElementById('sortBy').value;
    const tbody = document.querySelector('#tarifTable tbody');
    
    const sortedTarifs = [...allTarifs].sort((a, b) => {
        switch (sortBy) {
            case 'nama_tarif':
                return a.nama.localeCompare(b.nama);
            case 'tarif_per_m3':
                const priceA = parseFloat(a.tarif.replace(/[^\d]/g, ''));
                const priceB = parseFloat(b.tarif.replace(/[^\d]/g, ''));
                return priceB - priceA;
            case 'created_at':
            case 'updated_at':
                return new Date(b.created) - new Date(a.created);
            default:
                return 0;
        }
    });
    
    // Reorder DOM elements
    sortedTarifs.forEach(tarif => {
        tbody.appendChild(tarif.element);
    });
}

// Reset filters
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('sortBy').value = 'nama_tarif';
    filterTable();
    sortTable();
}

// Toggle bulk select
function toggleBulkSelect(checked) {
    document.querySelectorAll('.bulk-checkbox').forEach(checkbox => {
        if (checkbox.closest('tr').style.display !== 'none') {
            checkbox.checked = checked;
        }
    });
    updateBulkActions();
}

// Update bulk actions visibility
function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('.bulk-checkbox:checked');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (checkedBoxes.length > 0) {
        bulkActions.classList.remove('d-none');
        selectedCount.textContent = checkedBoxes.length;
    } else {
        bulkActions.classList.add('d-none');
    }
}

// Bulk actions
function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.bulk-checkbox:checked');
    const ids = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (ids.length === 0) {
        alert('Pilih minimal satu tarif');
        return;
    }
    
    let message, url;
    
    switch (action) {
        case 'activate':
            message = `Aktifkan ${ids.length} tarif yang dipilih?`;
            url = '<?php echo base_url("admin/tarif/bulk_tarif_status"); ?>';
            break;
        case 'deactivate':
            message = `Nonaktifkan ${ids.length} tarif yang dipilih?`;
            url = '<?php echo base_url("admin/tarif/bulk_tarif_status"); ?>';
            break;
        case 'delete':
            message = `Hapus ${ids.length} tarif yang dipilih? Aksi ini tidak dapat dibatalkan.`;
            url = '<?php echo base_url("admin/tarif/bulk_delete_tarif"); ?>';
            break;
    }
    
    if (confirm(message)) {
        // Show loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
        button.disabled = true;
        
        // In real implementation, use AJAX
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
            alert('Operasi berhasil!');
            location.reload();
        }, 2000);
    }
}

// Toggle status
function toggleStatus(id, nama, isActive) {
    const action = isActive ? 'mengaktifkan' : 'menonaktifkan';
    
    if (confirm(`Yakin ingin ${action} tarif "${nama}"?`)) {
        // In real implementation, use AJAX
        window.location.href = `<?php echo base_url('admin/tarif/toggle_tarif/'); ?>${id}`;
    } else {
        // Revert checkbox
        event.target.checked = !isActive;
    }
}

// Delete tarif
function deleteTarif(id, nama) {
    if (confirm(`Yakin ingin menghapus tarif "${nama}"?\n\nAksi ini tidak dapat dibatalkan.`)) {
        window.location.href = `<?php echo base_url('admin/tarif/delete_tarif/'); ?>${id}`;
    }
}

// Export data
function exportData() {
    const exportOptions = [
        'Excel (.xlsx)',
        'CSV (.csv)',
        'PDF (.pdf)'
    ];
    
    const choice = prompt('Pilih format export:\n1. Excel\n2. CSV\n3. PDF\n\nKetik nomor pilihan (1-3):');
    
    if (choice >= 1 && choice <= 3) {
        const formats = ['xlsx', 'csv', 'pdf'];
        const format = formats[choice - 1];
        
        // Show loading
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'position-fixed top-50 start-50 translate-middle bg-dark text-white p-3 rounded';
        loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengexport data...';
        document.body.appendChild(loadingDiv);
        
        // Simulate export
        setTimeout(() => {
            document.body.removeChild(loadingDiv);
            
            // In real implementation, redirect to export URL
            // window.location.href = `<?php echo base_url('admin/tarif/export_tarif/'); ?>${format}`;
            
            alert(`Data berhasil diexport dalam format ${format.toUpperCase()}!`);
        }, 2000);
    }
}

// Quick add form
document.getElementById('quickAddForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
    submitBtn.disabled = true;
    
    // Simulate save
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Close modal
        bootstrap.Modal.getInstance(document.getElementById('quickAddModal')).hide();
        
        // Show success
        alert('Tarif berhasil ditambahkan!');
        location.reload();
    }, 1500);
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl + N = New tarif
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        window.location.href = '<?php echo base_url("admin/tarif/form"); ?>';
    }
    
    // Ctrl + F = Focus search
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        document.getElementById('searchInput').focus();
    }
    
    // Escape = Clear search
    if (e.key === 'Escape') {
        resetFilters();
    }
});

// Auto-refresh every 5 minutes
setInterval(() => {
    const lastActivity = localStorage.getItem('lastActivity');
    const now = Date.now();
    
    // Only refresh if user has been active in last 5 minutes
    if (!lastActivity || (now - lastActivity) < 300000) {
        console.log('Auto-refreshing tarif data...');
        // In real implementation, use AJAX to refresh data
    }
}, 300000);

// Track user activity
document.addEventListener('click', () => {
    localStorage.setItem('lastActivity', Date.now());
});
</script>

<?php $this->load->view('templates/footer'); ?>