<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Log_model extends CI_Model {
    
    protected $table = 'log_aktivitas';
    
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Insert log activity
     */
    public function insert($data)
    {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * Get logs with user info
     */
    public function get_logs($limit = 100, $offset = 0)
    {
        $this->db->select('log_aktivitas.*, users.nama_lengkap, users.role');
        $this->db->from($this->table);
        $this->db->join('users', 'users.id = log_aktivitas.id_user');
        $this->db->order_by('log_aktivitas.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get()->result();
    }
    
    /**
     * Get logs by user
     */
    public function get_by_user($user_id, $limit = 50)
    {
        $this->db->where('id_user', $user_id);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Clean old logs (older than 6 months)
     */
    public function clean_old_logs()
    {
        $six_months_ago = date('Y-m-d H:i:s', strtotime('-6 months'));
        $this->db->where('created_at <', $six_months_ago);
        return $this->db->delete($this->table);
    }
}