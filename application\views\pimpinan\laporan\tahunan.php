<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    transition: transform 0.2s;
}
.stats-card:hover {
    transform: translateY(-2px);
}
.chart-container {
    position: relative;
    height: 300px;
}
.btn-group .btn.active {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,.075);
}
.badge {
    font-size: 0.75em;
}
</style>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">📊 <PERSON><PERSON><PERSON></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <form method="GET" class="d-flex gap-2">
                <select name="tahun" class="form-select">
                    <?php for($i = date('Y'); $i >= date('Y') - 5; $i--): ?>
                        <option value="<?php echo $i; ?>" <?php echo ($tahun == $i) ? 'selected' : ''; ?>>
                            <?php echo $i; ?>
                        </option>
                    <?php endfor; ?>
                </select>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Tampilkan
                </button>
            </form>
        </div>
        <div class="btn-group">
            <a href="<?php echo base_url('pimpinan/laporan/print_tahunan?tahun=' . $tahun); ?>" 
               class="btn btn-success" target="_blank">
                <i class="fas fa-print"></i> Cetak
            </a>
            <a href="<?php echo base_url('pimpinan/laporan'); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Bulanan
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Ringkasan Tahunan -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Ringkasan Tahun <?php echo $tahun; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center border-end">
                        <h4 class="text-primary"><?php echo number_format($total_tahun_ini['pencatatan']); ?></h4>
                        <p class="text-muted mb-1">Total Pencatatan</p>
                        <?php if($pertumbuhan['pencatatan'] >= 0): ?>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> +<?php echo number_format($pertumbuhan['pencatatan'], 1); ?>%
                            </small>
                        <?php else: ?>
                            <small class="text-danger">
                                <i class="fas fa-arrow-down"></i> <?php echo number_format($pertumbuhan['pencatatan'], 1); ?>%
                            </small>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3 text-center border-end">
                        <h4 class="text-info"><?php echo number_format($total_tahun_ini['pemakaian']); ?> m³</h4>
                        <p class="text-muted mb-1">Total Pemakaian</p>
                        <?php if($pertumbuhan['pemakaian'] >= 0): ?>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> +<?php echo number_format($pertumbuhan['pemakaian'], 1); ?>%
                            </small>
                        <?php else: ?>
                            <small class="text-danger">
                                <i class="fas fa-arrow-down"></i> <?php echo number_format($pertumbuhan['pemakaian'], 1); ?>%
                            </small>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3 text-center border-end">
                        <h4 class="text-warning"><?php echo format_rupiah($total_tahun_ini['tagihan']); ?></h4>
                        <p class="text-muted mb-1">Total Tagihan</p>
                        <?php if($pertumbuhan['tagihan'] >= 0): ?>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> +<?php echo number_format($pertumbuhan['tagihan'], 1); ?>%
                            </small>
                        <?php else: ?>
                            <small class="text-danger">
                                <i class="fas fa-arrow-down"></i> <?php echo number_format($pertumbuhan['tagihan'], 1); ?>%
                            </small>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success"><?php echo format_rupiah($total_tahun_ini['terbayar']); ?></h4>
                        <p class="text-muted mb-1">Total Terbayar</p>
                        <?php if($pertumbuhan['terbayar'] >= 0): ?>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> +<?php echo number_format($pertumbuhan['terbayar'], 1); ?>%
                            </small>
                        <?php else: ?>
                            <small class="text-danger">
                                <i class="fas fa-arrow-down"></i> <?php echo number_format($pertumbuhan['terbayar'], 1); ?>%
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Perbandingan dengan Tahun Sebelumnya -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-balance-scale me-2"></i>Perbandingan dengan <?php echo $tahun_sebelumnya; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Indikator</th>
                                <th class="text-end"><?php echo $tahun_sebelumnya; ?></th>
                                <th class="text-end"><?php echo $tahun; ?></th>
                                <th class="text-center">Growth</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Pencatatan</td>
                                <td class="text-end"><?php echo number_format($total_tahun_lalu['pencatatan']); ?></td>
                                <td class="text-end"><?php echo number_format($total_tahun_ini['pencatatan']); ?></td>
                                <td class="text-center">
                                    <?php if($pertumbuhan['pencatatan'] >= 0): ?>
                                        <span class="badge bg-success">+<?php echo number_format($pertumbuhan['pencatatan'], 1); ?>%</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><?php echo number_format($pertumbuhan['pencatatan'], 1); ?>%</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>Tagihan</td>
                                <td class="text-end"><?php echo format_rupiah($total_tahun_lalu['tagihan']); ?></td>
                                <td class="text-end"><?php echo format_rupiah($total_tahun_ini['tagihan']); ?></td>
                                <td class="text-center">
                                    <?php if($pertumbuhan['tagihan'] >= 0): ?>
                                        <span class="badge bg-success">+<?php echo number_format($pertumbuhan['tagihan'], 1); ?>%</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><?php echo number_format($pertumbuhan['tagihan'], 1); ?>%</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>Terbayar</td>
                                <td class="text-end"><?php echo format_rupiah($total_tahun_lalu['terbayar']); ?></td>
                                <td class="text-end"><?php echo format_rupiah($total_tahun_ini['terbayar']); ?></td>
                                <td class="text-center">
                                    <?php if($pertumbuhan['terbayar'] >= 0): ?>
                                        <span class="badge bg-success">+<?php echo number_format($pertumbuhan['terbayar'], 1); ?>%</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><?php echo number_format($pertumbuhan['terbayar'], 1); ?>%</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>Bulan Terbaik <?php echo $tahun; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php foreach($top_months as $index => $month): ?>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-primary me-2"><?php echo $index + 1; ?></span>
                        <strong><?php echo $month['nama_bulan']; ?></strong>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold"><?php echo format_rupiah($month['total_terbayar']); ?></div>
                        <small class="text-muted"><?php echo $month['jumlah_pembayaran']; ?> pembayaran</small>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Ringkasan Bulanan -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2"></i>Ringkasan Per Bulan Tahun <?php echo $tahun; ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Bulan</th>
                                <th class="text-center">Pencatatan</th>
                                <th class="text-center">Pemakaian (m³)</th>
                                <th class="text-end">Tagihan</th>
                                <th class="text-end">Terbayar</th>
                                <th class="text-center">% Bayar</th>
                                <th class="text-center">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($ringkasan_bulanan as $bulan_data): ?>
                            <tr>
                                <td><strong><?php echo $bulan_data['nama_bulan']; ?></strong></td>
                                <td class="text-center"><?php echo number_format($bulan_data['pencatatan']); ?></td>
                                <td class="text-center"><?php echo number_format($bulan_data['total_pemakaian']); ?></td>
                                <td class="text-end"><?php echo format_rupiah($bulan_data['total_tagihan']); ?></td>
                                <td class="text-end"><?php echo format_rupiah($bulan_data['total_terbayar']); ?></td>
                                <td class="text-center">
                                    <?php 
                                    $persen = $bulan_data['total_tagihan'] > 0 ? 
                                        ($bulan_data['total_terbayar'] / $bulan_data['total_tagihan']) * 100 : 0;
                                    echo number_format($persen, 1) . '%';
                                    ?>
                                </td>
                                <td class="text-center">
                                    <?php if($bulan_data['pencatatan'] == 0): ?>
                                        <span class="badge bg-secondary">Belum Ada</span>
                                    <?php elseif($persen >= 80): ?>
                                        <span class="badge bg-success">Baik</span>
                                    <?php elseif($persen >= 60): ?>
                                        <span class="badge bg-warning">Cukup</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Kurang</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th>TOTAL</th>
                                <th class="text-center"><?php echo number_format($total_tahun_ini['pencatatan']); ?></th>
                                <th class="text-center"><?php echo number_format($total_tahun_ini['pemakaian']); ?></th>
                                <th class="text-end"><?php echo format_rupiah($total_tahun_ini['tagihan']); ?></th>
                                <th class="text-end"><?php echo format_rupiah($total_tahun_ini['terbayar']); ?></th>
                                <th class="text-center">
                                    <?php 
                                    $total_persen = $total_tahun_ini['tagihan'] > 0 ? 
                                        ($total_tahun_ini['terbayar'] / $total_tahun_ini['tagihan']) * 100 : 0;
                                    echo number_format($total_persen, 1) . '%';
                                    ?>
                                </th>
                                <th class="text-center">
                                    <?php if($total_persen >= 80): ?>
                                        <span class="badge bg-success">Baik</span>
                                    <?php elseif($total_persen >= 60): ?>
                                        <span class="badge bg-warning">Cukup</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Kurang</span>
                                    <?php endif; ?>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rata-rata Bulanan -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>Rata-rata per Bulan
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-primary mb-1"><?php echo number_format($rata_rata_bulanan['pencatatan'], 1); ?></h5>
                            <small class="text-muted">Pencatatan/Bulan</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-info mb-1"><?php echo number_format($rata_rata_bulanan['pemakaian'], 1); ?> m³</h5>
                            <small class="text-muted">Pemakaian/Bulan</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h5 class="text-warning mb-1"><?php echo format_rupiah($rata_rata_bulanan['tagihan']); ?></h5>
                            <small class="text-muted">Tagihan/Bulan</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3">
                            <h5 class="text-success mb-1"><?php echo format_rupiah($rata_rata_bulanan['terbayar']); ?></h5>
                            <small class="text-muted">Terbayar/Bulan</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Grafik Tahunan
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" id="showChart" onclick="toggleView('chart')">
                        <i class="fas fa-chart-line"></i> Grafik
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="showTable" onclick="toggleView('table')">
                        <i class="fas fa-table"></i> Tabel
                    </button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="yearlyChart" height="200"></canvas>

                <!-- Fallback Table jika grafik tidak muncul -->
                <div id="chartFallback" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Bulan</th>
                                    <th class="text-end">Tagihan</th>
                                    <th class="text-end">Terbayar</th>
                                    <th class="text-center">Persentase</th>
                                    <th class="text-center">Trend</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ringkasan_bulanan as $index => $bulan): ?>
                                <tr>
                                    <td><strong><?php echo $bulan['nama_bulan']; ?></strong></td>
                                    <td class="text-end"><?php echo format_rupiah($bulan['total_tagihan']); ?></td>
                                    <td class="text-end"><?php echo format_rupiah($bulan['total_terbayar']); ?></td>
                                    <td class="text-center">
                                        <?php
                                        $persentase = $bulan['total_tagihan'] > 0 ? ($bulan['total_terbayar'] / $bulan['total_tagihan']) * 100 : 0;
                                        ?>
                                        <span class="badge bg-<?php echo $persentase >= 80 ? 'success' : ($persentase >= 60 ? 'warning' : 'danger'); ?>">
                                            <?php echo number_format($persentase, 1); ?>%
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <?php if ($index > 0): ?>
                                            <?php
                                            $prev_total = $ringkasan_bulanan[$index-1]['total_terbayar'];
                                            $current_total = $bulan['total_terbayar'];
                                            if ($current_total > $prev_total): ?>
                                                <i class="fas fa-arrow-up text-success"></i>
                                            <?php elseif ($current_total < $prev_total): ?>
                                                <i class="fas fa-arrow-down text-danger"></i>
                                            <?php else: ?>
                                                <i class="fas fa-minus text-muted"></i>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <i class="fas fa-minus text-muted"></i>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Keterangan:</strong> Grafik tidak dapat dimuat, data ditampilkan dalam bentuk tabel.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded');
        document.getElementById('yearlyChart').style.display = 'none';
        document.getElementById('chartFallback').style.display = 'block';
        return;
    }

    try {
        // Yearly Chart
        const yearlyCtx = document.getElementById('yearlyChart').getContext('2d');
        const monthlyData = <?php echo json_encode($ringkasan_bulanan); ?>;

        console.log('Monthly data:', monthlyData); // Debug

        if (!monthlyData || monthlyData.length === 0) {
            document.getElementById('yearlyChart').style.display = 'none';
            document.getElementById('chartFallback').style.display = 'block';
            return;
        }

        const yearlyChart = new Chart(yearlyCtx, {
            type: 'line',
            data: {
                labels: monthlyData.map(item => item.nama_bulan),
                datasets: [{
                    label: 'Tagihan',
                    data: monthlyData.map(item => parseInt(item.total_tagihan) || 0),
                    borderColor: 'rgb(255, 193, 7)',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Terbayar',
                    data: monthlyData.map(item => parseInt(item.total_terbayar) || 0),
                    borderColor: 'rgb(40, 167, 69)',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': Rp ' + context.parsed.y.toLocaleString('id-ID');
                            }
                        }
                    }
                }
            }
        });

        console.log('Chart created successfully');

    } catch (error) {
        console.error('Error creating chart:', error);
        document.getElementById('yearlyChart').style.display = 'none';
        document.getElementById('chartFallback').style.display = 'block';
        // Update button states
        document.getElementById('showChart').classList.remove('active');
        document.getElementById('showTable').classList.add('active');
    }
});

// Toggle between chart and table view
function toggleView(view) {
    const chartElement = document.getElementById('yearlyChart');
    const tableElement = document.getElementById('chartFallback');
    const chartBtn = document.getElementById('showChart');
    const tableBtn = document.getElementById('showTable');

    if (view === 'chart') {
        chartElement.style.display = 'block';
        tableElement.style.display = 'none';
        chartBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else {
        chartElement.style.display = 'none';
        tableElement.style.display = 'block';
        chartBtn.classList.remove('active');
        tableBtn.classList.add('active');
    }
}
</script>