<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth_lib {
    
    private $CI;
    
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->model('User_model');
    }
    
    /**
     * Login user
     */
    public function login($username, $password)
    {
        $user = $this->CI->User_model->get_by_username($username);
        
        if ($user && password_verify($password, $user->password)) {
            if ($user->status == 'aktif') {
                $session_data = array(
                    'user_id' => $user->id,
                    'username' => $user->username,
                    'nama_lengkap' => $user->nama_lengkap,
                    'email' => $user->email,
                    'role' => $user->role,
                    'logged_in' => TRUE
                );
                
                $this->CI->session->set_userdata($session_data);
                
                // Log aktivitas login
                $this->log_activity($user->id, 'Login ke sistem');
                
                return TRUE;
            } else {
                return 'User tidak aktif';
            }
        }
        
        return 'Username atau password salah';
    }
    
    /**
     * Logout user
     */
    public function logout()
    {
        $user_id = $this->CI->session->userdata('user_id');
        
        if ($user_id) {
            $this->log_activity($user_id, 'Logout dari sistem');
        }
        
        $this->CI->session->sess_destroy();
    }
    
    /**
     * Check if user is logged in
     */
    public function is_logged_in()
    {
        return $this->CI->session->userdata('logged_in') === TRUE;
    }
    
    /**
     * Get current user data
     */
    public function get_user_data($key = null)
    {
        if ($key) {
            return $this->CI->session->userdata($key);
        }
        
        return array(
            'user_id' => $this->CI->session->userdata('user_id'),
            'username' => $this->CI->session->userdata('username'),
            'nama_lengkap' => $this->CI->session->userdata('nama_lengkap'),
            'email' => $this->CI->session->userdata('email'),
            'role' => $this->CI->session->userdata('role')
        );
    }
    
    /**
     * Check user role
     */
    public function check_role($required_role)
    {
        $user_role = $this->CI->session->userdata('role');
        
        if (is_array($required_role)) {
            return in_array($user_role, $required_role);
        }
        
        return $user_role == $required_role;
    }
    
    /**
     * Require login
     */
    public function require_login()
    {
        if (!$this->is_logged_in()) {
            redirect('auth/login');
        }
    }
    
    /**
     * Require specific role
     */
    public function require_role($required_role)
    {
        $this->require_login();
        
        if (!$this->check_role($required_role)) {
            show_404();
        }
    }
    
    /**
     * Log user activity
     */
    public function log_activity($user_id, $aktivitas, $tabel_terkait = null, $id_record = null)
    {
        $this->CI->load->model('Log_model');
        
        $data = array(
            'id_user' => $user_id,
            'aktivitas' => $aktivitas,
            'tabel_terkait' => $tabel_terkait,
            'id_record' => $id_record,
            'ip_address' => $this->CI->input->ip_address(),
            'user_agent' => $this->CI->input->user_agent()
        );
        
        $this->CI->Log_model->insert($data);
    }
    
    /**
     * Generate secure password hash
     */
    public function hash_password($password)
    {
        return password_hash($password, PASSWORD_BCRYPT);
    }
    
    /**
     * Verify password
     */
    public function verify_password($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * Generate random password
     */
    public function generate_password($length = 8)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
}