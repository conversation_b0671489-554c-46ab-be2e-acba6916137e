<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Import Data Pelanggan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="#" onclick="downloadTemplate()" class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Download Template
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i> Upload File Excel/CSV
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($upload_error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo $upload_error; ?>
                </div>
                <?php endif; ?>

                <?php echo form_open_multipart('admin/pelanggan/import', 'id="importForm"'); ?>
                
                <!-- File Upload Area -->
                <div class="mb-4">
                    <label for="file_import" class="form-label">Pilih File Excel/CSV <span class="text-danger">*</span></label>
                    <div class="upload-area border-dashed border-2 border-secondary rounded p-4 text-center" 
                         ondrop="dropHandler(event);" ondragover="dragOverHandler(event);" ondragleave="dragLeaveHandler(event);">
                        <input type="file" class="form-control d-none" id="file_import" name="file_import" 
                               accept=".xlsx,.xls,.csv" required onchange="handleFileSelect(this)">
                        
                        <div id="upload-placeholder">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Drop file di sini atau <a href="#" onclick="document.getElementById('file_import').click()">browse</a></h5>
                            <p class="text-muted mb-0">Mendukung format: .xlsx, .xls, .csv (max 2MB)</p>
                        </div>
                        
                        <div id="file-preview" class="d-none">
                            <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                            <h6 id="file-name" class="text-success"></h6>
                            <p id="file-size" class="text-muted mb-2"></p>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile()">
                                <i class="fas fa-times"></i> Hapus File
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Import Options -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip_header" name="skip_header" checked>
                            <label class="form-check-label" for="skip_header">
                                Skip baris pertama (header)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto_generate_no" name="auto_generate_no">
                            <label class="form-check-label" for="auto_generate_no">
                                Generate nomor pelanggan otomatis jika kosong
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action pada Duplikat -->
                <div class="mb-4">
                    <label class="form-label">Jika menemukan nomor pelanggan yang sudah ada:</label>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="duplicate_action" id="skip_duplicate" value="skip" checked>
                                <label class="form-check-label" for="skip_duplicate">
                                    <i class="fas fa-forward text-warning"></i> Skip/Lewati
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="duplicate_action" id="update_duplicate" value="update">
                                <label class="form-check-label" for="update_duplicate">
                                    <i class="fas fa-sync text-info"></i> Update Data
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="duplicate_action" id="stop_duplicate" value="stop">
                                <label class="form-check-label" for="stop_duplicate">
                                    <i class="fas fa-stop text-danger"></i> Stop Import
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <div>
                        <button type="button" class="btn btn-outline-info me-2" onclick="previewImport()">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Sekarang
                        </button>
                    </div>
                </div>
                
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Panduan Import
                </h6>
            </div>
            <div class="card-body">
                <h6>Format File Excel/CSV:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Kolom</th>
                                <th>Wajib</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>No Pelanggan</td>
                                <td><span class="badge bg-success">Ya</span></td>
                            </tr>
                            <tr>
                                <td>Nama Pelanggan</td>
                                <td><span class="badge bg-success">Ya</span></td>
                            </tr>
                            <tr>
                                <td>Alamat</td>
                                <td><span class="badge bg-success">Ya</span></td>
                            </tr>
                            <tr>
                                <td>No HP</td>
                                <td><span class="badge bg-secondary">Tidak</span></td>
                            </tr>
                            <tr>
                                <td>Email</td>
                                <td><span class="badge bg-secondary">Tidak</span></td>
                            </tr>
                            <tr>
                                <td>Tanggal Pemasangan</td>
                                <td><span class="badge bg-success">Ya</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <hr>
                
                <h6>Catatan Penting:</h6>
                <ul class="small text-muted">
                    <li>File maksimal 2MB</li>
                    <li>Format tanggal: DD/MM/YYYY atau YYYY-MM-DD</li>
                    <li>No Pelanggan harus unik</li>
                    <li>Gunakan template untuk hasil terbaik</li>
                    <li>Pastikan tidak ada baris kosong di tengah data</li>
                </ul>
                
                <hr>
                
                <h6>Contoh Data:</h6>
                <div class="bg-light p-2 rounded">
                    <small>
                        <strong>PLG202400001</strong><br>
                        Budi Santoso<br>
                        Jl. Merdeka No. 1, RT 01/RW 01<br>
                        08123456789<br>
                        <EMAIL><br>
                        15/01/2024
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Progress Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line"></i> Progress Import
                </h6>
            </div>
            <div class="card-body">
                <div id="import-progress" class="d-none">
                    <div class="mb-2">
                        <div class="d-flex justify-content-between">
                            <small>Progress Import</small>
                            <small><span id="progress-percent">0</span>%</small>
                        </div>
                        <div class="progress">
                            <div id="progress-bar" class="progress-bar bg-success" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="text-center">
                        <small class="text-muted">
                            <span id="progress-current">0</span> dari <span id="progress-total">0</span> data
                        </small>
                    </div>
                </div>
                
                <div id="import-idle">
                    <div class="text-center text-muted">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p class="mb-0">Siap untuk import data</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Statistik Pelanggan
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-primary mb-0"><?php echo $this->Pelanggan_model->count_all(); ?></h5>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success mb-0"><?php echo $this->Pelanggan_model->count_by_status('aktif'); ?></h5>
                            <small class="text-muted">Aktif</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info mb-0"><?php echo date('m/Y'); ?></h5>
                        <small class="text-muted">Periode</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Preview Data Import</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="preview-content">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" onclick="proceedImport()">
                    <i class="fas fa-upload"></i> Lanjutkan Import
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.upload-area {
    cursor: pointer;
    transition: all 0.3s ease;
}
.upload-area:hover {
    border-color: #007bff !important;
    background-color: #f8f9fa;
}
.upload-area.dragover {
    border-color: #007bff !important;
    background-color: #e3f2fd;
}
.border-dashed {
    border-style: dashed !important;
}
</style>

<script>
// File upload handling
function handleFileSelect(input) {
    const file = input.files[0];
    if (file) {
        showFilePreview(file);
    }
}

function showFilePreview(file) {
    document.getElementById('upload-placeholder').classList.add('d-none');
    document.getElementById('file-preview').classList.remove('d-none');
    
    document.getElementById('file-name').textContent = file.name;
    document.getElementById('file-size').textContent = formatFileSize(file.size);
}

function removeFile() {
    document.getElementById('file_import').value = '';
    document.getElementById('file-preview').classList.add('d-none');
    document.getElementById('upload-placeholder').classList.remove('d-none');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Drag and drop handling
function dragOverHandler(ev) {
    ev.preventDefault();
    ev.currentTarget.classList.add('dragover');
}

function dragLeaveHandler(ev) {
    ev.currentTarget.classList.remove('dragover');
}

function dropHandler(ev) {
    ev.preventDefault();
    ev.currentTarget.classList.remove('dragover');
    
    const files = ev.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        // Check file type
        const allowedTypes = ['.xlsx', '.xls', '.csv'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (allowedTypes.includes(fileExtension)) {
            document.getElementById('file_import').files = files;
            showFilePreview(file);
        } else {
            alert('Tipe file tidak didukung. Gunakan file Excel (.xlsx, .xls) atau CSV (.csv)');
        }
    }
}

// Preview import
function previewImport() {
    const fileInput = document.getElementById('file_import');
    if (!fileInput.files[0]) {
        alert('Pilih file terlebih dahulu');
        return;
    }
    
    // Show loading
    document.getElementById('preview-content').innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <h6>Memproses file...</h6>
            <p class="text-muted">Mohon tunggu sebentar</p>
        </div>
    `;
    
    // Simulate preview (in real implementation, process the file)
    setTimeout(() => {
        document.getElementById('preview-content').innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Preview:</strong> Ditemukan 10 baris data. 2 data duplikat akan dilewati.
            </div>
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>No</th>
                            <th>No Pelanggan</th>
                            <th>Nama</th>
                            <th>Alamat</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>PLG202400010</td>
                            <td>John Doe</td>
                            <td>Jl. Example No. 1</td>
                            <td><span class="badge bg-success">Valid</span></td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>PLG202400011</td>
                            <td>Jane Smith</td>
                            <td>Jl. Example No. 2</td>
                            <td><span class="badge bg-success">Valid</span></td>
                        </tr>
                        <tr class="table-warning">
                            <td>3</td>
                            <td>PLG202400001</td>
                            <td>Budi Santoso</td>
                            <td>Jl. Merdeka No. 1</td>
                            <td><span class="badge bg-warning">Duplikat</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="alert alert-success">
                <strong>Ringkasan:</strong> 8 data valid, 2 data duplikat
            </div>
        `;
    }, 1500);
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Proceed with import
function proceedImport() {
    bootstrap.Modal.getInstance(document.getElementById('previewModal')).hide();
    
    // Show progress
    document.getElementById('import-idle').classList.add('d-none');
    document.getElementById('import-progress').classList.remove('d-none');
    
    // Simulate import progress
    let progress = 0;
    const total = 8;
    const interval = setInterval(() => {
        progress++;
        const percent = Math.round((progress / total) * 100);
        
        document.getElementById('progress-bar').style.width = percent + '%';
        document.getElementById('progress-percent').textContent = percent;
        document.getElementById('progress-current').textContent = progress;
        document.getElementById('progress-total').textContent = total;
        
        if (progress >= total) {
            clearInterval(interval);
            setTimeout(() => {
                alert('Import berhasil! 8 data telah diimport.');
                window.location.href = '<?php echo base_url("admin/pelanggan"); ?>';
            }, 500);
        }
    }, 300);
}

// Download template
function downloadTemplate() {
    // Create sample Excel data
    const csvContent = "data:text/csv;charset=utf-8," 
        + "No Pelanggan,Nama Pelanggan,Alamat,No HP,Email,Tanggal Pemasangan\n"
        + "PLG202400001,Budi Santoso,\"Jl. Merdeka No. 1, RT 01/RW 01\",08123456789,<EMAIL>,15/01/2024\n"
        + "PLG202400002,Siti Aminah,\"Jl. Sudirman No. 25, RT 02/RW 01\",08123456788,<EMAIL>,20/01/2024\n"
        + "PLG202400003,Ahmad Wijaya,\"Jl. Gatot Subroto No. 10, RT 03/RW 02\",08123456787,<EMAIL>,01/02/2024";

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "template_pelanggan.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Form validation
document.getElementById('importForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('file_import');
    if (!fileInput.files[0]) {
        e.preventDefault();
        alert('Pilih file terlebih dahulu');
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengimport...';
    submitBtn.disabled = true;
    
    // Show progress
    document.getElementById('import-idle').classList.add('d-none');
    document.getElementById('import-progress').classList.remove('d-none');
});

// Click upload area to open file dialog
document.querySelector('.upload-area').addEventListener('click', function(e) {
    if (e.target === this || e.target.closest('#upload-placeholder')) {
        document.getElementById('file_import').click();
    }
});
</script>

<?php $this->load->view('templates/footer'); ?>