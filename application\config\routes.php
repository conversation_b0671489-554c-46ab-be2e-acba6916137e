<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'auth';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

// Auth routes
$route['login'] = 'auth/login';
$route['logout'] = 'auth/logout';
$route['dashboard'] = 'dashboard';

// Admin routes
$route['admin/users'] = 'admin/users';
$route['admin/pengaturan'] = 'admin/pengaturan';
$route['admin/laporan'] = 'admin/laporan';

// Admin Pelanggan routes
$route['admin/pelanggan'] = 'admin/pelanggan';
$route['admin/pelanggan/index'] = 'admin/pelanggan/index';
$route['admin/pelanggan/add'] = 'admin/pelanggan/add';
$route['admin/pelanggan/edit/(:num)'] = 'admin/pelanggan/edit/$1';
$route['admin/pelanggan/detail/(:num)'] = 'admin/pelanggan/detail/$1';
$route['admin/pelanggan/delete/(:num)'] = 'admin/pelanggan/delete/$1';
$route['admin/pelanggan/toggle_status/(:num)'] = 'admin/pelanggan/toggle_status/$1';
$route['admin/pelanggan/import'] = 'admin/pelanggan/import';
$route['admin/pelanggan/export'] = 'admin/pelanggan/export';
$route['admin/pelanggan/bulk_action'] = 'admin/pelanggan/bulk_action';
$route['admin/pelanggan/ajax_search'] = 'admin/pelanggan/ajax_search';

// Admin Tarif routes (TERPISAH - BARU!)
$route['admin/tarif'] = 'admin/tarif/index';
$route['admin/tarif/index'] = 'admin/tarif/index';
$route['admin/tarif/form'] = 'admin/tarif/form';
$route['admin/tarif/form/(:num)'] = 'admin/tarif/form/$1';
$route['admin/tarif/delete/(:num)'] = 'admin/tarif/delete/$1';
$route['admin/tarif/toggle_status/(:num)'] = 'admin/tarif/toggle_status/$1';
$route['admin/tarif/bulk_action'] = 'admin/tarif/bulk_action';
$route['admin/tarif/export/(:any)'] = 'admin/tarif/export/$1';
$route['admin/tarif/import'] = 'admin/tarif/import';
$route['admin/tarif/detail/(:num)'] = 'admin/tarif/detail/$1';
$route['admin/tarif/ajax_search'] = 'admin/tarif/ajax_search';

// Admin Pengaturan routes (TANPA TARIF)
$route['admin/pengaturan/sistem'] = 'admin/pengaturan/sistem';
$route['admin/pengaturan/email'] = 'admin/pengaturan/email';
$route['admin/pengaturan/backup'] = 'admin/pengaturan/backup';
$route['admin/pengaturan/backup/create'] = 'admin/pengaturan/create_backup';
$route['admin/pengaturan/backup/download/(:any)'] = 'admin/pengaturan/download_backup/$1';
$route['admin/pengaturan/backup/delete/(:any)'] = 'admin/pengaturan/delete_backup/$1';
$route['admin/pengaturan/info'] = 'admin/pengaturan/info';
$route['admin/pengaturan/clear_cache'] = 'admin/pengaturan/clear_cache';
$route['admin/pengaturan/test_email'] = 'admin/pengaturan/test_email';

$route['admin/pengaturan/email_tagihan'] = 'admin/pengaturan/email_tagihan';
$route['admin/pengaturan/get_tagihan_pelanggan'] = 'admin/pengaturan/get_tagihan_pelanggan';
$route['admin/pengaturan/kirim_email_tagihan'] = 'admin/pengaturan/kirim_email_tagihan';

// Penagih routes
$route['penagih/penggunaan'] = 'penagih/penggunaan';
$route['penagih/pembayaran'] = 'penagih/pembayaran';
$route['penagih/pelanggan'] = 'penagih/pelanggan';

// Pimpinan routes
$route['pimpinan/laporan'] = 'pimpinan/laporan';
$route['pimpinan/laporan/export'] = 'pimpinan/laporan/export';
$route['pimpinan/laporan/print'] = 'pimpinan/laporan/print';
$route['pimpinan/laporan/tahunan'] = 'pimpinan/laporan/tahunan';
$route['pimpinan/laporan/print_tahunan'] = 'pimpinan/laporan/print_tahunan';
$route['pimpinan/statistik'] = 'pimpinan/statistik';