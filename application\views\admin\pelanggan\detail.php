<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Detail Pelanggan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo base_url('admin/pelanggan/edit/' . $pelanggan->id); ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-cog"></i> Aksi
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="<?php echo base_url('admin/pelanggan/toggle_status/' . $pelanggan->id); ?>" 
                           onclick="return confirm('Yakin ingin mengubah status?')">
                        <i class="fas fa-toggle-on me-2"></i>Toggle Status
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="<?php echo base_url('admin/pelanggan/delete/' . $pelanggan->id); ?>" 
                           onclick="return confirm('Yakin ingin menghapus pelanggan ini? Data terkait akan ikut terhapus!')">
                        <i class="fas fa-trash me-2"></i>Hapus Pelanggan
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Profile Card -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="avatar bg-primary text-white rounded-circle mx-auto mb-3" style="width: 80px; height: 80px; line-height: 80px; font-size: 32px;">
                    <?php echo strtoupper(substr($pelanggan->nama_pelanggan, 0, 1)); ?>
                </div>
                <h4 class="mb-1"><?php echo $pelanggan->nama_pelanggan; ?></h4>
                <p class="text-muted mb-2"><?php echo $pelanggan->no_pelanggan; ?></p>
                <span class="badge bg-<?php echo ($pelanggan->status_langganan == 'aktif') ? 'success' : (($pelanggan->status_langganan == 'nonaktif') ? 'warning' : 'danger'); ?> mb-3">
                    <?php echo ucfirst($pelanggan->status_langganan); ?>
                </span>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="mb-0 text-primary"><?php echo format_rupiah($total_tagihan); ?></h5>
                            <small class="text-muted">Total Tagihan</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="mb-0 text-success"><?php echo format_rupiah($total_terbayar); ?></h5>
                            <small class="text-muted">Terbayar</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="mb-0 text-<?php echo ($tunggakan > 0) ? 'danger' : 'success'; ?>"><?php echo format_rupiah($tunggakan); ?></h5>
                        <small class="text-muted">Tunggakan</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi Pelanggan</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="40%"><strong>No Pelanggan:</strong></td>
                                <td><?php echo $pelanggan->no_pelanggan; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Nama Lengkap:</strong></td>
                                <td><?php echo $pelanggan->nama_pelanggan; ?></td>
                            </tr>
                            <tr>
                                <td><strong>No HP:</strong></td>
                                <td>
                                    <?php if ($pelanggan->no_hp): ?>
                                        <a href="tel:<?php echo $pelanggan->no_hp; ?>" class="text-decoration-none">
                                            <i class="fas fa-phone text-success me-1"></i><?php echo $pelanggan->no_hp; ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>
                                    <?php if ($pelanggan->email): ?>
                                        <a href="mailto:<?php echo $pelanggan->email; ?>" class="text-decoration-none">
                                            <i class="fas fa-envelope text-info me-1"></i><?php echo $pelanggan->email; ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td width="40%"><strong>Tanggal Pasang:</strong></td>
                                <td><?php echo format_tanggal($pelanggan->tanggal_pemasangan, 'd F Y'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Status:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo ($pelanggan->status_langganan == 'aktif') ? 'success' : (($pelanggan->status_langganan == 'nonaktif') ? 'warning' : 'danger'); ?>">
                                        <?php echo ucfirst($pelanggan->status_langganan); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Dibuat:</strong></td>
                                <td><?php echo format_tanggal($pelanggan->created_at, 'd/m/Y H:i'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Diupdate:</strong></td>
                                <td><?php echo format_tanggal($pelanggan->updated_at, 'd/m/Y H:i'); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <strong>Alamat Lengkap:</strong>
                        <p class="mt-1 text-muted"><?php echo $pelanggan->alamat; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tabs Content -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="detailTabs" role="tablist">
            <li class="nav-item">
                <button class="nav-link active" id="penggunaan-tab" data-bs-toggle="tab" data-bs-target="#penggunaan" type="button">
                    <i class="fas fa-tachometer-alt me-1"></i>Riwayat Penggunaan
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" id="pembayaran-tab" data-bs-toggle="tab" data-bs-target="#pembayaran" type="button">
                    <i class="fas fa-money-bill-wave me-1"></i>Riwayat Pembayaran
                </button>
            </li>
            <li class="nav-item">
                <button class="nav-link" id="statistik-tab" data-bs-toggle="tab" data-bs-target="#statistik" type="button">
                    <i class="fas fa-chart-bar me-1"></i>Statistik
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="detailTabContent">
            <!-- Tab Riwayat Penggunaan -->
            <div class="tab-pane fade show active" id="penggunaan" role="tabpanel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Riwayat Penggunaan Air (6 Bulan Terakhir)</h6>
                    <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $pelanggan->id); ?>" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Input Penggunaan
                    </a>
                </div>
                
                <?php if (!empty($riwayat_penggunaan)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Periode</th>
                                <th>Meter Awal</th>
                                <th>Meter Akhir</th>
                                <th>Pemakaian</th>
                                <th>Total Tagihan</th>
                                <th>Status Bayar</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($riwayat_penggunaan as $usage): ?>
                            <tr>
                                <td>
                                    <strong><?php echo nama_bulan($usage->bulan) . ' ' . $usage->tahun; ?></strong><br>
                                    <small class="text-muted">Dicatat: <?php echo format_tanggal($usage->tanggal_catat, 'd/m/Y'); ?></small>
                                </td>
                                <td><?php echo number_format($usage->meter_awal); ?> m³</td>
                                <td><?php echo number_format($usage->meter_akhir); ?> m³</td>
                                <td>
                                    <span class="badge bg-info"><?php echo number_format($usage->pemakaian); ?> m³</span>
                                </td>
                                <td><?php echo format_rupiah($usage->total_tagihan); ?></td>
                                <td>
                                    <?php 
                                    $payment_status = 'belum_bayar'; // default
                                    // Check payment status logic here
                                    ?>
                                    <span class="badge bg-<?php echo ($payment_status == 'lunas') ? 'success' : (($payment_status == 'pending') ? 'warning' : 'danger'); ?>">
                                        <?php echo ($payment_status == 'lunas') ? 'Lunas' : (($payment_status == 'pending') ? 'Pending' : 'Belum Bayar'); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" onclick="showUsageDetail(<?php echo $usage->id; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada data penggunaan</h5>
                    <p class="text-muted">Silakan input penggunaan air untuk pelanggan ini</p>
                    <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $pelanggan->id); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Input Penggunaan Pertama
                    </a>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Tab Riwayat Pembayaran -->
            <div class="tab-pane fade" id="pembayaran" role="tabpanel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Riwayat Pembayaran (10 Terakhir)</h6>
                    <button class="btn btn-sm btn-success" onclick="showPaymentSummary()">
                        <i class="fas fa-chart-line"></i> Ringkasan
                    </button>
                </div>
                
                <?php if (!empty($riwayat_pembayaran)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>No Kwitansi</th>
                                <th>Tanggal Bayar</th>
                                <th>Periode</th>
                                <th>Jumlah</th>
                                <th>Metode</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($riwayat_pembayaran as $payment): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $payment->no_kwitansi; ?></strong>
                                </td>
                                <td><?php echo format_tanggal($payment->tanggal_bayar, 'd/m/Y'); ?></td>
                                <td>
                                    <small><?php echo nama_bulan($payment->bulan) . ' ' . $payment->tahun; ?></small>
                                </td>
                                <td><?php echo format_rupiah($payment->jumlah_bayar); ?></td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo ucfirst($payment->metode_bayar); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo ($payment->status_verifikasi == 'verified') ? 'success' : (($payment->status_verifikasi == 'pending') ? 'warning' : 'danger'); ?>">
                                        <?php echo ($payment->status_verifikasi == 'verified') ? 'Terverifikasi' : (($payment->status_verifikasi == 'pending') ? 'Pending' : 'Ditolak'); ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" onclick="showPaymentDetail(<?php echo $payment->id; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada riwayat pembayaran</h5>
                    <p class="text-muted">Pembayaran akan muncul setelah ada penggunaan air dan pembayaran</p>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Tab Statistik -->
            <div class="tab-pane fade" id="statistik" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-tachometer-alt text-primary me-2"></i>Statistik Penggunaan</h6>
                                <canvas id="usageChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6><i class="fas fa-money-bill-wave text-success me-2"></i>Statistik Pembayaran</h6>
                                <canvas id="paymentChart" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h4 class="text-primary"><?php echo count($riwayat_penggunaan); ?></h4>
                                <small class="text-muted">Total Periode</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <?php 
                                $total_pemakaian = 0;
                                foreach ($riwayat_penggunaan as $usage) {
                                    $total_pemakaian += $usage->pemakaian;
                                }
                                $avg_pemakaian = count($riwayat_penggunaan) > 0 ? $total_pemakaian / count($riwayat_penggunaan) : 0;
                                ?>
                                <h4 class="text-info"><?php echo number_format($avg_pemakaian, 1); ?> m³</h4>
                                <small class="text-muted">Rata-rata Pemakaian</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <?php 
                                $payment_percentage = $total_tagihan > 0 ? ($total_terbayar / $total_tagihan) * 100 : 0;
                                ?>
                                <h4 class="text-success"><?php echo number_format($payment_percentage, 1); ?>%</h4>
                                <small class="text-muted">Tingkat Pembayaran</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <?php 
                                $days_since_install = round((time() - strtotime($pelanggan->tanggal_pemasangan)) / (60 * 60 * 24));
                                ?>
                                <h4 class="text-warning"><?php echo number_format($days_since_install); ?></h4>
                                <small class="text-muted">Hari Berlangganan</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for details -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalTitle">Detail</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailModalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Show usage detail
function showUsageDetail(usageId) {
    document.getElementById('detailModalTitle').textContent = 'Detail Penggunaan Air';
    document.getElementById('detailModalBody').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    
    // In real implementation, fetch data via AJAX
    setTimeout(() => {
        document.getElementById('detailModalBody').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr><td><strong>Periode:</strong></td><td>Januari 2024</td></tr>
                        <tr><td><strong>Meter Awal:</strong></td><td>100 m³</td></tr>
                        <tr><td><strong>Meter Akhir:</strong></td><td>125 m³</td></tr>
                        <tr><td><strong>Pemakaian:</strong></td><td>25 m³</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr><td><strong>Tarif:</strong></td><td>Rp 2.500/m³</td></tr>
                        <tr><td><strong>Biaya Admin:</strong></td><td>Rp 5.000</td></tr>
                        <tr><td><strong>Total Tagihan:</strong></td><td>Rp 67.500</td></tr>
                        <tr><td><strong>Petugas:</strong></td><td>Agus Setiawan</td></tr>
                    </table>
                </div>
            </div>
        `;
    }, 500);
    
    new bootstrap.Modal(document.getElementById('detailModal')).show();
}

// Show payment detail
function showPaymentDetail(paymentId) {
    document.getElementById('detailModalTitle').textContent = 'Detail Pembayaran';
    document.getElementById('detailModalBody').innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    
    // In real implementation, fetch data via AJAX
    setTimeout(() => {
        document.getElementById('detailModalBody').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr><td><strong>No Kwitansi:</strong></td><td>KWT20241201001</td></tr>
                        <tr><td><strong>Tanggal Bayar:</strong></td><td>05/12/2024</td></tr>
                        <tr><td><strong>Jumlah Bayar:</strong></td><td>Rp 67.500</td></tr>
                        <tr><td><strong>Metode:</strong></td><td>Transfer Bank</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr><td><strong>Status:</strong></td><td><span class="badge bg-success">Terverifikasi</span></td></tr>
                        <tr><td><strong>Diverifikasi:</strong></td><td>05/12/2024 10:00</td></tr>
                        <tr><td><strong>Petugas:</strong></td><td>Agus Setiawan</td></tr>
                        <tr><td><strong>Catatan:</strong></td><td>Pembayaran sesuai</td></tr>
                    </table>
                </div>
            </div>
        `;
    }, 500);
    
    new bootstrap.Modal(document.getElementById('detailModal')).show();
}

// Show payment summary
function showPaymentSummary() {
    document.getElementById('detailModalTitle').textContent = 'Ringkasan Pembayaran';
    document.getElementById('detailModalBody').innerHTML = `
        <div class="row text-center">
            <div class="col-md-4">
                <div class="border rounded p-3">
                    <h4 class="text-primary"><?php echo format_rupiah($total_tagihan); ?></h4>
                    <small class="text-muted">Total Tagihan</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="border rounded p-3">
                    <h4 class="text-success"><?php echo format_rupiah($total_terbayar); ?></h4>
                    <small class="text-muted">Total Terbayar</small>
                </div>
            </div>
            <div class="col-md-4">
                <div class="border rounded p-3">
                    <h4 class="text-danger"><?php echo format_rupiah($tunggakan); ?></h4>
                    <small class="text-muted">Tunggakan</small>
                </div>
            </div>
        </div>
        <hr>
        <div class="text-center">
            <h6>Tingkat Pembayaran: <span class="text-success"><?php echo number_format($payment_percentage, 1); ?>%</span></h6>
            <div class="progress">
                <div class="progress-bar bg-success" style="width: <?php echo $payment_percentage; ?>%"></div>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('detailModal')).show();
}

// Initialize charts
const usageData = <?php echo json_encode($riwayat_penggunaan); ?>;
const paymentData = <?php echo json_encode($riwayat_pembayaran); ?>;

// Usage Chart
if (usageData.length > 0) {
    const usageChart = new Chart(document.getElementById('usageChart'), {
        type: 'line',
        data: {
            labels: usageData.map(item => item.bulan + '/' + item.tahun),
            datasets: [{
                label: 'Pemakaian (m³)',
                data: usageData.map(item => item.pemakaian),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Payment Chart
if (paymentData.length > 0) {
    const paymentChart = new Chart(document.getElementById('paymentChart'), {
        type: 'bar',
        data: {
            labels: paymentData.map(item => item.no_kwitansi),
            datasets: [{
                label: 'Jumlah Pembayaran',
                data: paymentData.map(item => item.jumlah_bayar),
                backgroundColor: 'rgba(40, 167, 69, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString('id-ID');
                        }
                    }
                }
            }
        }
    });
}
</script>

<?php $this->load->view('templates/footer'); ?>