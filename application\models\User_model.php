<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_model extends CI_Model {
    
    protected $table = 'users';
    
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Get all users
     */
    public function get_all($role = null)
    {
        if ($role) {
            $this->db->where('role', $role);
        }
        $this->db->order_by('nama_lengkap', 'ASC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get user by ID
     */
    public function get_by_id($id)
    {
        return $this->db->get_where($this->table, array('id' => $id))->row();
    }
    
    /**
     * Get user by username
     */
    public function get_by_username($username)
    {
        return $this->db->get_where($this->table, array('username' => $username))->row();
    }
    
    /**
     * Get user by email
     */
    public function get_by_email($email)
    {
        return $this->db->get_where($this->table, array('email' => $email))->row();
    }
    
    /**
     * Insert new user
     */
    public function insert($data)
    {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * Update user
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }
    
    /**
     * Delete user
     */
    public function delete($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }
    
    /**
     * Check if username exists
     */
    public function is_username_exists($username, $exclude_id = null)
    {
        $this->db->where('username', $username);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get($this->table)->num_rows() > 0;
    }
    
    /**
     * Check if email exists
     */
    public function is_email_exists($email, $exclude_id = null)
    {
        $this->db->where('email', $email);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get($this->table)->num_rows() > 0;
    }
    
    /**
     * Count users by role
     */
    public function count_by_role($role)
    {
        $this->db->where('role', $role);
        $this->db->where('status', 'aktif');
        return $this->db->count_all_results($this->table);
    }
    
    /**
     * Get active users
     */
    public function get_active_users($role = null)
    {
        $this->db->where('status', 'aktif');
        if ($role) {
            $this->db->where('role', $role);
        }
        $this->db->order_by('nama_lengkap', 'ASC');
        return $this->db->get($this->table)->result();
    }
    
    public function get_paginated($limit, $offset, $search = null)
    {
        if ($search) {
            $this->db->group_start();
            $this->db->like('username', $search);
            $this->db->or_like('nama_lengkap', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Count all users untuk pagination
     */
    public function count_all_users($search = null)
    {
        if ($search) {
            $this->db->group_start();
            $this->db->like('username', $search);
            $this->db->or_like('nama_lengkap', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        return $this->db->count_all_results($this->table);
    }
    
    /**
     * Search users
     */
    public function search_users($keyword, $limit = 10)
    {
        $this->db->group_start();
        $this->db->like('username', $keyword);
        $this->db->or_like('nama_lengkap', $keyword);
        $this->db->or_like('email', $keyword);
        $this->db->group_end();
        $this->db->limit($limit);
        $this->db->order_by('nama_lengkap', 'ASC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get all users for export
     */
    public function get_all_for_export()
    {
        $this->db->order_by('role', 'ASC');
        $this->db->order_by('nama_lengkap', 'ASC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Get users statistics
     */
    public function get_statistics()
    {
        $stats = array();
        
        // Total by role
        $stats['total_admin'] = $this->count_by_role('admin');
        $stats['total_penagih'] = $this->count_by_role('penagih');
        $stats['total_pimpinan'] = $this->count_by_role('pimpinan');
        
        // Total active/inactive
        $this->db->where('status', 'aktif');
        $stats['total_aktif'] = $this->db->count_all_results($this->table);
        
        $this->db->where('status', 'nonaktif');
        $stats['total_nonaktif'] = $this->db->count_all_results($this->table);
        
        // Total all
        $stats['total_all'] = $this->db->count_all($this->table);
        
        return $stats;
    }
    
    /**
     * Get recent users
     */
    public function get_recent($limit = 5)
    {
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Bulk update status
     */
    public function bulk_update_status($ids, $status)
    {
        $this->db->where_in('id', $ids);
        return $this->db->update($this->table, array('status' => $status));
    }
    
    /**
     * Bulk delete users
     */
    public function bulk_delete($ids)
    {
        $this->db->where_in('id', $ids);
        return $this->db->delete($this->table);
    }
    
    /**
     * Get users by status
     */
    public function get_by_status($status)
    {
        $this->db->where('status', $status);
        $this->db->order_by('nama_lengkap', 'ASC');
        return $this->db->get($this->table)->result();
    }
    
    /**
     * Count total users (alias untuk count_all_users tanpa search)
     */
    public function count_all()
    {
        return $this->db->count_all($this->table);
    }
}