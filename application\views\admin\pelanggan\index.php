<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Data Pelanggan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pelanggan/add'); ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Tambah Pelanggan
            </a>
            <a href="<?php echo base_url('admin/pelanggan/import'); ?>" class="btn btn-sm btn-info">
                <i class="fas fa-upload"></i> Import Excel
            </a>
            <a href="<?php echo base_url('admin/pelanggan/export'); ?>" class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Export Excel
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo number_format($total_pelanggan); ?></div>
                        <div class="small">Total Pelanggan</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo number_format($pelanggan_aktif); ?></div>
                        <div class="small">Aktif</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-times fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo number_format($pelanggan_nonaktif); ?></div>
                        <div class="small">Nonaktif</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-slash fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo number_format($pelanggan_putus); ?></div>
                        <div class="small">Putus</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           placeholder="Cari berdasarkan nomor, nama, atau alamat" 
                           value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="status">
                    <option value="">Semua Status</option>
                    <option value="aktif" <?php echo ($status_filter == 'aktif') ? 'selected' : ''; ?>>Aktif</option>
                    <option value="nonaktif" <?php echo ($status_filter == 'nonaktif') ? 'selected' : ''; ?>>Nonaktif</option>
                    <option value="putus" <?php echo ($status_filter == 'putus') ? 'selected' : ''; ?>>Putus</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Cari
                    </button>
                </div>
            </div>
        </form>
        
        <?php if ($search || $status_filter): ?>
        <div class="mt-2">
            <small class="text-muted">
                Filter aktif: 
                <?php if ($search): ?>
                    Pencarian "<strong><?php echo $search; ?></strong>"
                <?php endif; ?>
                <?php if ($status_filter): ?>
                    Status "<strong><?php echo ucfirst($status_filter); ?></strong>"
                <?php endif; ?>
                <a href="<?php echo base_url('admin/pelanggan'); ?>" class="ms-2">
                    <i class="fas fa-times"></i> Hapus Filter
                </a>
            </small>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bulk Actions -->
<?php if (!empty($pelanggan)): ?>
<div class="card mb-3">
    <div class="card-body py-2">
        <form id="bulkForm" method="post" action="<?php echo base_url('admin/pelanggan/bulk_action'); ?>">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <input type="checkbox" id="selectAll" class="form-check-input me-2">
                        <label for="selectAll" class="form-check-label me-3">Pilih Semua</label>
                        <select name="bulk_action" class="form-select form-select-sm me-2" style="width: auto;">
                            <option value="">Pilih Aksi</option>
                            <option value="activate">Aktifkan</option>
                            <option value="deactivate">Nonaktifkan</option>
                            <option value="delete">Hapus</option>
                        </select>
                        <button type="submit" class="btn btn-sm btn-warning" onclick="return confirmBulkAction()">
                            <i class="fas fa-bolt"></i> Jalankan
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-end">
                        <small class="text-muted">
                            <span id="selectedCount">0</span> item dipilih dari <?php echo count($pelanggan); ?> data
                        </small>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- Pelanggan Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Daftar Pelanggan
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($pelanggan)): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="30">
                            <input type="checkbox" id="selectAllTable" class="form-check-input">
                        </th>
                        <th>No Pelanggan</th>
                        <th>Nama Pelanggan</th>
                        <th>Alamat</th>
                        <th>Kontak</th>
                        <th>Tanggal Pasang</th>
                        <th>Status</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pelanggan as $p): ?>
                    <tr>
                        <td>
                            <input type="checkbox" name="selected_ids[]" value="<?php echo $p->id; ?>" class="form-check-input row-checkbox">
                        </td>
                        <td>
                            <strong><?php echo $p->no_pelanggan; ?></strong>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo $p->nama_pelanggan; ?></strong>
                            </div>
                        </td>
                        <td>
                            <small class="text-muted"><?php echo substr($p->alamat, 0, 50) . (strlen($p->alamat) > 50 ? '...' : ''); ?></small>
                        </td>
                        <td>
                            <?php if ($p->no_hp): ?>
                                <small><i class="fas fa-phone text-success"></i> <?php echo $p->no_hp; ?></small><br>
                            <?php endif; ?>
                            <?php if ($p->email): ?>
                                <small><i class="fas fa-envelope text-info"></i> <?php echo $p->email; ?></small>
                            <?php endif; ?>
                            <?php if (!$p->no_hp && !$p->email): ?>
                                <small class="text-muted">-</small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo format_tanggal($p->tanggal_pemasangan, 'd/m/Y'); ?>
                            </small>
                        </td>
                        <td>
                            <?php 
                            $status_class = '';
                            switch ($p->status_langganan) {
                                case 'aktif':
                                    $status_class = 'bg-success';
                                    break;
                                case 'nonaktif':
                                    $status_class = 'bg-warning';
                                    break;
                                case 'putus':
                                    $status_class = 'bg-danger';
                                    break;
                            }
                            ?>
                            <span class="badge <?php echo $status_class; ?>">
                                <?php echo ucfirst($p->status_langganan); ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('admin/pelanggan/detail/' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-info" title="Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo base_url('admin/pelanggan/edit/' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo base_url('admin/pelanggan/toggle_status/' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-warning" 
                                   title="<?php echo ($p->status_langganan == 'aktif') ? 'Nonaktifkan' : 'Aktifkan'; ?>"
                                   onclick="return confirm('Yakin ingin mengubah status pelanggan ini?')">
                                    <i class="fas fa-<?php echo ($p->status_langganan == 'aktif') ? 'toggle-off' : 'toggle-on'; ?>"></i>
                                </a>
                                <a href="<?php echo base_url('admin/pelanggan/delete/' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-danger" title="Hapus"
                                   onclick="return confirm('Yakin ingin menghapus pelanggan ini? Data terkait akan ikut terhapus!')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($pagination): ?>
        <div class="d-flex justify-content-center mt-3">
            <?php echo $pagination; ?>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada data pelanggan</h5>
            <?php if ($search || $status_filter): ?>
            <p class="text-muted">
                Tidak ditemukan pelanggan dengan filter yang diterapkan
            </p>
            <a href="<?php echo base_url('admin/pelanggan'); ?>" class="btn btn-primary">
                <i class="fas fa-list"></i> Lihat Semua Pelanggan
            </a>
            <?php else: ?>
            <p class="text-muted">Silakan tambah pelanggan pertama</p>
            <a href="<?php echo base_url('admin/pelanggan/add'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Pelanggan
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Stats Modal -->
<div class="modal fade" id="quickStatsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Statistik Pelanggan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="statusChart" height="200"></canvas>
                    </div>
                    <div class="col-md-6">
                        <h6>Ringkasan Status</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-circle text-success"></i> Aktif</span>
                                <strong><?php echo $pelanggan_aktif; ?></strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-circle text-warning"></i> Nonaktif</span>
                                <strong><?php echo $pelanggan_nonaktif; ?></strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><i class="fas fa-circle text-danger"></i> Putus</span>
                                <strong><?php echo $pelanggan_putus; ?></strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>Total</strong></span>
                                <strong><?php echo $total_pelanggan; ?></strong>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <div class="dropup">
        <button type="button" class="btn btn-primary btn-lg rounded-circle dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-plus"></i>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="<?php echo base_url('admin/pelanggan/add'); ?>">
                <i class="fas fa-plus me-2"></i>Tambah Pelanggan
            </a></li>
            <li><a class="dropdown-item" href="<?php echo base_url('admin/pelanggan/import'); ?>">
                <i class="fas fa-upload me-2"></i>Import Excel
            </a></li>
            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#quickStatsModal">
                <i class="fas fa-chart-pie me-2"></i>Lihat Statistik
            </a></li>
        </ul>
    </div>
</div>

<script>
// Bulk selection functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.row-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
    updateSelectedCount();
});

document.getElementById('selectAllTable').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.row-checkbox');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
    document.getElementById('selectAll').checked = this.checked;
    updateSelectedCount();
});

// Update selected count
document.querySelectorAll('.row-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

function updateSelectedCount() {
    const selected = document.querySelectorAll('.row-checkbox:checked').length;
    document.getElementById('selectedCount').textContent = selected;
    
    // Update select all checkboxes
    const total = document.querySelectorAll('.row-checkbox').length;
    const selectAll = document.getElementById('selectAll');
    const selectAllTable = document.getElementById('selectAllTable');
    
    if (selected === 0) {
        selectAll.indeterminate = false;
        selectAll.checked = false;
        selectAllTable.indeterminate = false;
        selectAllTable.checked = false;
    } else if (selected === total) {
        selectAll.indeterminate = false;
        selectAll.checked = true;
        selectAllTable.indeterminate = false;
        selectAllTable.checked = true;
    } else {
        selectAll.indeterminate = true;
        selectAllTable.indeterminate = true;
    }
}

// Confirm bulk action
function confirmBulkAction() {
    const action = document.querySelector('select[name="bulk_action"]').value;
    const selected = document.querySelectorAll('.row-checkbox:checked').length;
    
    if (!action) {
        alert('Pilih aksi yang akan dilakukan');
        return false;
    }
    
    if (selected === 0) {
        alert('Pilih minimal satu pelanggan');
        return false;
    }
    
    let message = '';
    switch (action) {
        case 'activate':
            message = `Yakin ingin mengaktifkan ${selected} pelanggan?`;
            break;
        case 'deactivate':
            message = `Yakin ingin menonaktifkan ${selected} pelanggan?`;
            break;
        case 'delete':
            message = `Yakin ingin menghapus ${selected} pelanggan? Data terkait akan ikut terhapus!`;
            break;
    }
    
    return confirm(message);
}

// Status Chart
const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: {
        labels: ['Aktif', 'Nonaktif', 'Putus'],
        datasets: [{
            data: [<?php echo $pelanggan_aktif; ?>, <?php echo $pelanggan_nonaktif; ?>, <?php echo $pelanggan_putus; ?>],
            backgroundColor: ['#28a745', '#ffc107', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Auto hide alerts
setTimeout(function() {
    $('.alert').fadeOut('slow');
}, 5000);

// Search form enhancement
document.querySelector('input[name="search"]').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});
</script>

<?php $this->load->view('templates/footer'); ?>