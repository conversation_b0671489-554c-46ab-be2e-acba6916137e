<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i>Verifikasi Pembayaran</h1>
</div>

<?php echo show_flash_message(); ?>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select class="form-select" name="status">
                    <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>Pending</option>
                    <option value="verified" <?php echo ($status_filter == 'verified') ? 'selected' : ''; ?>>Verified</option>
                    <option value="rejected" <?php echo ($status_filter == 'rejected') ? 'selected' : ''; ?>>Rejected</option>
                    <option value="all" <?php echo ($status_filter == 'all') ? 'selected' : ''; ?>>Semua</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">Cari</label>
                <input type="text" class="form-control" name="search" value="<?php echo $search; ?>" placeholder="Nama, No. pelanggan, No. kwitansi...">
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i> Filter</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Stats -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-2"></i>
                <h4><?php echo $total_pending; ?></h4>
                <small>Pending</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4><?php echo $total_verified; ?></h4>
                <small>Verified</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);">
            <div class="card-body text-center">
                <i class="fas fa-times-circle fa-2x mb-2"></i>
                <h4><?php echo $total_rejected; ?></h4>
                <small>Rejected</small>
            </div>
        </div>
    </div>
</div>

<!-- Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Pembayaran - <?php echo ucfirst($status_filter); ?></h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($pembayaran)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>No</th>
                        <th>Pelanggan</th>
                        <th>Periode</th>
                        <th>No. Kwitansi</th>
                        <th>Jumlah Bayar</th>
                        <th>Metode</th>
                        <th>Status</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pembayaran as $index => $p): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td>
                            <strong><?php echo $p->nama_pelanggan; ?></strong><br>
                            <small class="text-muted"><?php echo $p->no_pelanggan; ?></small>
                        </td>
                        <td><?php echo nama_bulan($p->bulan) . ' ' . $p->tahun; ?></td>
                        <td><span class="text-primary"><?php echo $p->no_kwitansi; ?></span></td>
                        <td>
                            <strong><?php echo format_rupiah($p->jumlah_bayar); ?></strong><br>
                            <small class="text-muted">Tagihan: <?php echo format_rupiah($p->total_tagihan); ?></small>
                        </td>
                        <td>
                            <?php
                            $metode_class = '';
                            switch ($p->metode_bayar) {
                                case 'tunai': $metode_class = 'success'; break;
                                case 'transfer': $metode_class = 'primary'; break;
                                case 'qris': $metode_class = 'info'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $metode_class; ?>"><?php echo ucfirst($p->metode_bayar); ?></span>
                        </td>
                        <td>
                            <?php
                            $status_class = '';
                            switch ($p->status_verifikasi) {
                                case 'verified': $status_class = 'success'; break;
                                case 'pending': $status_class = 'warning'; break;
                                case 'rejected': $status_class = 'danger'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $status_class; ?>"><?php echo ucfirst($p->status_verifikasi); ?></span>
                        </td>
                        <td><?php echo format_tanggal($p->tanggal_bayar); ?></td>
                        <td>
                            <a href="<?php echo base_url('penagih/pembayaran/verifikasi/' . $p->id); ?>" 
                               class="btn btn-sm btn-primary">
                                <i class="fas fa-eye"></i> Verifikasi
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada pembayaran</h5>
            <p class="text-muted">Belum ada pembayaran dengan status <?php echo $status_filter; ?></p>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php $this->load->view('templates/footer'); ?>