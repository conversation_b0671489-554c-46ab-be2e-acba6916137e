<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_login();
        
        // Load models yang diperlukan
        $this->load->model('User_model');
        $this->load->model('Pelanggan_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Pembayaran_model');
        $this->load->model('Tarif_model');
    }

    /**
     * Dashboard utama - redirect berdasarkan role
     */
    public function index()
    {
        $role = $this->auth_lib->get_user_data('role');
        
        switch ($role) {
            case 'admin':
                $this->admin_dashboard();
                break;
            case 'penagih':
                $this->penagih_dashboard();
                break;
            case 'pimpinan':
                $this->pimpinan_dashboard();
                break;
            default:
                $this->auth_lib->logout();
                redirect('auth/login');
        }
    }

    /**
     * Dashboard untuk Admin
     */
    private function admin_dashboard()
    {
        $data['title'] = 'Dashboard Admin - PDAM System';
        $data['user_data'] = $this->auth_lib->get_user_data();
        
        // Statistik dasar
        $data['total_pelanggan'] = $this->get_safe_count('Pelanggan_model', 'count_all');
        $data['pelanggan_aktif'] = $this->get_safe_count('Pelanggan_model', 'count_by_status', 'aktif');
        $data['total_penagih'] = $this->get_safe_count('User_model', 'count_by_role', 'penagih');
        
        // Data keuangan bulan ini
        $bulan_ini = date('m');
        $tahun_ini = date('Y');
        
        $data['pembayaran_bulan_ini'] = $this->get_safe_count('Pembayaran_model', 'count_by_month', [$bulan_ini, $tahun_ini]);
        $data['total_tagihan_bulan_ini'] = $this->get_safe_sum('Penggunaan_model', 'sum_tagihan_by_month', [$bulan_ini, $tahun_ini]);
        $data['total_pembayaran_bulan_ini'] = $this->get_safe_sum('Pembayaran_model', 'sum_by_month', [$bulan_ini, $tahun_ini]);
        $data['pending_verifikasi'] = $this->get_safe_count('Pembayaran_model', 'count_pending');
        
        // Data recent payments dengan error handling
        try {
            $data['recent_payments'] = $this->Pembayaran_model->get_recent(5);
        } catch (Exception $e) {
            $data['recent_payments'] = array();
        }
        
        $this->load->view('admin/dashboard', $data);
    }

    /**
     * Dashboard untuk Penagih
     */
    private function penagih_dashboard()
    {
        $data['title'] = 'Dashboard Penagih - PDAM System';
        $data['user_data'] = $this->auth_lib->get_user_data();
        
        $user_id = $this->auth_lib->get_user_data('user_id');
        $bulan_ini = date('m');
        $tahun_ini = date('Y');
        
        // Statistik untuk penagih
        $data['total_pelanggan'] = $this->get_safe_count('Pelanggan_model', 'count_all');
        $data['pencatatan_bulan_ini'] = $this->get_safe_count('Penggunaan_model', 'count_by_petugas_month', [$user_id, $bulan_ini, $tahun_ini]);
        $data['verifikasi_pending'] = $this->get_safe_count('Pembayaran_model', 'count_pending');
        $data['verifikasi_hari_ini'] = $this->get_safe_count('Pembayaran_model', 'count_verified_today_by_petugas', $user_id);
        
        // Data tugas hari ini dengan error handling
        try {
            $data['pelanggan_belum_dicatat'] = $this->Penggunaan_model->get_pelanggan_belum_dicatat($bulan_ini, $tahun_ini);
        } catch (Exception $e) {
            $data['pelanggan_belum_dicatat'] = array();
        }
        
        try {
            $data['pembayaran_perlu_verifikasi'] = $this->Pembayaran_model->get_pending(10);
        } catch (Exception $e) {
            $data['pembayaran_perlu_verifikasi'] = array();
        }
        
        $this->load->view('penagih/dashboard', $data);
    }

    /**
     * Dashboard untuk Pimpinan
     */
    private function pimpinan_dashboard()
    {
        $data['title'] = 'Dashboard Pimpinan - PDAM System';
        $data['user_data'] = $this->auth_lib->get_user_data();
        
        $bulan_ini = date('m');
        $tahun_ini = date('Y');
        
        // Overview statistik
        $data['total_pelanggan'] = $this->get_safe_count('Pelanggan_model', 'count_all');
        $data['pelanggan_aktif'] = $this->get_safe_count('Pelanggan_model', 'count_by_status', 'aktif');
        
        // Keuangan bulan ini
        $data['total_tagihan_bulan_ini'] = $this->get_safe_sum('Penggunaan_model', 'sum_tagihan_by_month', [$bulan_ini, $tahun_ini]);
        $data['total_terbayar_bulan_ini'] = $this->get_safe_sum('Pembayaran_model', 'sum_verified_by_month', [$bulan_ini, $tahun_ini]);
        
        // Hitung persentase pembayaran
        if ($data['total_tagihan_bulan_ini'] > 0) {
            $data['persentase_pembayaran'] = round(($data['total_terbayar_bulan_ini'] / $data['total_tagihan_bulan_ini']) * 100, 2);
        } else {
            $data['persentase_pembayaran'] = 0;
        }
        
        // Data grafik dengan error handling
        try {
            $data['grafik_pembayaran'] = $this->Pembayaran_model->get_payment_chart_data(6);
        } catch (Exception $e) {
            $data['grafik_pembayaran'] = $this->get_dummy_chart_data();
        }
        
        try {
            $data['top_areas'] = $this->Pembayaran_model->get_top_payment_areas(5);
        } catch (Exception $e) {
            $data['top_areas'] = array();
        }
        
        $this->load->view('pimpinan/dashboard', $data);
    }

    /**
     * Helper function untuk safely get count data
     */
    private function get_safe_count($model, $method, $params = null)
    {
        try {
            if ($params === null) {
                return $this->$model->$method();
            } elseif (is_array($params)) {
                return call_user_func_array(array($this->$model, $method), $params);
            } else {
                return $this->$model->$method($params);
            }
        } catch (Exception $e) {
            log_message('error', 'Dashboard error in ' . $model . '::' . $method . ' - ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Helper function untuk safely get sum data
     */
    private function get_safe_sum($model, $method, $params = null)
    {
        try {
            if ($params === null) {
                return $this->$model->$method();
            } elseif (is_array($params)) {
                return call_user_func_array(array($this->$model, $method), $params);
            } else {
                return $this->$model->$method($params);
            }
        } catch (Exception $e) {
            log_message('error', 'Dashboard error in ' . $model . '::' . $method . ' - ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Generate dummy chart data untuk fallback
     */
    private function get_dummy_chart_data()
    {
        $data = array();
        for ($i = 5; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i months"));
            $bulan = date('m', strtotime($date));
            $tahun = date('Y', strtotime($date));
            
            $data[] = array(
                'bulan' => nama_bulan($bulan) . ' ' . $tahun,
                'bulan_num' => $bulan,
                'tahun' => $tahun,
                'total' => 0
            );
        }
        return $data;
    }

    /**
     * Test page untuk development/debugging
     */
    public function test()
    {
        if (ENVIRONMENT !== 'development') {
            show_404();
            return;
        }
        
        echo "<h2>PDAM Dashboard Test</h2>";
        echo "<p><strong>Environment:</strong> " . ENVIRONMENT . "</p>";
        echo "<p><strong>Current User:</strong> " . $this->auth_lib->get_user_data('nama_lengkap') . "</p>";
        echo "<p><strong>Role:</strong> " . $this->auth_lib->get_user_data('role') . "</p>";
        echo "<p><strong>Base URL:</strong> " . base_url() . "</p>";
        
        echo "<h3>Database Connection Test:</h3>";
        if ($this->db->conn_id) {
            echo "<p style='color: green;'>✅ Database connected successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Database connection failed</p>";
        }
        
        echo "<h3>Models Test:</h3>";
        $models = array('User_model', 'Pelanggan_model', 'Penggunaan_model', 'Pembayaran_model', 'Tarif_model');
        
        foreach ($models as $model) {
            try {
                $this->load->model($model);
                echo "<p style='color: green;'>✅ {$model} loaded successfully</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ {$model} failed: " . $e->getMessage() . "</p>";
            }
        }
        
        echo "<h3>Data Count Test:</h3>";
        echo "<ul>";
        echo "<li>Total Pelanggan: " . $this->get_safe_count('Pelanggan_model', 'count_all') . "</li>";
        echo "<li>Pelanggan Aktif: " . $this->get_safe_count('Pelanggan_model', 'count_by_status', 'aktif') . "</li>";
        echo "<li>Total Users Admin: " . $this->get_safe_count('User_model', 'count_by_role', 'admin') . "</li>";
        echo "<li>Total Users Penagih: " . $this->get_safe_count('User_model', 'count_by_role', 'penagih') . "</li>";
        echo "<li>Total Users Pimpinan: " . $this->get_safe_count('User_model', 'count_by_role', 'pimpinan') . "</li>";
        echo "<li>Pending Verifikasi: " . $this->get_safe_count('Pembayaran_model', 'count_pending') . "</li>";
        echo "</ul>";
        
        echo "<h3>Helper Functions Test:</h3>";
        echo "<ul>";
        echo "<li>Format Rupiah: " . format_rupiah(1500000) . "</li>";
        echo "<li>Format Tanggal: " . format_tanggal(date('Y-m-d')) . "</li>";
        echo "<li>Nama Bulan: " . nama_bulan(date('m')) . "</li>";
        echo "<li>Generate Kwitansi: " . generate_kwitansi() . "</li>";
        echo "<li>Generate No Pelanggan: " . generate_no_pelanggan() . "</li>";
        echo "</ul>";
        
        echo "<h3>Session Data:</h3>";
        echo "<pre>";
        print_r($this->session->userdata());
        echo "</pre>";
        
        echo "<hr>";
        echo "<p><a href='" . base_url('dashboard') . "'>← Back to Dashboard</a></p>";
        echo "<p><a href='" . base_url('auth/logout') . "'>Logout</a></p>";
    }

    /**
     * AJAX method untuk refresh dashboard data
     */
    public function refresh_data()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
            return;
        }
        
        $role = $this->auth_lib->get_user_data('role');
        $bulan_ini = date('m');
        $tahun_ini = date('Y');
        
        $response = array();
        
        switch ($role) {
            case 'admin':
                $response = array(
                    'total_pelanggan' => $this->get_safe_count('Pelanggan_model', 'count_all'),
                    'pelanggan_aktif' => $this->get_safe_count('Pelanggan_model', 'count_by_status', 'aktif'),
                    'pending_verifikasi' => $this->get_safe_count('Pembayaran_model', 'count_pending'),
                    'total_tagihan' => $this->get_safe_sum('Penggunaan_model', 'sum_tagihan_by_month', [$bulan_ini, $tahun_ini]),
                    'total_pembayaran' => $this->get_safe_sum('Pembayaran_model', 'sum_by_month', [$bulan_ini, $tahun_ini])
                );
                break;
                
            case 'penagih':
                $user_id = $this->auth_lib->get_user_data('user_id');
                $response = array(
                    'pencatatan_bulan_ini' => $this->get_safe_count('Penggunaan_model', 'count_by_petugas_month', [$user_id, $bulan_ini, $tahun_ini]),
                    'verifikasi_pending' => $this->get_safe_count('Pembayaran_model', 'count_pending'),
                    'verifikasi_hari_ini' => $this->get_safe_count('Pembayaran_model', 'count_verified_today_by_petugas', $user_id)
                );
                break;
                
            case 'pimpinan':
                $total_tagihan = $this->get_safe_sum('Penggunaan_model', 'sum_tagihan_by_month', [$bulan_ini, $tahun_ini]);
                $total_terbayar = $this->get_safe_sum('Pembayaran_model', 'sum_verified_by_month', [$bulan_ini, $tahun_ini]);
                $response = array(
                    'total_tagihan' => $total_tagihan,
                    'total_terbayar' => $total_terbayar,
                    'persentase_pembayaran' => $total_tagihan > 0 ? round(($total_terbayar / $total_tagihan) * 100, 2) : 0
                );
                break;
        }
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response));
    }
}