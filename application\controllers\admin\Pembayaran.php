<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pembayaran extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_role('admin'); // <PERSON>ya admin yang bisa akses
        
        $this->load->model('Pembayaran_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Pelanggan_model');
        $this->load->helper(['form', 'url', 'date']);
        $this->load->library(['form_validation', 'session']);
    }

    /**
     * Halaman utama pembayaran
     */
    public function index()
    {
        $data['title'] = 'Data Pembayaran - PDAM System';
        
        // Filter
        $status = $this->input->get('status') ?: 'all';
        $search = $this->input->get('search');
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        
        // Get data pembayaran
        $this->db->select('p.*, pa.bulan, pa.tahun, pa.total_tagihan, pa.pemakaian, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat');
        $this->db->from('pembayaran p');
        $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        
        // Filter by period
        $this->db->where('pa.bulan', $bulan);
        $this->db->where('pa.tahun', $tahun);
        
        // Filter by status
        if ($status != 'all') {
            $this->db->where('p.status_verifikasi', $status);
        }
        
        // Search
        if ($search) {
            $this->db->group_start();
            $this->db->like('pel.nama_pelanggan', $search);
            $this->db->or_like('pel.no_pelanggan', $search);
            $this->db->or_like('p.no_kwitansi', $search);
            $this->db->group_end();
        }
        
        $this->db->order_by('p.created_at', 'DESC');
        $data['pembayaran'] = $this->db->get()->result();
        
        // Statistics - Fixed SQL syntax with error handling
        try {
            // Count pending
            $this->db->select('COUNT(*) as total');
            $this->db->from('pembayaran p');
            $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->where('p.status_verifikasi', 'pending');
            $result = $this->db->get()->row();
            $data['total_pending'] = $result ? $result->total : 0;

            // Count verified
            $this->db->select('COUNT(*) as total');
            $this->db->from('pembayaran p');
            $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->where('p.status_verifikasi', 'verified');
            $result = $this->db->get()->row();
            $data['total_verified'] = $result ? $result->total : 0;

            // Sum total terbayar
            $this->db->select('SUM(p.jumlah_bayar) as total_bayar');
            $this->db->from('pembayaran p');
            $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
            $this->db->where('pa.bulan', $bulan);
            $this->db->where('pa.tahun', $tahun);
            $this->db->where('p.status_verifikasi', 'verified');
            $result = $this->db->get()->row();
            $data['total_terbayar'] = ($result && $result->total_bayar) ? $result->total_bayar : 0;

        } catch (Exception $e) {
            log_message('error', 'Error in pembayaran statistics: ' . $e->getMessage());
            $data['total_pending'] = 0;
            $data['total_verified'] = 0;
            $data['total_terbayar'] = 0;
        }
        
        $data['status_filter'] = $status;
        $data['search'] = $search;
        $data['bulan_filter'] = $bulan;
        $data['tahun_filter'] = $tahun;
        
        $this->load->view('admin/pembayaran/index', $data);
    }

    /**
     * Form input pembayaran baru
     */
    public function add()
    {
        $data['title'] = 'Input Pembayaran Baru';
        
        // Get tagihan yang belum dibayar
        $bulan = $this->input->get('bulan') ?: date('m');
        $tahun = $this->input->get('tahun') ?: date('Y');
        
        $this->db->select('pa.*, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat, pel.no_hp');
        $this->db->from('penggunaan_air pa');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        $this->db->join('pembayaran p', 'p.id_penggunaan = pa.id', 'LEFT');
        $this->db->where('pa.bulan', $bulan);
        $this->db->where('pa.tahun', $tahun);
        $this->db->where('p.id IS NULL'); // Belum ada pembayaran
        $this->db->order_by('pel.nama_pelanggan', 'ASC');
        $data['tagihan_list'] = $this->db->get()->result();
        
        $data['bulan_filter'] = $bulan;
        $data['tahun_filter'] = $tahun;
        
        // Pre-select tagihan from URL
        $selected_tagihan = $this->input->get('tagihan');
        $data['selected_tagihan'] = $selected_tagihan;
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('id_penggunaan', 'Tagihan', 'required');
            $this->form_validation->set_rules('jumlah_bayar', 'Jumlah Bayar', 'required|numeric');
            $this->form_validation->set_rules('metode_bayar', 'Metode Bayar', 'required');
            $this->form_validation->set_rules('tanggal_bayar', 'Tanggal Bayar', 'required');
            
            if ($this->form_validation->run() == TRUE) {
                $id_penggunaan = $this->input->post('id_penggunaan');
                $jumlah_bayar = $this->input->post('jumlah_bayar');
                $metode_bayar = $this->input->post('metode_bayar');
                $tanggal_bayar = $this->input->post('tanggal_bayar');
                $keterangan = $this->input->post('keterangan');
                
                // Generate nomor kwitansi
                $no_kwitansi = 'KWT-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                
                // Check if already exists
                $this->db->where('id_penggunaan', $id_penggunaan);
                $existing = $this->db->get('pembayaran')->row();
                
                if ($existing) {
                    $this->session->set_flashdata('error', 'Tagihan ini sudah dibayar.');
                } else {
                    // Prepare data - only core fields that should exist
                    $pembayaran_data = [
                        'id_penggunaan' => $id_penggunaan,
                        'no_kwitansi' => $no_kwitansi,
                        'jumlah_bayar' => $jumlah_bayar,
                        'metode_bayar' => $metode_bayar,
                        'tanggal_bayar' => $tanggal_bayar,
                        'status_verifikasi' => 'pending'
                    ];

                    // Try to add optional fields with error handling
                    try {
                        // Check if keterangan field exists
                        $fields = $this->db->list_fields('pembayaran');
                        if (in_array('keterangan', $fields)) {
                            $pembayaran_data['keterangan'] = $keterangan;
                        }
                        if (in_array('petugas_input', $fields)) {
                            $pembayaran_data['petugas_input'] = $this->session->userdata('user_id');
                        }
                        if (in_array('created_at', $fields)) {
                            $pembayaran_data['created_at'] = date('Y-m-d H:i:s');
                        }
                    } catch (Exception $e) {
                        log_message('error', 'Error checking table fields: ' . $e->getMessage());
                    }
                    
                    if ($this->db->insert('pembayaran', $pembayaran_data)) {
                        // Log aktivitas
                        $log_data = [
                            'id_user' => $this->session->userdata('user_id'),
                            'aktivitas' => 'Input pembayaran baru: ' . $no_kwitansi,
                            'tabel_terkait' => 'pembayaran',
                            'id_record' => $this->db->insert_id(),
                            'ip_address' => $this->input->ip_address(),
                            'user_agent' => $this->input->user_agent()
                        ];
                        $this->db->insert('log_aktivitas', $log_data);
                        
                        $this->session->set_flashdata('success', 'Pembayaran berhasil diinput dengan nomor kwitansi: ' . $no_kwitansi);
                        redirect('admin/pembayaran');
                    } else {
                        $this->session->set_flashdata('error', 'Gagal menyimpan data pembayaran.');
                    }
                }
            }
        }
        
        $this->load->view('admin/pembayaran/add', $data);
    }

    /**
     * AJAX: Get detail tagihan
     */
    public function get_tagihan_detail()
    {
        $id_penggunaan = $this->input->post('id_penggunaan');
        
        $this->db->select('pa.*, pel.nama_pelanggan, pel.no_pelanggan, pel.alamat, t.nama_tarif, t.tarif_per_m3');
        $this->db->from('penggunaan_air pa');
        $this->db->join('pelanggan pel', 'pel.id = pa.id_pelanggan');
        $this->db->join('tarif t', 't.id = pa.id_tarif');
        $this->db->where('pa.id', $id_penggunaan);
        $tagihan = $this->db->get()->row();
        
        if ($tagihan) {
            echo json_encode([
                'success' => true,
                'data' => [
                    'nama_pelanggan' => $tagihan->nama_pelanggan,
                    'no_pelanggan' => $tagihan->no_pelanggan,
                    'alamat' => $tagihan->alamat,
                    'periode' => nama_bulan($tagihan->bulan) . ' ' . $tagihan->tahun,
                    'pemakaian' => $tagihan->pemakaian,
                    'total_tagihan' => $tagihan->total_tagihan,
                    'formatted_tagihan' => format_rupiah($tagihan->total_tagihan),
                    'tarif_info' => $tagihan->nama_tarif . ' - ' . format_rupiah($tagihan->tarif_per_m3) . '/m³'
                ]
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Tagihan tidak ditemukan']);
        }
    }

    /**
     * Detail pembayaran
     */
    public function detail($id)
    {
        $data['title'] = 'Detail Pembayaran';
        
        $data['pembayaran'] = $this->Pembayaran_model->get_by_id($id);

        if (!$data['pembayaran']) {
            $this->session->set_flashdata('error', 'Data pembayaran tidak ditemukan.');
            redirect('admin/pembayaran');
        }

        // Debug: Log available fields
        log_message('debug', 'Pembayaran object fields: ' . print_r($data['pembayaran'], true));
        
        $this->load->view('admin/pembayaran/detail', $data);
    }

    /**
     * Edit pembayaran
     */
    public function edit($id)
    {
        $data['title'] = 'Edit Pembayaran';
        
        $data['pembayaran'] = $this->Pembayaran_model->get_by_id($id);
        
        if (!$data['pembayaran']) {
            $this->session->set_flashdata('error', 'Data pembayaran tidak ditemukan.');
            redirect('admin/pembayaran');
        }
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('jumlah_bayar', 'Jumlah Bayar', 'required|numeric');
            $this->form_validation->set_rules('metode_bayar', 'Metode Bayar', 'required');
            $this->form_validation->set_rules('tanggal_bayar', 'Tanggal Bayar', 'required');
            
            if ($this->form_validation->run() == TRUE) {
                // Prepare update data - only core fields
                $update_data = [
                    'jumlah_bayar' => $this->input->post('jumlah_bayar'),
                    'metode_bayar' => $this->input->post('metode_bayar'),
                    'tanggal_bayar' => $this->input->post('tanggal_bayar')
                ];

                // Try to add optional fields with error handling
                try {
                    $fields = $this->db->list_fields('pembayaran');
                    if (in_array('keterangan', $fields)) {
                        $update_data['keterangan'] = $this->input->post('keterangan');
                    }
                    if (in_array('updated_at', $fields)) {
                        $update_data['updated_at'] = date('Y-m-d H:i:s');
                    }
                } catch (Exception $e) {
                    log_message('error', 'Error checking table fields for update: ' . $e->getMessage());
                }
                
                $this->db->where('id', $id);
                if ($this->db->update('pembayaran', $update_data)) {
                    $this->session->set_flashdata('success', 'Data pembayaran berhasil diupdate.');
                    redirect('admin/pembayaran');
                } else {
                    $this->session->set_flashdata('error', 'Gagal mengupdate data pembayaran.');
                }
            }
        }
        
        $this->load->view('admin/pembayaran/edit', $data);
    }

    /**
     * Delete pembayaran
     */
    public function delete($id)
    {
        $pembayaran = $this->Pembayaran_model->get_by_id($id);
        
        if (!$pembayaran) {
            $this->session->set_flashdata('error', 'Data pembayaran tidak ditemukan.');
            redirect('admin/pembayaran');
        }
        
        if ($this->db->delete('pembayaran', ['id' => $id])) {
            $this->session->set_flashdata('success', 'Data pembayaran berhasil dihapus.');
        } else {
            $this->session->set_flashdata('error', 'Gagal menghapus data pembayaran.');
        }
        
        redirect('admin/pembayaran');
    }
}
