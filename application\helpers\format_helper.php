<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Format Helper
 * Helper untuk formatting data umum
 */

if (!function_exists('format_rupiah')) {
    /**
     * Format angka ke format rupiah
     * @param int|float $angka
     * @return string
     */
    function format_rupiah($angka)
    {
        return 'Rp ' . number_format($angka, 0, ',', '.');
    }
}

if (!function_exists('nama_bulan')) {
    /**
     * Konversi nomor bulan ke nama bulan
     * @param int $bulan (1-12)
     * @return string
     */
    function nama_bulan($bulan)
    {
        $nama_bulan = [
            1 => 'Januari',
            2 => 'Februari', 
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];
        
        return isset($nama_bulan[$bulan]) ? $nama_bulan[$bulan] : 'Invalid';
    }
}

if (!function_exists('format_tanggal')) {
    /**
     * Format tanggal ke bahasa Indonesia
     * @param string $tanggal (Y-m-d)
     * @return string
     */
    function format_tanggal($tanggal)
    {
        $timestamp = strtotime($tanggal);
        $hari = date('d', $timestamp);
        $bulan = nama_bulan(date('n', $timestamp));
        $tahun = date('Y', $timestamp);
        
        return $hari . ' ' . $bulan . ' ' . $tahun;
    }
}

if (!function_exists('format_angka')) {
    /**
     * Format angka dengan separator
     * @param int|float $angka
     * @return string
     */
    function format_angka($angka)
    {
        return number_format($angka, 0, ',', '.');
    }
}

if (!function_exists('format_persen')) {
    /**
     * Format angka ke persentase
     * @param float $angka
     * @param int $desimal
     * @return string
     */
    function format_persen($angka, $desimal = 1)
    {
        return number_format($angka, $desimal, ',', '.') . '%';
    }
}