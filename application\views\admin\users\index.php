<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Manajemen User</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/users/add'); ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Tambah User
            </a>
            <a href="<?php echo base_url('admin/users/export'); ?>" class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Export Excel
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo $total_admin; ?></div>
                        <div class="small">Admin</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo $total_penagih; ?></div>
                        <div class="small">Penagih</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-cog fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo $total_pimpinan; ?></div>
                        <div class="small">Pimpinan</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-secondary">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="h5 mb-0"><?php echo $total_admin + $total_penagih + $total_pimpinan; ?></div>
                        <div class="small">Total User</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-10">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" placeholder="Cari berdasarkan username, email, atau nama lengkap" value="<?php echo $search; ?>">
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i> Cari
                </button>
            </div>
        </form>
        
        <?php if ($search): ?>
        <div class="mt-2">
            <small class="text-muted">
                Hasil pencarian untuk: "<strong><?php echo $search; ?></strong>"
                <a href="<?php echo base_url('admin/users'); ?>" class="ms-2">
                    <i class="fas fa-times"></i> Hapus Filter
                </a>
            </small>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Daftar User
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($users)): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Avatar</th>
                        <th>Username</th>
                        <th>Nama Lengkap</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Dibuat</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                    <tr>
                        <td>
                            <div class="avatar bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <?php echo strtoupper(substr($user->nama_lengkap, 0, 1)); ?>
                            </div>
                        </td>
                        <td>
                            <strong><?php echo $user->username; ?></strong>
                            <?php if ($user->id == $this->session->userdata('user_id')): ?>
                                <span class="badge bg-warning text-dark ms-1">You</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php echo $user->nama_lengkap; ?>
                            <?php if ($user->no_hp): ?>
                                <br><small class="text-muted">
                                    <i class="fas fa-phone"></i> <?php echo $user->no_hp; ?>
                                </small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $user->email; ?></td>
                        <td>
                            <?php 
                            $role_class = '';
                            switch ($user->role) {
                                case 'admin':
                                    $role_class = 'bg-primary';
                                    break;
                                case 'penagih':
                                    $role_class = 'bg-success';
                                    break;
                                case 'pimpinan':
                                    $role_class = 'bg-info';
                                    break;
                            }
                            ?>
                            <span class="badge <?php echo $role_class; ?>">
                                <?php echo ucfirst($user->role); ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($user->status == 'aktif'): ?>
                                <span class="badge bg-success">Aktif</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Nonaktif</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo format_tanggal($user->created_at, 'd/m/Y H:i'); ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('admin/users/edit/' . $user->id); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <?php if ($user->id != $this->session->userdata('user_id')): ?>
                                <a href="<?php echo base_url('admin/users/toggle_status/' . $user->id); ?>" 
                                   class="btn btn-sm btn-outline-warning" 
                                   title="<?php echo ($user->status == 'aktif') ? 'Nonaktifkan' : 'Aktifkan'; ?>"
                                   onclick="return confirm('Yakin ingin mengubah status user ini?')">
                                    <i class="fas fa-<?php echo ($user->status == 'aktif') ? 'toggle-off' : 'toggle-on'; ?>"></i>
                                </a>
                                
                                <a href="<?php echo base_url('admin/users/reset_password/' . $user->id); ?>" 
                                   class="btn btn-sm btn-outline-info" title="Reset Password"
                                   onclick="return confirm('Yakin ingin reset password user ini?')">
                                    <i class="fas fa-key"></i>
                                </a>
                                
                                <a href="<?php echo base_url('admin/users/delete/' . $user->id); ?>" 
                                   class="btn btn-sm btn-outline-danger" title="Hapus"
                                   onclick="return confirm('Yakin ingin menghapus user ini? Data yang terkait akan ikut terhapus!')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                <?php else: ?>
                                <span class="btn btn-sm btn-outline-secondary disabled" title="Tidak dapat mengelola akun sendiri">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($pagination): ?>
        <div class="d-flex justify-content-center mt-3">
            <?php echo $pagination; ?>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada data user</h5>
            <?php if ($search): ?>
            <p class="text-muted">Tidak ditemukan user dengan kata kunci "<?php echo $search; ?>"</p>
            <a href="<?php echo base_url('admin/users'); ?>" class="btn btn-primary">
                <i class="fas fa-list"></i> Lihat Semua User
            </a>
            <?php else: ?>
            <p class="text-muted">Silakan tambah user pertama</p>
            <a href="<?php echo base_url('admin/users/add'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah User
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions Modal -->
<div class="modal fade" id="quickActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quick Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('admin/users/add'); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tambah User Baru
                    </a>
                    <a href="<?php echo base_url('admin/users/export'); ?>" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Export Data User
                    </a>
                    <a href="<?php echo base_url('admin/users'); ?>" class="btn btn-info">
                        <i class="fas fa-refresh me-2"></i>Refresh Data
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Floating Action Button -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
    <button type="button" class="btn btn-primary btn-lg rounded-circle" data-bs-toggle="modal" data-bs-target="#quickActionModal">
        <i class="fas fa-plus"></i>
    </button>
</div>

<script>
// Auto hide alerts after 5 seconds
setTimeout(function() {
    $('.alert').fadeOut('slow');
}, 5000);

// Tooltip initialization
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});

// Search form enhancement
document.querySelector('input[name="search"]').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});

// Confirm actions
function confirmAction(message, url) {
    if (confirm(message)) {
        window.location.href = url;
    }
}
</script>

<?php $this->load->view('templates/footer'); ?>