<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-plus me-2"></i>Input Penggunaan Air</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('penagih/penggunaan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Form Input Penggunaan</h5>
            </div>
            <div class="card-body">
                <?php echo form_open('', ['id' => 'form-penggunaan']); ?>
                
                <!-- Info Periode -->
                <div class="alert alert-info">
                    <i class="fas fa-calendar me-2"></i>
                    <strong>Periode Pencatatan:</strong> <?php echo nama_bulan(date('m')) . ' ' . date('Y'); ?>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_pelanggan" class="form-label">Pilih Pelanggan <span class="text-danger">*</span></label>
                            <select class="form-select" id="id_pelanggan" name="id_pelanggan" required>
                                <option value="">-- Pilih Pelanggan --</option>
                                <?php if (isset($pelanggan_list) && !empty($pelanggan_list)): ?>
                                    <?php foreach ($pelanggan_list as $pelanggan): ?>
                                        <option value="<?php echo $pelanggan->id; ?>"
                                                data-no="<?php echo $pelanggan->no_pelanggan; ?>"
                                                data-alamat="<?php echo $pelanggan->alamat; ?>"
                                                <?php echo (isset($selected_pelanggan) && $selected_pelanggan == $pelanggan->id) ? 'selected' : ''; ?>>
                                            <?php echo $pelanggan->no_pelanggan . ' - ' . $pelanggan->nama_pelanggan; ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tidak ada pelanggan yang belum dicatat bulan ini</option>
                                <?php endif; ?>
                            </select>
                            <?php echo form_error('id_pelanggan', '<small class="text-danger">', '</small>'); ?>

                            <?php if (isset($pelanggan_list) && empty($pelanggan_list)): ?>
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Info:</strong> Semua pelanggan aktif sudah dicatat untuk bulan <?php echo nama_bulan(date('m')) . ' ' . date('Y'); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_tarif" class="form-label">Tarif <span class="text-danger">*</span></label>
                            <select class="form-select" id="id_tarif" name="id_tarif" required>
                                <option value="">-- Pilih Tarif --</option>
                                <?php if (isset($tarif_list) && !empty($tarif_list)): ?>
                                    <?php foreach ($tarif_list as $tarif): ?>
                                        <option value="<?php echo $tarif->id; ?>"
                                                data-tarif="<?php echo $tarif->tarif_per_m3; ?>"
                                                data-admin="<?php echo $tarif->biaya_admin; ?>"
                                                data-minimum="<?php echo $tarif->batas_minimum; ?>">
                                            <?php echo $tarif->nama_tarif . ' - ' . format_rupiah($tarif->tarif_per_m3) . '/m³'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Belum ada tarif yang tersedia</option>
                                <?php endif; ?>
                            </select>
                            <?php echo form_error('id_tarif', '<small class="text-danger">', '</small>'); ?>

                            <?php if (isset($tarif_list) && empty($tarif_list)): ?>
                            <div class="alert alert-warning mt-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Peringatan:</strong> Belum ada tarif yang dikonfigurasi. Hubungi admin untuk menambahkan tarif.
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Info Pelanggan -->
                <div id="info-pelanggan" class="alert alert-light d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>No. Pelanggan:</strong> <span id="show-no-pelanggan"></span><br>
                            <strong>Alamat:</strong> <span id="show-alamat"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>Meter Terakhir:</strong> <span id="show-meter-terakhir" class="text-primary"></span><br>
                            <strong>Periode Terakhir:</strong> <span id="show-periode-terakhir"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="meter_awal" class="form-label">Meter Awal (m³)</label>
                            <input type="number" class="form-control" id="meter_awal" name="meter_awal" 
                                   min="0" step="1" placeholder="Meter awal">
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i> Auto terisi dari pencatatan terakhir
                            </small>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="meter_akhir" class="form-label">Meter Akhir (m³) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="meter_akhir" name="meter_akhir" 
                                   min="0" step="1" placeholder="Masukkan angka meter akhir" required>
                            <?php echo form_error('meter_akhir', '<small class="text-danger">', '</small>'); ?>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="pemakaian" class="form-label">Pemakaian (m³)</label>
                            <input type="number" class="form-control" id="pemakaian" readonly 
                                   style="background-color: #e9ecef; font-weight: bold;">
                        </div>
                    </div>
                </div>

                <!-- Calculation Result -->
                <div id="calculation-result" class="d-none">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title"><i class="fas fa-calculator me-2"></i>Perhitungan Tagihan</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <small class="text-muted">Pemakaian:</small>
                                    <div class="fw-bold" id="calc-pemakaian">0 m³</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Pemakaian Tagihan:</small>
                                    <div class="fw-bold" id="calc-pemakaian-tagihan">0 m³</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Biaya Pemakaian:</small>
                                    <div class="fw-bold text-primary" id="calc-biaya-pemakaian">Rp 0</div>
                                </div>
                                <div class="col-md-3">
                                    <small class="text-muted">Biaya Admin:</small>
                                    <div class="fw-bold text-info" id="calc-biaya-admin">Rp 0</div>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-md-12 text-center">
                                    <small class="text-muted">Total Tagihan:</small>
                                    <div class="fw-bold text-success fs-4" id="calc-total-tagihan">Rp 0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary" id="btn-submit" disabled>
                        <i class="fas fa-save me-2"></i>Simpan Penggunaan
                    </button>
                    <a href="<?php echo base_url('penagih/penggunaan'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Batal
                    </a>
                </div>

                <?php echo form_close(); ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Pelanggan Belum Dicatat -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-list me-2"></i>Pelanggan Belum Dicatat</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($pelanggan_list)): ?>
                    <p class="text-muted">Total: <strong><?php echo count($pelanggan_list); ?> pelanggan</strong></p>
                    <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                        <?php foreach (array_slice($pelanggan_list, 0, 10) as $pelanggan): ?>
                        <a href="#" class="list-group-item list-group-item-action p-2 select-pelanggan" 
                           data-id="<?php echo $pelanggan->id; ?>">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?php echo $pelanggan->nama_pelanggan; ?></h6>
                                <small class="text-primary"><?php echo $pelanggan->no_pelanggan; ?></small>
                            </div>
                            <small class="text-muted"><?php echo substr($pelanggan->alamat, 0, 40) . '...'; ?></small>
                        </a>
                        <?php endforeach; ?>
                    </div>
                    <?php if (count($pelanggan_list) > 10): ?>
                    <div class="text-center mt-2">
                        <small class="text-muted">Dan <?php echo count($pelanggan_list) - 10; ?> pelanggan lainnya...</small>
                    </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h6 class="text-success">Semua Selesai!</h6>
                        <p class="text-muted mb-0">Semua pelanggan aktif sudah dicatat untuk bulan ini.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tips -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips Pencatatan</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info p-3">
                    <ul class="mb-0" style="font-size: 14px;">
                        <li>Pastikan angka meter akhir lebih besar dari meter awal</li>
                        <li>Jika meter rusak/reset, catat di keterangan</li>
                        <li>Periksa kembali angka sebelum menyimpan</li>
                        <li>Foto meter sebagai bukti (jika diperlukan)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.select-pelanggan:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

#calculation-result {
    animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
$(document).ready(function() {
    // Event pelanggan change
    $('#id_pelanggan').change(function() {
        const pelangganId = $(this).val();
        if (pelangganId) {
            // Show info pelanggan
            const selectedOption = $(this).find('option:selected');
            $('#show-no-pelanggan').text(selectedOption.data('no'));
            $('#show-alamat').text(selectedOption.data('alamat'));
            $('#info-pelanggan').removeClass('d-none');
            
            // Get meter terakhir via AJAX
            $.post('<?php echo base_url("penagih/penggunaan/get_meter_terakhir"); ?>', {
                id_pelanggan: pelangganId
            }, function(response) {
                try {
                    const data = JSON.parse(response);
                    $('#meter_awal').val(data.meter_terakhir);
                    $('#show-meter-terakhir').text(data.formatted_meter + ' m³');
                    $('#show-periode-terakhir').text(data.periode_terakhir);

                    // Enable meter akhir & awal
                    $('#meter_akhir, #meter_awal').prop('disabled', false);
                    $('#meter_akhir').focus();

                    // Trigger calculation
                    calculatePemakaian();
                } catch (e) {
                    console.error('Error parsing response:', e);
                    alert('Error mengambil data meter terakhir');
                }
            }).fail(function() {
                alert('Error koneksi ke server');
            });
        } else {
            $('#info-pelanggan').addClass('d-none');
            $('#meter_awal').val('');
            $('#meter_awal').prop('disabled', true);
            $('#meter_akhir').prop('disabled', true);
            $('#calculation-result').addClass('d-none');
        }
    });

    // Quick select pelanggan
    $('.select-pelanggan').click(function(e) {
        e.preventDefault();
        const pelangganId = $(this).data('id');
        $('#id_pelanggan').val(pelangganId).trigger('change');
    });

    // Event meter awal, akhir & tarif change - CALCULATION
    $('#meter_awal, #meter_akhir, #id_tarif').on('input change', function() {
        calculatePemakaian();
    });

    // Function untuk menghitung pemakaian
    function calculatePemakaian() {
        const meterAwal = parseInt($('#meter_awal').val()) || 0;
        const meterAkhir = parseInt($('#meter_akhir').val()) || 0;
        const pemakaian = meterAkhir - meterAwal;

        // Set pemakaian
        $('#pemakaian').val(pemakaian >= 0 ? pemakaian : 0);

        // Validasi dan tampilkan pesan error jika perlu
        if (meterAkhir > 0 && pemakaian < 0) {
            $('#pemakaian').addClass('is-invalid');
            if (!$('#pemakaian-error').length) {
                $('#pemakaian').after('<div id="pemakaian-error" class="invalid-feedback">Meter akhir tidak boleh lebih kecil dari meter awal</div>');
            }
        } else {
            $('#pemakaian').removeClass('is-invalid');
            $('#pemakaian-error').remove();
        }

        // Calculate tagihan jika tarif sudah dipilih
        if ($('#id_tarif').val() && pemakaian >= 0) {
            calculateTagihan();
        }

        // Enable/disable submit button
        updateSubmitButton();
    }

    // Function untuk menghitung tagihan
    function calculateTagihan() {
        const meterAwal = parseInt($('#meter_awal').val()) || 0;
        const meterAkhir = parseInt($('#meter_akhir').val()) || 0;
        const idTarif = $('#id_tarif').val();

        if (!idTarif || meterAkhir <= 0) {
            $('#calculation-result').addClass('d-none');
            return;
        }

        const pemakaian = meterAkhir - meterAwal;
        if (pemakaian < 0) {
            $('#calculation-result').addClass('d-none');
            return;
        }

        // AJAX call untuk calculate tagihan
        $.post('<?php echo base_url("penagih/penggunaan/calculate_tagihan"); ?>', {
            meter_awal: meterAwal,
            meter_akhir: meterAkhir,
            id_tarif: idTarif
        }, function(response) {
            try {
                const data = JSON.parse(response);

                // Update calculation result
                $('#calc-pemakaian').text(data.pemakaian + ' m³');
                $('#calc-pemakaian-tagihan').text(data.pemakaian_tagihan + ' m³');
                $('#calc-biaya-pemakaian').text('Rp ' + data.formatted_biaya_pemakaian);
                $('#calc-biaya-admin').text('Rp ' + data.formatted_biaya_admin);
                $('#calc-total-tagihan').text('Rp ' + data.formatted_total_tagihan);

                // Show calculation result
                $('#calculation-result').removeClass('d-none');

            } catch (e) {
                console.error('Error parsing calculation response:', e);
            }
        }).fail(function() {
            console.error('Error calculating tagihan');
        });
    }

    // Function untuk update submit button
    function updateSubmitButton() {
        const pelangganId = $('#id_pelanggan').val();
        const idTarif = $('#id_tarif').val();
        const meterAkhir = parseInt($('#meter_akhir').val()) || 0;
        const pemakaian = parseInt($('#pemakaian').val()) || 0;

        if (pelangganId && idTarif && meterAkhir > 0 && pemakaian >= 0) {
            $('#btn-submit').prop('disabled', false);
        } else {
            $('#btn-submit').prop('disabled', true);
        }
    }

    // Pre-select pelanggan from URL
    <?php if ($selected_pelanggan): ?>
    $('#id_pelanggan').trigger('change');
    <?php endif; ?>
});
</script>

<?php $this->load->view('templates/footer'); ?>