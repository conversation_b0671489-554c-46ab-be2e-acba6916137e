<?php

if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Format currency ke Rupiah
 */
if (!function_exists('format_rupiah')) {
    function format_rupiah($angka, $prefix = 'Rp ') {
        return $prefix . number_format($angka, 0, ',', '.');
    }
}

/**
 * Format tanggal Indonesia
 */
if (!function_exists('format_tanggal')) {
    function format_tanggal($tanggal, $format = 'd/m/Y') {
        if ($tanggal == '0000-00-00' || empty($tanggal)) {
            return '-';
        }
        return date($format, strtotime($tanggal));
    }
}

/**
 * Get nama bulan Indonesia
 */
if (!function_exists('nama_bulan')) {
    function nama_bulan($bulan) {
        $bulan_array = array(
            1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
            5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
            9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
        );
        return isset($bulan_array[$bulan]) ? $bulan_array[$bulan] : '';
    }
}

/**
 * Generate nomor kwitansi otomatis
 */
if (!function_exists('generate_kwitansi')) {
    function generate_kwitansi() {
        return 'KWT' . date('Ymd') . sprintf('%04d', rand(1, 9999));
    }
}

/**
 * Generate nomor pelanggan otomatis
 */
if (!function_exists('generate_no_pelanggan')) {
    function generate_no_pelanggan() {
        return 'PLG' . date('Y') . sprintf('%06d', rand(1, 999999));
    }
}

/**
 * Alert flash message
 */
if (!function_exists('set_flash_message')) {
    function set_flash_message($type, $message) {
        $CI =& get_instance();
        $CI->session->set_flashdata('flash_type', $type);
        $CI->session->set_flashdata('flash_message', $message);
    }
}

/**
 * Show flash message
 */
if (!function_exists('show_flash_message')) {
    function show_flash_message() {
        $CI =& get_instance();
        $type = $CI->session->flashdata('flash_type');
        $message = $CI->session->flashdata('flash_message');
        
        if ($type && $message) {
            $alert_class = '';
            switch ($type) {
                case 'success':
                    $alert_class = 'alert-success';
                    break;
                case 'error':
                    $alert_class = 'alert-danger';
                    break;
                case 'warning':
                    $alert_class = 'alert-warning';
                    break;
                case 'info':
                    $alert_class = 'alert-info';
                    break;
                default:
                    $alert_class = 'alert-info';
            }
            
            return '<div class="alert ' . $alert_class . ' alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert">&times;</button>
                        ' . $message . '
                    </div>';
        }
        return '';
    }
}