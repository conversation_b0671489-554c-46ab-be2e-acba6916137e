<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt me-2"></i>Data Penggunaan Air</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Input Penggunaan
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Filter & Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Bulan</label>
                <select class="form-select" name="bulan">
                    <?php for ($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo sprintf('%02d', $i); ?>" 
                                <?php echo ($bulan == sprintf('%02d', $i)) ? 'selected' : ''; ?>>
                            <?php echo nama_bulan($i); ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Tahun</label>
                <select class="form-select" name="tahun">
                    <?php for ($y = date('Y'); $y >= date('Y') - 3; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo ($tahun == $y) ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Cari Pelanggan</label>
                <input type="text" class="form-control" name="search" value="<?php echo $search; ?>" 
                       placeholder="Nama atau No. pelanggan...">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?php echo base_url('penagih/penggunaan'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            Pencatatan <?php echo nama_bulan($bulan) . ' ' . $tahun; ?>
                        </div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_dicatat); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tachometer-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan Aktif</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_pelanggan); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Progress</div>
                        <div class="h5 mb-0 font-weight-bold">
                            <?php echo $total_pelanggan > 0 ? number_format(($total_dicatat / $total_pelanggan) * 100, 1) : 0; ?>%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Daftar Penggunaan - <?php echo nama_bulan($bulan) . ' ' . $tahun; ?>
            <?php if ($search): ?>
                <small class="text-muted">(<?php echo count($penggunaan); ?> hasil pencarian)</small>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($penggunaan)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th width="5%">No</th>
                        <th width="12%">No. Pelanggan</th>
                        <th width="18%">Nama Pelanggan</th>
                        <th width="10%">Meter Awal</th>
                        <th width="10%">Meter Akhir</th>
                        <th width="8%">Pemakaian</th>
                        <th width="12%">Tarif</th>
                        <th width="12%">Total Tagihan</th>
                        <th width="10%">Tgl. Catat</th>
                        <th width="8%">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_pemakaian = 0;
                    $total_tagihan_all = 0;
                    ?>
                    <?php foreach ($penggunaan as $index => $p): ?>
                    <?php 
                    $total_pemakaian += $p->pemakaian;
                    $total_tagihan_all += $p->total_tagihan;
                    ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td>
                            <strong class="text-primary"><?php echo $p->no_pelanggan; ?></strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <strong><?php echo $p->nama_pelanggan; ?></strong>
                                    <br><small class="text-muted"><?php echo substr($p->alamat, 0, 30) . '...'; ?></small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?php echo number_format($p->meter_awal); ?> m³</span>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo number_format($p->meter_akhir); ?> m³</span>
                        </td>
                        <td>
                            <span class="badge bg-primary fs-6"><?php echo number_format($p->pemakaian); ?> m³</span>
                        </td>
                        <td>
                            <small class="text-muted"><?php echo $p->nama_tarif; ?></small>
                        </td>
                        <td>
                            <strong class="text-success"><?php echo format_rupiah($p->total_tagihan); ?></strong>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?php echo format_tanggal($p->tanggal_catat); ?><br>
                                <i class="fas fa-user-tie"></i> <?php echo $p->petugas; ?>
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('penagih/penggunaan/edit/' . $p->id); ?>" 
                                   class="btn btn-sm btn-outline-warning" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo base_url('penagih/pelanggan/detail/' . $p->id_pelanggan); ?>" 
                                   class="btn btn-sm btn-outline-primary" title="Detail Pelanggan">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th colspan="5" class="text-end">TOTAL:</th>
                        <th><span class="badge bg-primary fs-6"><?php echo number_format($total_pemakaian); ?> m³</span></th>
                        <th></th>
                        <th><strong class="text-success fs-5"><?php echo format_rupiah($total_tagihan_all); ?></strong></th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Belum ada pencatatan penggunaan</h5>
            <p class="text-muted">
                <?php if ($search): ?>
                    Tidak ditemukan data untuk pencarian "<?php echo $search; ?>" pada <?php echo nama_bulan($bulan) . ' ' . $tahun; ?>
                <?php else: ?>
                    Belum ada pencatatan penggunaan air untuk <?php echo nama_bulan($bulan) . ' ' . $tahun; ?>
                <?php endif; ?>
            </p>
            <div class="mt-3">
                <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-2"></i>Input Penggunaan Baru
                </a>
                <?php if ($search): ?>
                <a href="<?php echo base_url('penagih/penggunaan?bulan=' . $bulan . '&tahun=' . $tahun); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-refresh me-2"></i>Reset Pencarian
                </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Progress & Quick Info -->
<?php if (!empty($penggunaan)): ?>
<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Ringkasan Pencatatan</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary"><?php echo count($penggunaan); ?></h4>
                        <small class="text-muted">Pelanggan Dicatat</small>
                    </div>
                    <div class="col-md-3 text-center border-start">
                        <h4 class="text-success"><?php echo number_format($total_pemakaian); ?> m³</h4>
                        <small class="text-muted">Total Pemakaian</small>
                    </div>
                    <div class="col-md-3 text-center border-start">
                        <h4 class="text-info"><?php echo format_rupiah($total_tagihan_all); ?></h4>
                        <small class="text-muted">Total Tagihan</small>
                    </div>
                    <div class="col-md-3 text-center border-start">
                        <h4 class="text-warning"><?php echo number_format(($total_pemakaian / count($penggunaan)), 1); ?> m³</h4>
                        <small class="text-muted">Rata-rata Pemakaian</small>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <small class="text-muted">Progress Pencatatan Bulan Ini</small>
                        <small class="text-muted"><?php echo $total_dicatat; ?>/<?php echo $total_pelanggan; ?> pelanggan</small>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-primary" style="width: <?php echo $total_pelanggan > 0 ? ($total_dicatat / $total_pelanggan) * 100 : 0; ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Input Penggunaan Baru
                    </a>
                    <a href="<?php echo base_url('penagih/pelanggan'); ?>" class="btn btn-info">
                        <i class="fas fa-users me-2"></i>Data Pelanggan
                    </a>
                    <button onclick="window.print()" class="btn btn-secondary">
                        <i class="fas fa-print me-2"></i>Print Laporan
                    </button>
                    <button onclick="exportToExcel()" class="btn btn-success">
                        <i class="fas fa-file-excel me-2"></i>Export Excel
                    </button>
                </div>
                
                <!-- Status Info -->
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary mb-0"><?php echo $total_dicatat; ?></h6>
                            <small class="text-muted">Dicatat</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-warning mb-0"><?php echo $total_pelanggan - $total_dicatat; ?></h6>
                        <small class="text-muted">Belum</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Notification Card -->
        <?php if (($total_pelanggan - $total_dicatat) > 0): ?>
        <div class="card mt-3 border-warning">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">Perhatian!</h6>
                        <p class="mb-0 small">Masih ada <strong><?php echo $total_pelanggan - $total_dicatat; ?> pelanggan</strong> yang belum dicatat untuk bulan ini.</p>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>

<!-- Pelanggan Belum Dicatat -->
<?php if (($total_pelanggan - $total_dicatat) > 0): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users me-2"></i>Pelanggan Belum Dicatat - <?php echo nama_bulan($bulan) . ' ' . $tahun; ?>
                    <span class="badge bg-warning ms-2"><?php echo $total_pelanggan - $total_dicatat; ?> pelanggan</span>
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php 
                    // Get pelanggan yang belum dicatat untuk ditampilkan
                    $this->db->select('p.*');
                    $this->db->from('pelanggan p');
                    $this->db->join('penggunaan_air pa', "pa.id_pelanggan = p.id AND pa.bulan = {$bulan} AND pa.tahun = {$tahun}", 'left');
                    $this->db->where('p.status_langganan', 'aktif');
                    $this->db->where('pa.id IS NULL');
                    $this->db->order_by('p.nama_pelanggan', 'ASC');
                    $this->db->limit(8);
                    $belum_dicatat = $this->db->get()->result();
                    ?>
                    
                    <?php if (!empty($belum_dicatat)): ?>
                        <?php foreach ($belum_dicatat as $pelanggan): ?>
                        <div class="col-md-3 mb-3">
                            <div class="card border-warning">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-1"><?php echo $pelanggan->nama_pelanggan; ?></h6>
                                    <p class="card-text small text-muted mb-2"><?php echo $pelanggan->no_pelanggan; ?></p>
                                    <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $pelanggan->id); ?>" 
                                       class="btn btn-sm btn-warning w-100">
                                        <i class="fas fa-plus me-1"></i>Input
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        
                        <?php if (($total_pelanggan - $total_dicatat) > 8): ?>
                        <div class="col-12 text-center">
                            <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-plus me-2"></i>Lihat Semua Pelanggan Belum Dicatat
                            </a>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
}

@media print {
    .btn-toolbar, .card-header, .btn-group, .navbar, .sidebar, .border-bottom {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table th, .table td {
        font-size: 11px !important;
        padding: 4px !important;
    }
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>

<script>
function exportToExcel() {
    const bulan = '<?php echo $bulan; ?>';
    const tahun = '<?php echo $tahun; ?>';
    window.open(`<?php echo base_url("penagih/penggunaan/export"); ?>?bulan=${bulan}&tahun=${tahun}`, '_blank');
}

// Auto refresh setiap 5 menit
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 300000);
</script>

<?php $this->load->view('templates/footer'); ?>