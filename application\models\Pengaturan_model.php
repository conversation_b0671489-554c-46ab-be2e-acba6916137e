<?php
// ========================================
// File: application/models/Pengaturan_model.php
// ========================================

defined('BASEPATH') OR exit('No direct script access allowed');

class Pengaturan_model extends CI_Model {

    private $table = 'pengaturan';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get single setting by name
     */
    public function get_setting($nama_setting, $default_value = null)
    {
        $this->db->where('nama_setting', $nama_setting);
        $query = $this->db->get($this->table);
        
        if ($query->num_rows() > 0) {
            return $query->row()->nilai;
        }
        
        return $default_value;
    }

    /**
     * Get multiple settings as associative array
     */
    public function get_settings($setting_names = array())
    {
        $settings = array();
        
        if (empty($setting_names)) {
            // Get all settings
            $query = $this->db->get($this->table);
            foreach ($query->result() as $row) {
                $settings[$row->nama_setting] = $row->nilai;
            }
        } else {
            // Get specific settings
            $this->db->where_in('nama_setting', $setting_names);
            $query = $this->db->get($this->table);
            
            foreach ($query->result() as $row) {
                $settings[$row->nama_setting] = $row->nilai;
            }
            
            // Add missing settings with null values
            foreach ($setting_names as $name) {
                if (!isset($settings[$name])) {
                    $settings[$name] = null;
                }
            }
        }
        
        return $settings;
    }

    /**
     * Update or insert single setting
     */
    public function update_setting($nama_setting, $nilai, $keterangan = null)
    {
        $this->db->where('nama_setting', $nama_setting);
        $existing = $this->db->get($this->table);
        
        $data = array(
            'nama_setting' => $nama_setting,
            'nilai' => $nilai
        );
        
        if ($keterangan !== null) {
            $data['keterangan'] = $keterangan;
        }
        
        if ($existing->num_rows() > 0) {
            // Update existing
            $this->db->where('nama_setting', $nama_setting);
            return $this->db->update($this->table, $data);
        } else {
            // Insert new
            return $this->db->insert($this->table, $data);
        }
    }

    /**
     * Update multiple settings at once
     */
    public function update_multiple($settings_array)
    {
        $this->db->trans_start();
        
        foreach ($settings_array as $nama_setting => $nilai) {
            if ($nilai !== null && $nilai !== '') {
                $this->update_setting($nama_setting, $nilai);
            }
        }
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Delete setting
     */
    public function delete_setting($nama_setting)
    {
        $this->db->where('nama_setting', $nama_setting);
        return $this->db->delete($this->table);
    }

    /**
     * Get all settings with pagination
     */
    public function get_all_paginated($limit = 10, $offset = 0, $search = '')
    {
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('nama_setting', $search);
            $this->db->or_like('nilai', $search);
            $this->db->or_like('keterangan', $search);
            $this->db->group_end();
        }
        
        $this->db->order_by('nama_setting', 'ASC');
        $this->db->limit($limit, $offset);
        return $this->db->get($this->table)->result();
    }

    /**
     * Count all settings
     */
    public function count_all($search = '')
    {
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('nama_setting', $search);
            $this->db->or_like('nilai', $search);
            $this->db->or_like('keterangan', $search);
            $this->db->group_end();
        }
        
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get settings by category (prefix)
     */
    public function get_by_category($prefix)
    {
        $this->db->like('nama_setting', $prefix, 'after');
        $this->db->order_by('nama_setting', 'ASC');
        $query = $this->db->get($this->table);
        
        $settings = array();
        foreach ($query->result() as $row) {
            $settings[$row->nama_setting] = $row->nilai;
        }
        
        return $settings;
    }

    /**
     * Reset settings to default values
     */
    public function reset_to_defaults()
    {
        $default_settings = array(
            'nama_perusahaan' => 'PDAM Tirta Sejahtera',
            'alamat_perusahaan' => 'Jl. Merdeka No. 123, Kota',
            'jatuh_tempo_hari' => '30',
            'email_pengingat' => 'aktif',
            'denda_per_hari' => '0',
            'timezone' => 'Asia/Jakarta',
            'auto_generate_invoice' => 'nonaktif',
            'backup_otomatis' => 'nonaktif',
            'maintenance_mode' => 'nonaktif',
            'email_pengingat' => 'aktif',
            'sms_pengingat' => 'nonaktif'
        );
        
        return $this->update_multiple($default_settings);
    }

    /**
     * Get system info settings
     */
    public function get_system_info()
    {
        $info_settings = $this->get_settings(array(
            'nama_perusahaan', 'alamat_perusahaan', 'no_telepon', 
            'email_perusahaan', 'website', 'logo_perusahaan'
        ));
        
        return $info_settings;
    }

    /**
     * Get email configuration
     */
    public function get_email_config()
    {
        $email_settings = $this->get_settings(array(
            'smtp_host', 'smtp_port', 'smtp_user', 'smtp_pass', 
            'smtp_crypto', 'email_from_name', 'email_from_address'
        ));
        
        return $email_settings;
    }

    /**
     * Check if maintenance mode is active
     */
    public function is_maintenance_mode()
    {
        return $this->get_setting('maintenance_mode', 'nonaktif') === 'aktif';
    }

    /**
     * Get billing settings
     */
    public function get_billing_settings()
    {
        return $this->get_settings(array(
            'jatuh_tempo_hari', 'denda_per_hari', 'auto_generate_invoice'
        ));
    }

    /**
     * Get notification settings
     */
    public function get_notification_settings()
    {
        return $this->get_settings(array(
            'email_pengingat', 'sms_pengingat', 'email_template_header', 
            'email_template_footer'
        ));
    }

    /**
     * Backup settings to array
     */
    public function export_settings()
    {
        $query = $this->db->get($this->table);
        $settings = array();
        
        foreach ($query->result() as $row) {
            $settings[] = array(
                'nama_setting' => $row->nama_setting,
                'nilai' => $row->nilai,
                'keterangan' => $row->keterangan
            );
        }
        
        return $settings;
    }

    /**
     * Import settings from array
     */
    public function import_settings($settings_array)
    {
        $this->db->trans_start();
        
        foreach ($settings_array as $setting) {
            $this->update_setting(
                $setting['nama_setting'], 
                $setting['nilai'], 
                $setting['keterangan'] ?? null
            );
        }
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Search settings
     */
    public function search($keyword)
    {
        $this->db->group_start();
        $this->db->like('nama_setting', $keyword);
        $this->db->or_like('nilai', $keyword);
        $this->db->or_like('keterangan', $keyword);
        $this->db->group_end();
        
        $this->db->order_by('nama_setting', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get last updated setting
     */
    public function get_last_updated()
    {
        $this->db->order_by('updated_at', 'DESC');
        $this->db->limit(1);
        return $this->db->get($this->table)->row();
    }

    /**
     * Validate setting value
     */
    public function validate_setting($nama_setting, $nilai)
    {
        $validations = array(
            'jatuh_tempo_hari' => array('type' => 'integer', 'min' => 1, 'max' => 365),
            'denda_per_hari' => array('type' => 'decimal', 'min' => 0),
            'email_pengingat' => array('type' => 'enum', 'values' => array('aktif', 'nonaktif')),
            'sms_pengingat' => array('type' => 'enum', 'values' => array('aktif', 'nonaktif')),
            'maintenance_mode' => array('type' => 'enum', 'values' => array('aktif', 'nonaktif')),
            'smtp_port' => array('type' => 'integer', 'min' => 1, 'max' => 65535),
            'email_perusahaan' => array('type' => 'email'),
            'website' => array('type' => 'url')
        );
        
        if (!isset($validations[$nama_setting])) {
            return true; // No validation rule, allow any value
        }
        
        $rule = $validations[$nama_setting];
        
        switch ($rule['type']) {
            case 'integer':
                if (!is_numeric($nilai) || (int)$nilai != $nilai) {
                    return false;
                }
                if (isset($rule['min']) && $nilai < $rule['min']) {
                    return false;
                }
                if (isset($rule['max']) && $nilai > $rule['max']) {
                    return false;
                }
                break;
                
            case 'decimal':
                if (!is_numeric($nilai)) {
                    return false;
                }
                if (isset($rule['min']) && $nilai < $rule['min']) {
                    return false;
                }
                if (isset($rule['max']) && $nilai > $rule['max']) {
                    return false;
                }
                break;
                
            case 'enum':
                if (!in_array($nilai, $rule['values'])) {
                    return false;
                }
                break;
                
            case 'email':
                if (!filter_var($nilai, FILTER_VALIDATE_EMAIL)) {
                    return false;
                }
                break;
                
            case 'url':
                if (!empty($nilai) && !filter_var($nilai, FILTER_VALIDATE_URL)) {
                    return false;
                }
                break;
        }
        
        return true;
    }
}