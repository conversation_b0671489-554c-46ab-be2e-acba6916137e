<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>elanggan extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->model('Pelanggan_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Pembayaran_model');
        $this->load->helper(['form', 'url', 'date']);
        $this->load->library(['form_validation', 'session']);
        
        // Check login
        if (!$this->session->userdata('logged_in')) {
            redirect('auth/login');
        }
        
        // Check role penagih
        if ($this->session->userdata('role') !== 'penagih') {
            redirect('dashboard');
        }
    }

    public function index()
    {
        $data['title'] = 'Data Pelanggan';
        
        // Search & Filter
        $search = $this->input->get('search');
        $status = $this->input->get('status');
        
        // Get data pelanggan dengan filter
        $this->db->select('*');
        $this->db->from('pelanggan');
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('nama_pelanggan', $search);
            $this->db->or_like('no_pelanggan', $search);
            $this->db->or_like('alamat', $search);
            $this->db->or_like('no_hp', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('status_langganan', $status);
        }
        
        $this->db->order_by('nama_pelanggan', 'ASC');
        $data['pelanggan'] = $this->db->get()->result();
        
        $data['total_pelanggan'] = $this->Pelanggan_model->count_all();
        $data['search'] = $search;
        $data['status_filter'] = $status;
        
        $this->load->view('penagih/pelanggan/index', $data);
    }

    public function detail($id)
    {
        $data['title'] = 'Detail Pelanggan';
        $data['pelanggan'] = $this->Pelanggan_model->get_by_id($id);
        
        if (!$data['pelanggan']) {
            $this->session->set_flashdata('error', 'Data pelanggan tidak ditemukan.');
            redirect('penagih/pelanggan');
        }
        
        // Riwayat penggunaan 6 bulan terakhir
        $this->db->select('pa.*, t.nama_tarif, t.tarif_per_m3');
        $this->db->from('penggunaan_air pa');
        $this->db->join('tarif t', 't.id = pa.id_tarif');
        $this->db->where('pa.id_pelanggan', $id);
        $this->db->order_by('pa.tahun DESC, pa.bulan DESC');
        $this->db->limit(6);
        $data['riwayat_penggunaan'] = $this->db->get()->result();
        
        // Riwayat pembayaran 6 bulan terakhir
        $this->db->select('p.*, pa.bulan, pa.tahun, pa.total_tagihan');
        $this->db->from('pembayaran p');
        $this->db->join('penggunaan_air pa', 'pa.id = p.id_penggunaan');
        $this->db->where('pa.id_pelanggan', $id);
        $this->db->order_by('p.tanggal_bayar DESC');
        $this->db->limit(6);
        $data['riwayat_pembayaran'] = $this->db->get()->result();
        
        // Update path view
        $this->load->view('penagih/pelanggan/detail', $data);
    }

    // AJAX untuk pencarian cepat
    public function search_ajax()
    {
        $search = $this->input->post('search');
        
        $this->db->select('id, no_pelanggan, nama_pelanggan, alamat, status_langganan');
        $this->db->from('pelanggan');
        $this->db->group_start();
        $this->db->like('nama_pelanggan', $search);
        $this->db->or_like('no_pelanggan', $search);
        $this->db->group_end();
        $this->db->limit(10);
        
        $result = $this->db->get()->result();
        
        echo json_encode($result);
    }
}