<?php

if (!function_exists('load_email_config')) {
    function load_email_config() {
        $CI =& get_instance();
        $CI->load->model('Pengaturan_model');
        
        $email_keys = array('smtp_host', 'smtp_port', 'smtp_user', 'smtp_pass', 'smtp_crypto');
        $settings = $CI->Pengaturan_model->get_settings($email_keys);
        
        return array(
            'protocol' => 'smtp',
            'smtp_host' => $settings['smtp_host'],
            'smtp_port' => $settings['smtp_port'] ?: 587,
            'smtp_user' => $settings['smtp_user'],
            'smtp_pass' => $settings['smtp_pass'],
            'smtp_crypto' => $settings['smtp_crypto'] ?: 'tls',
            'smtp_timeout' => 30,
            'mailtype' => 'html',
            'charset' => 'utf-8',
            'newline' => "\r\n",
            'wordwrap' => TRUE
        );
    }
}
