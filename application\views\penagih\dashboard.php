<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard Penagih</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i> <?php echo format_tanggal(date('Y-m-d')); ?>
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_pelanggan); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Pencatatan Bulan Ini</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($pencatatan_bulan_ini); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tachometer-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Perlu Verifikasi</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($verifikasi_pending); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Verifikasi Hari Ini</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($verifikasi_hari_ini); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions & Tasks -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>Tugas Hari Ini</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Pelanggan Belum Dicatat (<?php echo nama_bulan(date('m')) . ' ' . date('Y'); ?>)</h6>
                        <?php if (!empty($pelanggan_belum_dicatat)): ?>
                            <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                <?php foreach (array_slice($pelanggan_belum_dicatat, 0, 5) as $pelanggan): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center p-2">
                                    <div>
                                        <strong><?php echo $pelanggan->nama_pelanggan; ?></strong><br>
                                        <small class="text-muted"><?php echo $pelanggan->no_pelanggan; ?></small>
                                    </div>
                                    <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $pelanggan->id); ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php if (count($pelanggan_belum_dicatat) > 5): ?>
                            <div class="text-center mt-2">
                                <a href="<?php echo base_url('penagih/penggunaan'); ?>" class="btn btn-sm btn-outline-primary">
                                    Lihat Semua (<?php echo count($pelanggan_belum_dicatat); ?> pelanggan)
                                </a>
                            </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="text-center py-3">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-muted mb-0">Semua pelanggan sudah dicatat</p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-6 border-start">
                        <h6 class="text-muted">Pembayaran Perlu Verifikasi</h6>
                        <?php if (!empty($pembayaran_perlu_verifikasi)): ?>
                            <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">
                                <?php foreach ($pembayaran_perlu_verifikasi as $pembayaran): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center p-2">
                                    <div>
                                        <strong><?php echo $pembayaran->nama_pelanggan; ?></strong><br>
                                        <small class="text-muted">
                                            <?php echo $pembayaran->no_kwitansi; ?> - <?php echo format_rupiah($pembayaran->jumlah_bayar); ?>
                                        </small>
                                    </div>
                                    <a href="<?php echo base_url('penagih/pembayaran/verifikasi/' . $pembayaran->id); ?>" 
                                       class="btn btn-sm btn-warning">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center mt-2">
                                <a href="<?php echo base_url('penagih/pembayaran'); ?>" class="btn btn-sm btn-outline-warning">
                                    Lihat Semua Verifikasi
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-3">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-muted mb-0">Tidak ada pembayaran pending</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Input Penggunaan Air
                    </a>
                    <a href="<?php echo base_url('penagih/pembayaran'); ?>" class="btn btn-warning">
                        <i class="fas fa-money-bill-wave me-2"></i>Verifikasi Pembayaran
                    </a>
                    <a href="<?php echo base_url('penagih/pelanggan'); ?>" class="btn btn-info">
                        <i class="fas fa-users me-2"></i>Cari Data Pelanggan
                    </a>
                    <a href="<?php echo base_url('penagih/penggunaan'); ?>" class="btn btn-success">
                        <i class="fas fa-list me-2"></i>Daftar Penggunaan
                    </a>
                </div>
                
                <hr>
                
                <h6 class="text-muted mb-3">Ringkasan Aktivitas</h6>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-0"><?php echo $pencatatan_bulan_ini; ?></h4>
                            <small class="text-muted">Pencatatan</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-0"><?php echo $verifikasi_hari_ini; ?></h4>
                        <small class="text-muted">Verifikasi</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Progress Bulan Ini</h6>
            </div>
            <div class="card-body">
                <?php 
                $progress_pencatatan = $total_pelanggan > 0 ? ($pencatatan_bulan_ini / $total_pelanggan) * 100 : 0;
                $progress_verifikasi = $verifikasi_pending > 0 ? (($verifikasi_pending - $verifikasi_pending) / $verifikasi_pending) * 100 : 100;
                ?>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <small>Pencatatan Meter</small>
                        <small><?php echo number_format($progress_pencatatan, 1); ?>%</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-primary" style="width: <?php echo $progress_pencatatan; ?>%"></div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <small>Pending Verifikasi</small>
                        <small><?php echo $verifikasi_pending; ?> item</small>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: <?php echo min(($verifikasi_pending / 10) * 100, 100); ?>%"></div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        Data diperbarui secara real-time
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Aktivitas Terbaru</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Pencatatan Terbaru</h6>
                        <div class="timeline">
                            <div class="timeline-item">
                                <i class="fas fa-clock text-muted"></i>
                                <span class="text-muted">Hari ini Anda telah mencatat <?php echo $pencatatan_bulan_ini; ?> penggunaan air</span>
                            </div>
                            <div class="timeline-item">
                                <i class="fas fa-check text-success"></i>
                                <span class="text-muted">Verifikasi <?php echo $verifikasi_hari_ini; ?> pembayaran hari ini</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-muted">Tips & Reminder</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Tips:</strong> Pastikan untuk mencatat meter air secara akurat dan upload bukti pembayaran yang jelas untuk mempercepat proses verifikasi.
                        </div>
                        
                        <?php if ($verifikasi_pending > 10): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Perhatian:</strong> Ada <?php echo $verifikasi_pending; ?> pembayaran yang menunggu verifikasi. Segera lakukan verifikasi!
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.timeline-item i {
    margin-right: 10px;
    width: 16px;
}
</style>

<?php $this->load->view('templates/footer'); ?>