<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-plus me-2"></i>Input Pembayaran Baru</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pembayaran'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Form Input Pembayaran</h5>
            </div>
            <div class="card-body">
                <?php echo form_open('', ['id' => 'form-pembayaran']); ?>
                
                <!-- Info Periode -->
                <div class="alert alert-info">
                    <i class="fas fa-calendar me-2"></i>
                    <strong>Periode:</strong> <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?>
                    <a href="<?php echo base_url('admin/pembayaran/add?bulan=' . date('m') . '&tahun=' . date('Y')); ?>" 
                       class="btn btn-sm btn-outline-primary ms-2">
                        <i class="fas fa-sync"></i> Bulan Ini
                    </a>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="id_penggunaan" class="form-label">Pilih Tagihan <span class="text-danger">*</span></label>
                            <select class="form-select" id="id_penggunaan" name="id_penggunaan" required>
                                <option value="">-- Pilih Tagihan --</option>
                                <?php if (isset($tagihan_list) && !empty($tagihan_list)): ?>
                                    <?php foreach ($tagihan_list as $tagihan): ?>
                                        <option value="<?php echo $tagihan->id; ?>" 
                                                data-pelanggan="<?php echo $tagihan->nama_pelanggan; ?>"
                                                data-no="<?php echo $tagihan->no_pelanggan; ?>"
                                                data-alamat="<?php echo $tagihan->alamat; ?>"
                                                data-pemakaian="<?php echo $tagihan->pemakaian; ?>"
                                                data-tagihan="<?php echo $tagihan->total_tagihan; ?>"
                                                <?php echo (isset($selected_tagihan) && $selected_tagihan == $tagihan->id) ? 'selected' : ''; ?>>
                                            <?php echo $tagihan->no_pelanggan . ' - ' . $tagihan->nama_pelanggan . ' (Rp ' . number_format($tagihan->total_tagihan) . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>Tidak ada tagihan yang belum dibayar</option>
                                <?php endif; ?>
                            </select>
                            <?php echo form_error('id_penggunaan', '<small class="text-danger">', '</small>'); ?>
                            
                            <?php if (isset($tagihan_list) && empty($tagihan_list)): ?>
                            <div class="alert alert-warning mt-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Info:</strong> Semua tagihan untuk periode <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?> sudah dibayar.
                                <br>
                                <a href="<?php echo base_url('admin/pembayaran/add?bulan=' . date('m') . '&tahun=' . date('Y')); ?>" 
                                   class="btn btn-sm btn-primary mt-2">
                                    <i class="fas fa-calendar"></i> Lihat Bulan Ini
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="metode_bayar" class="form-label">Metode Pembayaran <span class="text-danger">*</span></label>
                            <select class="form-select" id="metode_bayar" name="metode_bayar" required>
                                <option value="">-- Pilih Metode --</option>
                                <option value="tunai">💵 Tunai</option>
                                <option value="transfer">🏦 Transfer Bank</option>
                                <option value="qris">📱 QRIS</option>
                                <option value="debit">💳 Kartu Debit</option>
                            </select>
                            <?php echo form_error('metode_bayar', '<small class="text-danger">', '</small>'); ?>
                        </div>
                    </div>
                </div>

                <!-- Info Pelanggan -->
                <div id="info-pelanggan" class="alert alert-light d-none">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Pelanggan:</strong> <span id="show-nama-pelanggan"></span><br>
                            <strong>No. Pelanggan:</strong> <span id="show-no-pelanggan"></span><br>
                            <strong>Alamat:</strong> <span id="show-alamat"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>Pemakaian:</strong> <span id="show-pemakaian" class="text-primary"></span> m³<br>
                            <strong>Total Tagihan:</strong> <span id="show-tagihan" class="text-success fw-bold"></span><br>
                            <strong>Periode:</strong> <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="jumlah_bayar" class="form-label">Jumlah Bayar <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="jumlah_bayar" name="jumlah_bayar" 
                                   min="0" step="1000" placeholder="Masukkan jumlah bayar" required>
                            <?php echo form_error('jumlah_bayar', '<small class="text-danger">', '</small>'); ?>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle"></i> Akan auto terisi sesuai tagihan
                            </small>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="tanggal_bayar" class="form-label">Tanggal Bayar <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="tanggal_bayar" name="tanggal_bayar" 
                                   value="<?php echo date('Y-m-d'); ?>" required>
                            <?php echo form_error('tanggal_bayar', '<small class="text-danger">', '</small>'); ?>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="selisih" class="form-label">Selisih</label>
                            <input type="text" class="form-control" id="selisih" readonly 
                                   style="background-color: #e9ecef; font-weight: bold;">
                            <small class="form-text text-muted">
                                <i class="fas fa-calculator"></i> Otomatis: Bayar - Tagihan
                            </small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="keterangan" class="form-label">Keterangan</label>
                    <textarea class="form-control" id="keterangan" name="keterangan" rows="3" 
                              placeholder="Keterangan tambahan (opsional)"></textarea>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary" id="btn-submit" disabled>
                        <i class="fas fa-save me-2"></i>Simpan Pembayaran
                    </button>
                    <a href="<?php echo base_url('admin/pembayaran'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Batal
                    </a>
                </div>

                <?php echo form_close(); ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Tagihan Belum Dibayar -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-list me-2"></i>Tagihan Belum Dibayar</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($tagihan_list)): ?>
                    <p class="text-muted">Total: <strong><?php echo count($tagihan_list); ?> tagihan</strong></p>
                    <div class="list-group list-group-flush" style="max-height: 300px; overflow-y: auto;">
                        <?php foreach (array_slice($tagihan_list, 0, 10) as $tagihan): ?>
                        <a href="#" class="list-group-item list-group-item-action p-2 select-tagihan" 
                           data-id="<?php echo $tagihan->id; ?>">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1"><?php echo $tagihan->nama_pelanggan; ?></h6>
                                <small class="text-primary"><?php echo $tagihan->no_pelanggan; ?></small>
                            </div>
                            <p class="mb-1 text-success fw-bold"><?php echo format_rupiah($tagihan->total_tagihan); ?></p>
                            <small class="text-muted"><?php echo $tagihan->pemakaian; ?> m³</small>
                        </a>
                        <?php endforeach; ?>
                        
                        <?php if (count($tagihan_list) > 10): ?>
                        <div class="text-center p-2">
                            <small class="text-muted">Dan <?php echo count($tagihan_list) - 10; ?> tagihan lainnya...</small>
                        </div>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted mb-0">Semua tagihan sudah dibayar</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Info Pembayaran -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Info Pembayaran</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info p-3">
                    <ul class="mb-0" style="font-size: 14px;">
                        <li>Pilih tagihan yang akan dibayar</li>
                        <li>Jumlah bayar akan auto terisi sesuai tagihan</li>
                        <li>Bisa bayar lebih atau kurang dari tagihan</li>
                        <li>Status awal: Pending (perlu verifikasi)</li>
                        <li>Nomor kwitansi akan auto generate</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Pembayaran form loaded');
    
    // Event tagihan change
    document.getElementById('id_penggunaan').addEventListener('change', function() {
        const tagihanId = this.value;
        const selectedOption = this.options[this.selectedIndex];
        
        if (tagihanId) {
            // Show info pelanggan
            document.getElementById('show-nama-pelanggan').textContent = selectedOption.dataset.pelanggan;
            document.getElementById('show-no-pelanggan').textContent = selectedOption.dataset.no;
            document.getElementById('show-alamat').textContent = selectedOption.dataset.alamat;
            document.getElementById('show-pemakaian').textContent = selectedOption.dataset.pemakaian;
            document.getElementById('show-tagihan').textContent = 'Rp ' + parseInt(selectedOption.dataset.tagihan).toLocaleString('id-ID');
            document.getElementById('info-pelanggan').classList.remove('d-none');
            
            // Set jumlah bayar
            document.getElementById('jumlah_bayar').value = selectedOption.dataset.tagihan;
            
            // Calculate selisih
            hitungSelisih();
            
            // Enable submit button
            cekButtonSubmit();
        } else {
            document.getElementById('info-pelanggan').classList.add('d-none');
            document.getElementById('jumlah_bayar').value = '';
            document.getElementById('selisih').value = '';
            cekButtonSubmit();
        }
    });
    
    // Event jumlah bayar change
    document.getElementById('jumlah_bayar').addEventListener('input', function() {
        hitungSelisih();
        cekButtonSubmit();
    });
    
    // Event metode bayar change
    document.getElementById('metode_bayar').addEventListener('change', function() {
        cekButtonSubmit();
    });
    
    // Quick select tagihan
    document.querySelectorAll('.select-tagihan').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const tagihanId = this.dataset.id;
            document.getElementById('id_penggunaan').value = tagihanId;
            document.getElementById('id_penggunaan').dispatchEvent(new Event('change'));
        });
    });
    
    function hitungSelisih() {
        const selectedOption = document.getElementById('id_penggunaan').options[document.getElementById('id_penggunaan').selectedIndex];
        const tagihan = parseInt(selectedOption.dataset.tagihan) || 0;
        const bayar = parseInt(document.getElementById('jumlah_bayar').value) || 0;
        const selisih = bayar - tagihan;
        
        let selisihText = '';
        if (selisih > 0) {
            selisihText = '+Rp ' + selisih.toLocaleString('id-ID') + ' (Lebih)';
            document.getElementById('selisih').style.color = '#28a745';
        } else if (selisih < 0) {
            selisihText = 'Rp ' + selisih.toLocaleString('id-ID') + ' (Kurang)';
            document.getElementById('selisih').style.color = '#dc3545';
        } else {
            selisihText = 'Rp 0 (Pas)';
            document.getElementById('selisih').style.color = '#6c757d';
        }
        
        document.getElementById('selisih').value = selisihText;
    }
    
    function cekButtonSubmit() {
        const tagihanId = document.getElementById('id_penggunaan').value;
        const metode = document.getElementById('metode_bayar').value;
        const jumlah = document.getElementById('jumlah_bayar').value;
        const tanggal = document.getElementById('tanggal_bayar').value;
        
        const btnSubmit = document.getElementById('btn-submit');
        
        if (tagihanId && metode && jumlah && tanggal && parseInt(jumlah) > 0) {
            btnSubmit.disabled = false;
            btnSubmit.classList.remove('btn-secondary');
            btnSubmit.classList.add('btn-primary');
        } else {
            btnSubmit.disabled = true;
            btnSubmit.classList.remove('btn-primary');
            btnSubmit.classList.add('btn-secondary');
        }
    }
    
    // Pre-select tagihan from URL
    <?php if ($selected_tagihan): ?>
    document.getElementById('id_penggunaan').dispatchEvent(new Event('change'));
    <?php endif; ?>
});
</script>

<?php $this->load->view('templates/footer'); ?>
