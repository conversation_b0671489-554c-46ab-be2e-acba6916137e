<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>Detail Pelanggan
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('penagih/pelanggan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Informasi Pelanggan -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-id-card me-2"></i>Informasi Pelanggan</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-lg bg-primary text-white rounded-circle mx-auto d-flex align-items-center justify-content-center mb-3">
                        <i class="fas fa-user fa-3x"></i>
                    </div>
                    <h4 class="mb-1"><?php echo $pelanggan->nama_pelanggan; ?></h4>
                    <span class="badge bg-<?php echo ($pelanggan->status_langganan == 'aktif') ? 'success' : (($pelanggan->status_langganan == 'nonaktif') ? 'warning' : 'danger'); ?> fs-6">
                        <?php echo ucfirst($pelanggan->status_langganan); ?>
                    </span>
                </div>

                <table class="table table-borderless">
                    <tr>
                        <td class="text-muted" width="40%">No. Pelanggan:</td>
                        <td><strong class="text-primary"><?php echo $pelanggan->no_pelanggan; ?></strong></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Email:</td>
                        <td>
                            <?php if ($pelanggan->email): ?>
                                <a href="mailto:<?php echo $pelanggan->email; ?>" class="text-decoration-none">
                                    <i class="fas fa-envelope text-primary"></i> <?php echo $pelanggan->email; ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">No. HP:</td>
                        <td>
                            <?php if ($pelanggan->no_hp): ?>
                                <a href="tel:<?php echo $pelanggan->no_hp; ?>" class="text-decoration-none">
                                    <i class="fas fa-phone text-success"></i> <?php echo $pelanggan->no_hp; ?>
                                </a>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">Alamat:</td>
                        <td><?php echo $pelanggan->alamat; ?></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Tgl. Pemasangan:</td>
                        <td><?php echo format_tanggal($pelanggan->tanggal_pemasangan); ?></td>
                    </tr>
                    <tr>
                        <td class="text-muted">Bergabung:</td>
                        <td><?php echo format_tanggal($pelanggan->created_at); ?></td>
                    </tr>
                </table>

                <div class="d-grid gap-2 mt-4">
                    <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $pelanggan->id); ?>" 
                       class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Input Penggunaan Air
                    </a>
                    <?php if ($pelanggan->no_hp): ?>
                    <a href="https://wa.me/62<?php echo substr($pelanggan->no_hp, 1); ?>?text=Halo%20<?php echo urlencode($pelanggan->nama_pelanggan); ?>,%20ini%20dari%20PDAM%20Tirta%20Sejahtera" 
                       target="_blank" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>Hubungi WhatsApp
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                            <h5 class="mb-1"><?php echo count($riwayat_penggunaan); ?></h5>
                            <small>Pencatatan Terakhir</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                            <h5 class="mb-1"><?php echo count($riwayat_pembayaran); ?></h5>
                            <small>Pembayaran Terakhir</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                    <div class="card-body">
                        <div class="text-center">
                            <i class="fas fa-calendar fa-2x mb-2"></i>
                            <h5 class="mb-1"><?php echo date('Y') - date('Y', strtotime($pelanggan->tanggal_pemasangan)); ?></h5>
                            <small>Tahun Berlangganan</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs untuk Riwayat -->
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="penggunaan-tab" data-bs-toggle="tab" data-bs-target="#penggunaan" type="button" role="tab">
                            <i class="fas fa-tachometer-alt me-2"></i>Riwayat Penggunaan
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pembayaran-tab" data-bs-toggle="tab" data-bs-target="#pembayaran" type="button" role="tab">
                            <i class="fas fa-money-bill-wave me-2"></i>Riwayat Pembayaran
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="myTabContent">
                    <!-- Tab Riwayat Penggunaan -->
                    <div class="tab-pane fade show active" id="penggunaan" role="tabpanel">
                        <?php if (!empty($riwayat_penggunaan)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Periode</th>
                                        <th>Meter Awal</th>
                                        <th>Meter Akhir</th>
                                        <th>Pemakaian</th>
                                        <th>Tarif</th>
                                        <th>Total Tagihan</th>
                                        <th>Tgl. Catat</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($riwayat_penggunaan as $penggunaan): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo nama_bulan($penggunaan->bulan) . ' ' . $penggunaan->tahun; ?></strong>
                                        </td>
                                        <td><?php echo number_format($penggunaan->meter_awal); ?> m³</td>
                                        <td><?php echo number_format($penggunaan->meter_akhir); ?> m³</td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo number_format($penggunaan->pemakaian); ?> m³</span>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo $penggunaan->nama_tarif; ?></small><br>
                                            <strong><?php echo format_rupiah($penggunaan->tarif_per_m3); ?>/m³</strong>
                                        </td>
                                        <td>
                                            <strong class="text-success"><?php echo format_rupiah($penggunaan->total_tagihan); ?></strong>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo format_tanggal($penggunaan->tanggal_catat); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-tachometer-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Belum ada riwayat penggunaan</h5>
                            <p class="text-muted">Pencatatan penggunaan air belum tersedia</p>
                            <a href="<?php echo base_url('penagih/penggunaan/add?pelanggan=' . $pelanggan->id); ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Input Penggunaan Pertama
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Tab Riwayat Pembayaran -->
                    <div class="tab-pane fade" id="pembayaran" role="tabpanel">
                        <?php if (!empty($riwayat_pembayaran)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Periode</th>
                                        <th>No. Kwitansi</th>
                                        <th>Tgl. Bayar</th>
                                        <th>Jumlah Bayar</th>
                                        <th>Metode</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($riwayat_pembayaran as $bayar): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo nama_bulan($bayar->bulan) . ' ' . $bayar->tahun; ?></strong>
                                        </td>
                                        <td>
                                            <span class="text-primary"><?php echo $bayar->no_kwitansi; ?></span>
                                        </td>
                                        <td><?php echo format_tanggal($bayar->tanggal_bayar); ?></td>
                                        <td>
                                            <strong class="text-success"><?php echo format_rupiah($bayar->jumlah_bayar); ?></strong>
                                            <br><small class="text-muted">Tagihan: <?php echo format_rupiah($bayar->total_tagihan); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $metode_class = '';
                                            switch ($bayar->metode_bayar) {
                                                case 'tunai': $metode_class = 'success'; break;
                                                case 'transfer': $metode_class = 'primary'; break;
                                                case 'qris': $metode_class = 'info'; break;
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $metode_class; ?>">
                                                <?php echo ucfirst($bayar->metode_bayar); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            $status_icon = '';
                                            switch ($bayar->status_verifikasi) {
                                                case 'verified':
                                                    $status_class = 'success';
                                                    $status_icon = 'check-circle';
                                                    break;
                                                case 'pending':
                                                    $status_class = 'warning';
                                                    $status_icon = 'clock';
                                                    break;
                                                case 'rejected':
                                                    $status_class = 'danger';
                                                    $status_icon = 'times-circle';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <i class="fas fa-<?php echo $status_icon; ?>"></i> 
                                                <?php echo ucfirst($bayar->status_verifikasi); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Belum ada riwayat pembayaran</h5>
                            <p class="text-muted">Pembayaran belum tersedia</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 24px;
}

@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .nav-tabs {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .tab-content {
        display: block !important;
    }
    .tab-pane {
        display: block !important;
    }
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
}
</style>

<?php $this->load->view('templates/footer'); ?>