<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-envelope me-2"></i><PERSON><PERSON>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pengaturan/email'); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <button type="button" class="btn btn-outline-info" onclick="kirimEmailMassal()">
                <i class="fas fa-paper-plane"></i> <PERSON><PERSON>
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-lg-8">
        <!-- Form Kirim Email -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Pilih Pelanggan & Tagihan
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="pelanggan_select" class="form-label">Pilih Pelanggan <span class="text-danger">*</span></label>
                        <select class="form-select" id="pelanggan_select" onchange="loadTagihan()" required>
                            <option value="">-- Pilih Pelanggan --</option>
                            <?php foreach ($pelanggan_list as $pelanggan): ?>
                            <option value="<?php echo $pelanggan->id; ?>" data-email="<?php echo $pelanggan->email; ?>" data-nama="<?php echo htmlspecialchars($pelanggan->nama_pelanggan); ?>">
                                <?php echo $pelanggan->no_pelanggan . ' - ' . htmlspecialchars($pelanggan->nama_pelanggan); ?>
                                (<?php echo $pelanggan->total_tagihan; ?> tagihan)
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="email_tujuan" class="form-label">Email Tujuan</label>
                        <input type="email" class="form-control" id="email_tujuan" readonly placeholder="Email akan otomatis terisi">
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="tagihan_select" class="form-label">Pilih Tagihan <span class="text-danger">*</span></label>
                    <select class="form-select" id="tagihan_select" onchange="showTagihanDetail()" disabled required>
                        <option value="">-- Pilih pelanggan dulu --</option>
                    </select>
                </div>
                
                <div id="tagihan_detail" class="d-none">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">Preview Tagihan</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td>Periode:</td>
                                            <td><strong id="detail_periode">-</strong></td>
                                        </tr>
                                        <tr>
                                            <td>Pemakaian:</td>
                                            <td><strong id="detail_pemakaian">-</strong> m³</td>
                                        </tr>
                                        <tr>
                                            <td>Tarif:</td>
                                            <td id="detail_tarif">-</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td>Meter Awal:</td>
                                            <td id="detail_meter_awal">-</td>
                                        </tr>
                                        <tr>
                                            <td>Meter Akhir:</td>
                                            <td id="detail_meter_akhir">-</td>
                                        </tr>
                                        <tr class="table-warning">
                                            <td><strong>Total Tagihan:</strong></td>
                                            <td><strong id="detail_total" class="text-danger">-</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid mt-4">
                    <button type="button" class="btn btn-primary btn-lg" onclick="kirimEmailTagihan()" id="btn_kirim" disabled>
                        <i class="fas fa-paper-plane me-2"></i>Kirim Email Tagihan
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Email Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Statistik Email Hari Ini
                </h6>
            </div>
            <div class="card-body">
                <?php 
                $today = date('Y-m-d');
                $this->db->where('DATE(created_at)', $today);
                $this->db->where('status', 'sent');
                $email_sent_today = $this->db->count_all_results('email_logs');
                
                $this->db->where('DATE(created_at)', $today);
                $this->db->where('status', 'failed');
                $email_failed_today = $this->db->count_all_results('email_logs');
                ?>
                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="text-success"><?php echo $email_sent_today; ?></h3>
                        <small class="text-muted">Terkirim</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-danger"><?php echo $email_failed_today; ?></h3>
                        <small class="text-muted">Gagal</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Template Preview -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>Preview Email
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="border rounded p-3 mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <h6 class="mb-1">💧 PDAM Tirta Sejahtera</h6>
                        <small>Tagihan Air Periode [BULAN TAHUN]</small>
                    </div>
                    <p class="small text-muted">Template email tagihan dengan:</p>
                    <ul class="small text-start text-muted">
                        <li>Info pelanggan lengkap</li>
                        <li>Rincian pemakaian air</li>
                        <li>Total tagihan & jatuh tempo</li>
                        <li>Cara pembayaran</li>
                        <li>Design responsif & menarik</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="previewTemplate()">
                        <i class="fas fa-eye"></i> Preview Template
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="testEmailSystem()">
                        <i class="fas fa-vial"></i> Test Email System
                    </button>
                    <a href="<?php echo base_url('admin/pengaturan/email'); ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-cog"></i> Email Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Preview Template -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Preview Template Email Tagihan</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedPelanggan = null;
let selectedTagihan = null;

// Load tagihan when pelanggan is selected
function loadTagihan() {
    const pelangganSelect = document.getElementById('pelanggan_select');
    const tagihanSelect = document.getElementById('tagihan_select');
    const emailTujuan = document.getElementById('email_tujuan');
    const btnKirim = document.getElementById('btn_kirim');
    
    // Reset
    tagihanSelect.innerHTML = '<option value="">-- Loading... --</option>';
    tagihanSelect.disabled = true;
    emailTujuan.value = '';
    btnKirim.disabled = true;
    hideTagihanDetail();
    
    if (!pelangganSelect.value) {
        tagihanSelect.innerHTML = '<option value="">-- Pilih pelanggan dulu --</option>';
        return;
    }
    
    // Get selected option data
    const selectedOption = pelangganSelect.options[pelangganSelect.selectedIndex];
    const email = selectedOption.getAttribute('data-email');
    const nama = selectedOption.getAttribute('data-nama');
    
    selectedPelanggan = {
        id: pelangganSelect.value,
        email: email,
        nama: nama
    };
    
    emailTujuan.value = email;
    
    // AJAX call to get tagihan
    fetch('<?php echo base_url("admin/pengaturan/get_tagihan_pelanggan"); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'pelanggan_id=' + encodeURIComponent(pelangganSelect.value)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateTagihanOptions(data.data);
        } else {
            showAlert('error', data.message || 'Gagal memuat data tagihan');
            tagihanSelect.innerHTML = '<option value="">-- Tidak ada tagihan --</option>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Terjadi kesalahan saat memuat tagihan');
        tagihanSelect.innerHTML = '<option value="">-- Error loading --</option>';
    });
}

// Populate tagihan options
function populateTagihanOptions(tagihanList) {
    const tagihanSelect = document.getElementById('tagihan_select');
    
    if (tagihanList.length === 0) {
        tagihanSelect.innerHTML = '<option value="">-- Tidak ada tagihan --</option>';
        return;
    }
    
    let html = '<option value="">-- Pilih Tagihan --</option>';
    
    tagihanList.forEach(tagihan => {
        const periode = formatPeriode(tagihan.bulan, tagihan.tahun);
        const totalFormatted = formatRupiah(tagihan.total_tagihan);
        
        html += `<option value="${tagihan.id}" data-tagihan='${JSON.stringify(tagihan)}'>
            ${periode} - ${totalFormatted} (${tagihan.pemakaian} m³)
        </option>`;
    });
    
    tagihanSelect.innerHTML = html;
    tagihanSelect.disabled = false;
}

// Show tagihan detail
function showTagihanDetail() {
    const tagihanSelect = document.getElementById('tagihan_select');
    const btnKirim = document.getElementById('btn_kirim');
    
    if (!tagihanSelect.value) {
        hideTagihanDetail();
        btnKirim.disabled = true;
        return;
    }
    
    const selectedOption = tagihanSelect.options[tagihanSelect.selectedIndex];
    const tagihan = JSON.parse(selectedOption.getAttribute('data-tagihan'));
    
    selectedTagihan = tagihan;
    
    // Populate detail
    document.getElementById('detail_periode').textContent = formatPeriode(tagihan.bulan, tagihan.tahun);
    document.getElementById('detail_pemakaian').textContent = formatNumber(tagihan.pemakaian);
    document.getElementById('detail_tarif').textContent = tagihan.nama_tarif + ' - Rp ' + formatNumber(tagihan.tarif_per_m3) + '/m³';
    document.getElementById('detail_meter_awal').textContent = formatNumber(tagihan.meter_awal) + ' m³';
    document.getElementById('detail_meter_akhir').textContent = formatNumber(tagihan.meter_akhir) + ' m³';
    document.getElementById('detail_total').textContent = formatRupiah(tagihan.total_tagihan);
    
    // Show detail
    document.getElementById('tagihan_detail').classList.remove('d-none');
    btnKirim.disabled = false;
}

// Hide tagihan detail
function hideTagihanDetail() {
    document.getElementById('tagihan_detail').classList.add('d-none');
    selectedTagihan = null;
}

// Kirim email tagihan
function kirimEmailTagihan() {
    if (!selectedPelanggan || !selectedTagihan) {
        showAlert('warning', 'Pilih pelanggan dan tagihan terlebih dahulu');
        return;
    }
    
    if (!selectedPelanggan.email) {
        showAlert('error', 'Email pelanggan tidak tersedia');
        return;
    }
    
    const confirmMsg = `Kirim email tagihan ke:\n${selectedPelanggan.nama}\n${selectedPelanggan.email}\n\nPeriode: ${formatPeriode(selectedTagihan.bulan, selectedTagihan.tahun)}\nTotal: ${formatRupiah(selectedTagihan.total_tagihan)}`;
    
    if (!confirm(confirmMsg)) {
        return;
    }
    
    const btnKirim = document.getElementById('btn_kirim');
    const originalText = btnKirim.innerHTML;
    btnKirim.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mengirim Email...';
    btnKirim.disabled = true;
    
    // AJAX call to send email
    fetch('<?php echo base_url("admin/pengaturan/kirim_email_tagihan"); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: `pelanggan_id=${selectedPelanggan.id}&penggunaan_id=${selectedTagihan.id}`
    })
    .then(response => response.json())
    .then(data => {
        btnKirim.innerHTML = originalText;
        btnKirim.disabled = false;
        
        if (data.success) {
            showAlert('success', data.message);
            // Reset form
            resetForm();
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        btnKirim.innerHTML = originalText;
        btnKirim.disabled = false;
        console.error('Error:', error);
        showAlert('error', 'Terjadi kesalahan saat mengirim email');
    });
}

// Reset form
function resetForm() {
    document.getElementById('pelanggan_select').value = '';
    document.getElementById('tagihan_select').innerHTML = '<option value="">-- Pilih pelanggan dulu --</option>';
    document.getElementById('tagihan_select').disabled = true;
    document.getElementById('email_tujuan').value = '';
    document.getElementById('btn_kirim').disabled = true;
    hideTagihanDetail();
    
    selectedPelanggan = null;
    selectedTagihan = null;
}

// Preview template
function previewTemplate() {
    const sampleData = {
        pelanggan: {
            no_pelanggan: 'PLG202400001',
            nama_pelanggan: 'Budi Santoso',
            alamat: 'Jl. Merdeka No. 1, RT 01/RW 01',
            email: '<EMAIL>'
        },
        tagihan: {
            bulan: 12,
            tahun: 2024,
            meter_awal: 100,
            meter_akhir: 125,
            pemakaian: 25,
            nama_tarif: 'Tarif Rumah Tangga',
            tarif_per_m3: 2500,
            total_tagihan: 87500
        },
        jatuh_tempo: '31/01/2025'
    };
    
    const previewHTML = generateEmailPreview(sampleData);
    document.getElementById('previewContent').innerHTML = previewHTML;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Generate email preview
function generateEmailPreview(data) {
    const periode = formatPeriode(data.tagihan.bulan, data.tagihan.tahun);
    
    return `
    <div style="max-width: 650px; margin: 0 auto; font-family: Arial, sans-serif; line-height: 1.6;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="margin: 0; font-size: 28px;">💧 PDAM Tirta Sejahtera</h1>
            <p style="margin: 5px 0 0 0; font-size: 16px; opacity: 0.9;">Tagihan Air Periode ${periode}</p>
        </div>
        
        <div style="background: white; padding: 30px; border: 1px solid #e0e0e0;">
            <h3 style="color: #333; margin-bottom: 15px;">Yth. ${data.pelanggan.nama_pelanggan},</h3>
            
            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h4 style="color: #495057; margin-top: 0;">📋 Informasi Pelanggan</h4>
                <p><strong>No. Pelanggan:</strong> ${data.pelanggan.no_pelanggan}</p>
                <p><strong>Nama:</strong> ${data.pelanggan.nama_pelanggan}</p>
                <p><strong>Alamat:</strong> ${data.pelanggan.alamat}</p>
            </div>
            
            <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
                <h4 style="color: #856404; margin-top: 0;">💰 Rincian Tagihan</h4>
                <p><strong>Periode:</strong> ${periode}</p>
                <p><strong>Pemakaian:</strong> ${data.tagihan.pemakaian} m³</p>
                <p><strong>Total Tagihan:</strong> <span style="font-size: 24px; color: #dc3545;">${formatRupiah(data.tagihan.total_tagihan)}</span></p>
            </div>
            
            <div style="background: #d1ecf1; padding: 20px; border-radius: 8px;">
                <h4 style="color: #0c5460; margin-top: 0;">⏰ Jatuh Tempo</h4>
                <p style="font-size: 18px; font-weight: bold;">${data.jatuh_tempo}</p>
            </div>
        </div>
    </div>`;
}

// Test email system
function testEmailSystem() {
    const email = prompt('Masukkan email untuk test:');
    if (!email) return;
    
    // Reuse test email function from email settings
    fetch('<?php echo base_url("admin/pengaturan/test_email"); ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'test_email=' + encodeURIComponent(email)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'Gagal mengirim test email');
    });
}

// Kirim email massal (placeholder)
function kirimEmailMassal() {
    alert('Fitur kirim email massal akan segera hadir!\n\nFitur ini akan memungkinkan pengiriman email tagihan ke semua pelanggan sekaligus.');
}

// Utility functions
function formatPeriode(bulan, tahun) {
    const namaBulan = [
        '', 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
        'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
    ];
    return namaBulan[bulan] + ' ' + tahun;
}

function formatNumber(num) {
    return new Intl.NumberFormat('id-ID').format(num);
}

function formatRupiah(num) {
    return 'Rp ' + new Intl.NumberFormat('id-ID').format(num);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-refresh statistics every 30 seconds
setInterval(() => {
    // In real implementation, use AJAX to refresh statistics
    console.log('Auto-refresh statistics...');
}, 30000);
</script>

<?php $this->load->view('templates/footer'); ?>