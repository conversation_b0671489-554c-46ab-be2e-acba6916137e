<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tint me-2"></i>Laporan Penggunaan Air</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/laporan'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo base_url('admin/laporan/export/penggunaan?bulan=' . $bulan_filter . '&tahun=' . $tahun_filter); ?>" 
               class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Export CSV
            </a>
            <button onclick="window.print()" class="btn btn-sm btn-info">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Periode</h5>
    </div>
    <div class="card-body">
        <?php echo form_open('', ['method' => 'GET', 'class' => 'row g-3']); ?>
        
        <div class="col-md-3">
            <label for="bulan" class="form-label">Bulan</label>
            <select class="form-select" id="bulan" name="bulan">
                <?php for ($i = 1; $i <= 12; $i++): ?>
                    <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>" 
                            <?php echo ($bulan_filter == str_pad($i, 2, '0', STR_PAD_LEFT)) ? 'selected' : ''; ?>>
                        <?php echo nama_bulan($i); ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-3">
            <label for="tahun" class="form-label">Tahun</label>
            <select class="form-select" id="tahun" name="tahun">
                <?php for ($i = date('Y') - 2; $i <= date('Y') + 1; $i++): ?>
                    <option value="<?php echo $i; ?>" <?php echo ($tahun_filter == $i) ? 'selected' : ''; ?>>
                        <?php echo $i; ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
            </div>
        </div>
        
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <a href="<?php echo base_url('admin/laporan/penggunaan?bulan=' . date('m') . '&tahun=' . date('Y')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-sync"></i> Bulan Ini
                </a>
            </div>
        </div>
        
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Record</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_records); ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Pemakaian</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_pemakaian); ?> m³</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Tagihan</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($total_tagihan); ?></div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Periode</div>
                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?></div>
            </div>
        </div>
    </div>
</div>

<!-- Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Data Penggunaan Air - <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($penggunaan)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th width="50">No</th>
                        <th>No. Pelanggan</th>
                        <th>Nama Pelanggan</th>
                        <th>Alamat</th>
                        <th>Meter Awal</th>
                        <th>Meter Akhir</th>
                        <th>Pemakaian</th>
                        <th>Tarif</th>
                        <th>Total Tagihan</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($penggunaan as $index => $p): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><strong><?php echo $p->no_pelanggan; ?></strong></td>
                        <td><?php echo $p->nama_pelanggan; ?></td>
                        <td><?php echo $p->alamat; ?></td>
                        <td class="text-center"><?php echo number_format($p->meter_awal); ?></td>
                        <td class="text-center"><?php echo number_format($p->meter_akhir); ?></td>
                        <td class="text-center">
                            <span class="badge bg-primary"><?php echo number_format($p->pemakaian); ?> m³</span>
                        </td>
                        <td><?php echo $p->nama_tarif; ?></td>
                        <td class="text-end">
                            <strong class="text-success"><?php echo format_rupiah($p->total_tagihan); ?></strong>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-secondary">
                    <tr>
                        <th colspan="6" class="text-end">TOTAL:</th>
                        <th class="text-center">
                            <span class="badge bg-success"><?php echo number_format($total_pemakaian); ?> m³</span>
                        </th>
                        <th></th>
                        <th class="text-end">
                            <strong class="text-success"><?php echo format_rupiah($total_tagihan); ?></strong>
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-tint fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada data penggunaan</h5>
            <p class="text-muted">Belum ada data penggunaan air untuk periode <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?></p>
            <a href="<?php echo base_url('penagih/penggunaan/add'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Input Penggunaan
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .table {
        font-size: 12px !important;
    }
    .badge {
        background-color: #6c757d !important;
        color: white !important;
    }
}
</style>

<?php $this->load->view('templates/footer'); ?>
