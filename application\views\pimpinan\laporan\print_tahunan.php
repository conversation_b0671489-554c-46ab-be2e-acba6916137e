<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { font-size: 12px; }
            .card { border: 1px solid #ddd !important; box-shadow: none !important; }
            .page-break { page-break-before: always; }
        }
        
        .header-company {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .signature-area {
            margin-top: 50px;
            text-align: right;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .stats-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Print Button -->
        <div class="no-print mb-3">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Cetak Laporan
            </button>
            <a href="<?php echo base_url('pimpinan/laporan/tahunan?tahun=' . $tahun); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>

        <!-- Header Perusahaan -->
        <div class="header-company">
            <h2 class="mb-1"><?php echo $nama_perusahaan; ?></h2>
            <p class="mb-1"><?php echo $alamat_perusahaan; ?></p>
            <hr class="my-3">
            <h3 class="text-primary">LAPORAN TAHUNAN</h3>
            <h4>TAHUN <?php echo $tahun; ?></h4>
            <small class="text-muted">Dicetak pada: <?php echo $tanggal_cetak; ?></small>
        </div>

        <!-- Ringkasan Eksekutif -->
        <div class="stats-summary">
            <div class="row text-center">
                <div class="col-md-3">
                    <h4><?php echo number_format($total_tahun_ini['pencatatan']); ?></h4>
                    <small>Total Pencatatan</small>
                </div>
                <div class="col-md-3">
                    <h4><?php echo number_format($total_tahun_ini['pemakaian']); ?> m³</h4>
                    <small>Total Pemakaian</small>
                </div>
                <div class="col-md-3">
                    <h4><?php echo format_rupiah($total_tahun_ini['tagihan']); ?></h4>
                    <small>Total Tagihan</small>
                </div>
                <div class="col-md-3">
                    <h4><?php echo format_rupiah($total_tahun_ini['terbayar']); ?></h4>
                    <small>Total Terbayar</small>
                </div>
            </div>
        </div>

        <!-- Tabel Ringkasan Bulanan -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Ringkasan Bulanan Tahun <?php echo $tahun; ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Bulan</th>
                                <th class="text-center">Pencatatan</th>
                                <th class="text-center">Pemakaian (m³)</th>
                                <th class="text-end">Tagihan</th>
                                <th class="text-end">Terbayar</th>
                                <th class="text-center">Pembayaran</th>
                                <th class="text-center">%</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($ringkasan_bulanan as $bulan): ?>
                            <tr>
                                <td><strong><?php echo $bulan['nama_bulan']; ?></strong></td>
                                <td class="text-center"><?php echo number_format($bulan['pencatatan']); ?></td>
                                <td class="text-center"><?php echo number_format($bulan['total_pemakaian']); ?></td>
                                <td class="text-end"><?php echo format_rupiah($bulan['total_tagihan']); ?></td>
                                <td class="text-end"><?php echo format_rupiah($bulan['total_terbayar']); ?></td>
                                <td class="text-center"><?php echo number_format($bulan['jumlah_pembayaran']); ?></td>
                                <td class="text-center">
                                    <?php 
                                    $persentase = $bulan['total_tagihan'] > 0 ? ($bulan['total_terbayar'] / $bulan['total_tagihan']) * 100 : 0;
                                    echo number_format($persentase, 1) . '%';
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="table-warning">
                            <tr>
                                <th>TOTAL</th>
                                <th class="text-center"><?php echo number_format($total_tahun_ini['pencatatan']); ?></th>
                                <th class="text-center"><?php echo number_format($total_tahun_ini['pemakaian']); ?></th>
                                <th class="text-end"><?php echo format_rupiah($total_tahun_ini['tagihan']); ?></th>
                                <th class="text-end"><?php echo format_rupiah($total_tahun_ini['terbayar']); ?></th>
                                <th class="text-center"><?php echo number_format($total_tahun_ini['pembayaran']); ?></th>
                                <th class="text-center">
                                    <?php 
                                    $total_persentase = $total_tahun_ini['tagihan'] > 0 ? ($total_tahun_ini['terbayar'] / $total_tahun_ini['tagihan']) * 100 : 0;
                                    echo number_format($total_persentase, 1) . '%';
                                    ?>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Analisis Kinerja -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-trophy me-2"></i>Bulan Terbaik</h6>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($top_months)): ?>
                            <?php foreach ($top_months as $index => $month): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <?php if ($index == 0): ?>
                                        <span class="badge bg-warning text-dark me-2"><i class="fas fa-trophy"></i></span>
                                    <?php elseif ($index == 1): ?>
                                        <span class="badge bg-secondary me-2"><i class="fas fa-medal"></i></span>
                                    <?php else: ?>
                                        <span class="badge bg-info me-2"><i class="fas fa-award"></i></span>
                                    <?php endif; ?>
                                    <strong><?php echo $month['nama_bulan']; ?></strong>
                                </div>
                                <span class="text-success"><?php echo format_rupiah($month['total_terbayar']); ?></span>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-muted">Belum ada data</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Rata-rata Bulanan</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td>Pencatatan</td>
                                <td class="text-end"><?php echo number_format($rata_rata_bulanan['pencatatan'], 0); ?></td>
                            </tr>
                            <tr>
                                <td>Pemakaian</td>
                                <td class="text-end"><?php echo number_format($rata_rata_bulanan['pemakaian'], 0); ?> m³</td>
                            </tr>
                            <tr>
                                <td>Tagihan</td>
                                <td class="text-end"><?php echo format_rupiah($rata_rata_bulanan['tagihan']); ?></td>
                            </tr>
                            <tr>
                                <td>Terbayar</td>
                                <td class="text-end"><?php echo format_rupiah($rata_rata_bulanan['terbayar']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kesimpulan -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-clipboard-check me-2"></i>Kesimpulan</h6>
            </div>
            <div class="card-body">
                <ul>
                    <li>Total pencatatan meter tahun <?php echo $tahun; ?>: <strong><?php echo number_format($total_tahun_ini['pencatatan']); ?></strong> pencatatan</li>
                    <li>Total pemakaian air: <strong><?php echo number_format($total_tahun_ini['pemakaian']); ?> m³</strong></li>
                    <li>Total tagihan yang dikeluarkan: <strong><?php echo format_rupiah($total_tahun_ini['tagihan']); ?></strong></li>
                    <li>Total pembayaran yang diterima: <strong><?php echo format_rupiah($total_tahun_ini['terbayar']); ?></strong></li>
                    <li>Tingkat pembayaran: <strong><?php echo number_format($total_persentase, 1); ?>%</strong></li>
                    <li>Sisa piutang: <strong><?php echo format_rupiah($total_tahun_ini['tagihan'] - $total_tahun_ini['terbayar']); ?></strong></li>
                </ul>
            </div>
        </div>

        <!-- Tanda Tangan -->
        <div class="signature-area">
            <p>Kota, <?php echo format_tanggal(date('Y-m-d')); ?></p>
            <p class="mb-5">Pimpinan PDAM</p>
            <p>_________________________</p>
            <p><strong>Nama Pimpinan</strong></p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print function
        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
