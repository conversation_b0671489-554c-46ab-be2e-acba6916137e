<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-chart-bar me-2"></i>Laporan PDAM</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button onclick="window.print()" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Filter Periode -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Periode</h5>
    </div>
    <div class="card-body">
        <?php echo form_open('', ['method' => 'GET', 'class' => 'row g-3']); ?>
        
        <div class="col-md-3">
            <label for="bulan" class="form-label">Bulan</label>
            <select class="form-select" id="bulan" name="bulan">
                <?php for ($i = 1; $i <= 12; $i++): ?>
                    <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>" 
                            <?php echo ($bulan_filter == str_pad($i, 2, '0', STR_PAD_LEFT)) ? 'selected' : ''; ?>>
                        <?php echo nama_bulan($i); ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-3">
            <label for="tahun" class="form-label">Tahun</label>
            <select class="form-select" id="tahun" name="tahun">
                <?php for ($i = date('Y') - 2; $i <= date('Y') + 1; $i++): ?>
                    <option value="<?php echo $i; ?>" <?php echo ($tahun_filter == $i) ? 'selected' : ''; ?>>
                        <?php echo $i; ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
            </div>
        </div>
        
        <div class="col-md-3">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <a href="<?php echo base_url('admin/laporan?bulan=' . date('m') . '&tahun=' . date('Y')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-sync"></i> Bulan Ini
                </a>
            </div>
        </div>
        
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_pelanggan); ?></div>
                        <div class="text-xs text-muted">Aktif: <?php echo number_format($pelanggan_aktif); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Penggunaan Bulan Ini</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_penggunaan); ?></div>
                        <div class="text-xs text-muted"><?php echo number_format($total_pemakaian); ?> m³</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tint fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Tagihan</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($total_tagihan); ?></div>
                        <div class="text-xs text-muted"><?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice-dollar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Total Terbayar</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($total_terbayar); ?></div>
                        <div class="text-xs text-muted">Sisa: <?php echo format_rupiah($sisa_piutang); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Menu Laporan -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tint me-2"></i>Laporan Penggunaan Air</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Laporan detail penggunaan air per pelanggan untuk periode tertentu.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('admin/laporan/penggunaan?bulan=' . $bulan_filter . '&tahun=' . $tahun_filter); ?>" 
                       class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>Lihat Laporan
                    </a>
                    <a href="<?php echo base_url('admin/laporan/export/penggunaan?bulan=' . $bulan_filter . '&tahun=' . $tahun_filter); ?>" 
                       class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>Export CSV
                    </a>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small><i class="fas fa-info-circle me-1"></i>Data: <?php echo number_format($total_penggunaan); ?> record</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Laporan Pembayaran</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Laporan detail pembayaran pelanggan untuk periode tertentu.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('admin/laporan/pembayaran?bulan=' . $bulan_filter . '&tahun=' . $tahun_filter); ?>" 
                       class="btn btn-success">
                        <i class="fas fa-eye me-2"></i>Lihat Laporan
                    </a>
                    <a href="<?php echo base_url('admin/laporan/export/pembayaran?bulan=' . $bulan_filter . '&tahun=' . $tahun_filter); ?>" 
                       class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>Export CSV
                    </a>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small><i class="fas fa-info-circle me-1"></i>Data: <?php echo number_format($total_pembayaran); ?> record</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Laporan Data Pelanggan</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Laporan data pelanggan dengan ringkasan penggunaan dan pembayaran.</p>
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('admin/laporan/pelanggan'); ?>" 
                       class="btn btn-info">
                        <i class="fas fa-eye me-2"></i>Lihat Laporan
                    </a>
                    <a href="<?php echo base_url('admin/laporan/export/pelanggan'); ?>" 
                       class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>Export CSV
                    </a>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small><i class="fas fa-info-circle me-1"></i>Total: <?php echo number_format($total_pelanggan); ?> pelanggan</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Ringkasan Keuangan</h5>
            </div>
            <div class="card-body">
                <p class="card-text">Ringkasan keuangan untuk periode <?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?>.</p>
                <table class="table table-sm">
                    <tr>
                        <td>Total Tagihan:</td>
                        <td class="text-end"><strong><?php echo format_rupiah($total_tagihan); ?></strong></td>
                    </tr>
                    <tr>
                        <td>Total Terbayar:</td>
                        <td class="text-end"><strong class="text-success"><?php echo format_rupiah($total_terbayar); ?></strong></td>
                    </tr>
                    <tr class="table-warning">
                        <td><strong>Sisa Piutang:</strong></td>
                        <td class="text-end"><strong class="text-danger"><?php echo format_rupiah($sisa_piutang); ?></strong></td>
                    </tr>
                </table>
                <?php 
                $persentase_terbayar = $total_tagihan > 0 ? ($total_terbayar / $total_tagihan) * 100 : 0;
                ?>
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $persentase_terbayar; ?>%" 
                         aria-valuenow="<?php echo $persentase_terbayar; ?>" aria-valuemin="0" aria-valuemax="100">
                        <?php echo number_format($persentase_terbayar, 1); ?>%
                    </div>
                </div>
                <small class="text-muted">Persentase terbayar</small>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-footer {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 20px !important;
    }
}
</style>

<?php $this->load->view('templates/footer'); ?>
