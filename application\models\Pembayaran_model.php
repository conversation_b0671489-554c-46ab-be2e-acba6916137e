<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pembayaran_model extends CI_Model {

    protected $table = 'pembayaran';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all pembayaran
     */
    public function get_all($status = null)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, penggunaan_air.total_tagihan, pelanggan.nama_pelanggan, pelanggan.no_pelanggan, pelanggan.alamat, users.nama_lengkap as verifikator_nama');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->join('users', 'users.id = pembayaran.petugas_verifikasi', 'LEFT');
        
        if ($status) {
            $this->db->where('pembayaran.status_verifikasi', $status);
        }
        
        $this->db->order_by('pembayaran.tanggal_bayar', 'DESC');
        return $this->db->get()->result();
    }

    /**
     * Get by ID
     */
    public function get_by_id($id)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, penggunaan_air.total_tagihan, penggunaan_air.pemakaian, pelanggan.nama_pelanggan, pelanggan.no_pelanggan, pelanggan.alamat, users.nama_lengkap as verifikator_nama');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->join('users', 'users.id = pembayaran.petugas_verifikasi', 'LEFT');
        $this->db->where('pembayaran.id', $id);
        return $this->db->get()->row();
    }

    /**
     * Insert pembayaran
     */
    public function insert($data)
    {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update pembayaran
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete pembayaran
     */
    public function delete($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Count by month
     */
    public function count_by_month($bulan, $tahun)
    {
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->where('penggunaan_air.bulan', $bulan);
        $this->db->where('penggunaan_air.tahun', $tahun);
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        return $this->db->count_all_results();
    }

    /**
     * Sum pembayaran by month
     */
    public function sum_by_month($bulan, $tahun)
    {
        $this->db->select_sum('pembayaran.jumlah_bayar');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->where('penggunaan_air.bulan', $bulan);
        $this->db->where('penggunaan_air.tahun', $tahun);
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        $result = $this->db->get()->row();
        return $result->jumlah_bayar ? $result->jumlah_bayar : 0;
    }

    /**
     * Sum verified by month
     */
    public function sum_verified_by_month($bulan, $tahun)
    {
        return $this->sum_by_month($bulan, $tahun);
    }

    /**
     * Count pending verification
     */
    public function count_pending()
    {
        $this->db->where('status_verifikasi', 'pending');
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get pending payments
     */
    public function get_pending($limit = 10)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, penggunaan_air.total_tagihan, pelanggan.nama_pelanggan, pelanggan.no_pelanggan');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->where('pembayaran.status_verifikasi', 'pending');
        $this->db->order_by('pembayaran.created_at', 'ASC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }

    /**
     * Get recent payments
     */
    public function get_recent($limit = 5)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, pelanggan.nama_pelanggan, pelanggan.no_pelanggan');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        $this->db->order_by('pembayaran.created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }

    /**
     * Count verified today by petugas
     */
    public function count_verified_today_by_petugas($petugas_id)
    {
        $this->db->where('petugas_verifikasi', $petugas_id);
        $this->db->where('status_verifikasi', 'verified');
        $this->db->where('DATE(tanggal_verifikasi)', date('Y-m-d'));
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get payment chart data (for last N months)
     */
    public function get_payment_chart_data($months = 6)
    {
        $data = array();

        for ($i = $months - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i months"));
            $bulan = (int)date('m', strtotime($date));
            $tahun = (int)date('Y', strtotime($date));

            // Reset query builder untuk setiap iterasi
            $this->db->reset_query();

            $this->db->select_sum('pembayaran.jumlah_bayar');
            $this->db->from($this->table);
            $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
            $this->db->where('penggunaan_air.bulan', $bulan);
            $this->db->where('penggunaan_air.tahun', $tahun);
            $this->db->where('pembayaran.status_verifikasi', 'verified');
            $result = $this->db->get()->row();

            // Pastikan ada data dan tidak null
            $total = 0;
            if ($result && isset($result->jumlah_bayar) && $result->jumlah_bayar !== null) {
                $total = (float)$result->jumlah_bayar;
            }

            $data[] = array(
                'bulan' => nama_bulan($bulan) . ' ' . $tahun,
                'bulan_num' => $bulan,
                'tahun' => $tahun,
                'total' => $total
            );
        }

        return $data;
    }

    /**
     * Get top payment areas
     */
    public function get_top_payment_areas($limit = 5)
    {
        $bulan_ini = (int)date('m');
        $tahun_ini = (int)date('Y');

        try {
            // Reset query builder
            $this->db->reset_query();

            // Gunakan TRIM untuk membersihkan alamat dan ambil bagian pertama
            $this->db->select('TRIM(SUBSTRING_INDEX(TRIM(pelanggan.alamat), ",", 1)) as area, COUNT(*) as total_pembayaran, SUM(pembayaran.jumlah_bayar) as total_nilai');
            $this->db->from($this->table);
            $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
            $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
            $this->db->where('penggunaan_air.bulan', $bulan_ini);
            $this->db->where('penggunaan_air.tahun', $tahun_ini);
            $this->db->where('pembayaran.status_verifikasi', 'verified');
            $this->db->where('pelanggan.alamat IS NOT NULL');
            $this->db->where('pelanggan.alamat !=', '');
            $this->db->group_by('area');
            $this->db->having('area IS NOT NULL');
            $this->db->having('area !=', '');
            $this->db->order_by('total_nilai', 'DESC');
            $this->db->limit($limit);

            $result = $this->db->get()->result();

            // Filter hasil untuk memastikan area tidak kosong
            $filtered_result = array();
            foreach ($result as $row) {
                if (!empty(trim($row->area))) {
                    $filtered_result[] = $row;
                }
            }

            return $filtered_result;

        } catch (Exception $e) {
            // Log error dan return array kosong
            log_message('error', 'Error in get_top_payment_areas: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Check if payment exists for penggunaan
     */
    public function is_payment_exists($id_penggunaan)
    {
        $this->db->where('id_penggunaan', $id_penggunaan);
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Get pembayaran dengan pagination
     */
    public function get_paginated($limit, $offset, $search = null, $status = null)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, pelanggan.nama_pelanggan, pelanggan.no_pelanggan');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('pembayaran.no_kwitansi', $search);
            $this->db->or_like('pelanggan.nama_pelanggan', $search);
            $this->db->or_like('pelanggan.no_pelanggan', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('pembayaran.status_verifikasi', $status);
        }
        
        $this->db->order_by('pembayaran.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get()->result();
    }

    /**
     * Count total untuk pagination
     */
    public function count_total($search = null, $status = null)
    {
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('pelanggan', 'pelanggan.id = penggunaan_air.id_pelanggan');
        
        if ($search) {
            $this->db->group_start();
            $this->db->like('pembayaran.no_kwitansi', $search);
            $this->db->or_like('pelanggan.nama_pelanggan', $search);
            $this->db->or_like('pelanggan.no_pelanggan', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('pembayaran.status_verifikasi', $status);
        }
        
        return $this->db->count_all_results();
    }
    
    
    public function get_by_pelanggan($id_pelanggan, $limit = 10)
    {
        $this->db->select('pembayaran.*, penggunaan_air.bulan, penggunaan_air.tahun, penggunaan_air.total_tagihan, users.nama_lengkap as verifikator_nama');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->join('users', 'users.id = pembayaran.petugas_verifikasi', 'LEFT');
        $this->db->where('penggunaan_air.id_pelanggan', $id_pelanggan);
        $this->db->order_by('pembayaran.tanggal_bayar', 'DESC');
        $this->db->limit($limit);
        return $this->db->get()->result();
    }
    
    /**
     * Sum pembayaran by pelanggan
     */
    public function sum_by_pelanggan($id_pelanggan)
    {
        $this->db->select_sum('pembayaran.jumlah_bayar');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan');
        $this->db->where('penggunaan_air.id_pelanggan', $id_pelanggan);
        $this->db->where('pembayaran.status_verifikasi', 'verified');
        $result = $this->db->get()->row();
        return $result->jumlah_bayar ? $result->jumlah_bayar : 0;
    }

    
}