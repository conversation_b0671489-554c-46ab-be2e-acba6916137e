<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lapor<PERSON>nan PDAM - <?php echo $bulan_nama . ' ' . $tahun; ?></title>
    <style>
        @page {
            margin: 1cm;
            size: A4;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            background: white;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            text-transform: uppercase;
        }
        
        .header h2 {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .header p {
            font-size: 11px;
            margin-bottom: 3px;
        }
        
        .kop-surat {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-right: 20px;
        }
        
        .info-perusahaan {
            text-align: left;
        }
        
        .laporan-title {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #ddd;
        }
        
        .laporan-title h3 {
            font-size: 16px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .periode {
            font-size: 14px;
            margin-top: 5px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .stats-card {
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
        }
        
        .stats-card h4 {
            font-size: 12px;
            margin-bottom: 8px;
            text-transform: uppercase;
            color: #666;
        }
        
        .stats-card .value {
            font-size: 16px;
            font-weight: bold;
            color: #000;
        }
        
        .ringkasan-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .ringkasan-table th,
        .ringkasan-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        
        .ringkasan-table th {
            background: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        .ringkasan-table .label {
            font-weight: bold;
            background: #f8f9fa;
        }
        
        .ringkasan-table .nilai {
            text-align: right;
            font-weight: bold;
        }
        
        .status-section {
            margin: 20px 0;
        }
        
        .status-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .status-table th,
        .status-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        
        .status-table th {
            background: #f0f0f0;
            font-weight: bold;
        }
        
        .footer {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .ttd-section {
            text-align: center;
            width: 200px;
        }
        
        .ttd-section p {
            margin-bottom: 60px;
        }
        
        .ttd-section .nama {
            font-weight: bold;
            border-top: 1px solid #000;
            padding-top: 5px;
        }
        
        .tanggal-cetak {
            font-size: 10px;
            color: #666;
            text-align: right;
            margin-top: 20px;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button class="print-button no-print" onclick="window.print()">
        🖨️ Cetak Laporan
    </button>

    <!-- Header Kop Surat -->
    <div class="header">
        <div class="kop-surat">
            <div class="logo">💧</div>
            <div class="info-perusahaan">
                <h1><?php echo $nama_perusahaan ?: 'PDAM TIRTA SEJAHTERA'; ?></h1>
                <p><?php echo $alamat_perusahaan ?: 'Jl. Merdeka No. 123, Kota'; ?></p>
                <p>Telp: (021) 1234-5678 | Email: <EMAIL></p>
            </div>
        </div>
    </div>

    <!-- Judul Laporan -->
    <div class="laporan-title">
        <h3>Laporan Operasional Bulanan</h3>
        <div class="periode">Periode: <?php echo $bulan_nama . ' ' . $tahun; ?></div>
    </div>

    <!-- Statistik Utama -->
    <div class="stats-grid">
        <div class="stats-card">
            <h4>Total Pelanggan</h4>
            <div class="value"><?php echo number_format($total_pelanggan); ?></div>
            <small>(<?php echo number_format($pelanggan_aktif); ?> aktif)</small>
        </div>
        <div class="stats-card">
            <h4>Pencatatan Meter</h4>
            <div class="value"><?php echo number_format($total_pencatatan); ?></div>
            <small><?php echo number_format($total_pemakaian); ?> m³</small>
        </div>
        <div class="stats-card">
            <h4>Total Tagihan</h4>
            <div class="value"><?php echo format_rupiah($total_tagihan); ?></div>
            <small><?php echo number_format($total_pembayaran); ?> pembayaran</small>
        </div>
        <div class="stats-card">
            <h4>Total Terbayar</h4>
            <div class="value"><?php echo format_rupiah($total_terbayar); ?></div>
            <small><?php echo number_format($persentase_pembayaran, 1); ?>%</small>
        </div>
    </div>

    <!-- Ringkasan Operasional -->
    <h3 style="margin: 25px 0 10px 0; border-bottom: 2px solid #000; padding-bottom: 5px;">
        📊 RINGKASAN OPERASIONAL
    </h3>
    
    <table class="ringkasan-table">
        <tr>
            <td class="label">Total Pelanggan Terdaftar</td>
            <td class="nilai"><?php echo number_format($total_pelanggan); ?> pelanggan</td>
        </tr>
        <tr>
            <td class="label">Pelanggan Aktif</td>
            <td class="nilai"><?php echo number_format($pelanggan_aktif); ?> pelanggan</td>
        </tr>
        <tr>
            <td class="label">Pencatatan Meter Bulan Ini</td>
            <td class="nilai"><?php echo number_format($total_pencatatan); ?> pencatatan</td>
        </tr>
        <tr>
            <td class="label">Total Pemakaian Air</td>
            <td class="nilai"><?php echo number_format($total_pemakaian); ?> m³</td>
        </tr>
        <tr>
            <td class="label">Rata-rata Pemakaian per Pelanggan</td>
            <td class="nilai"><?php echo $total_pencatatan > 0 ? number_format($total_pemakaian / $total_pencatatan, 1) : 0; ?> m³</td>
        </tr>
        <tr style="background: #fff3cd;">
            <td class="label">Total Tagihan Dikeluarkan</td>
            <td class="nilai"><?php echo format_rupiah($total_tagihan); ?></td>
        </tr>
        <tr style="background: #d4edda;">
            <td class="label">Total Terbayar</td>
            <td class="nilai"><?php echo format_rupiah($total_terbayar); ?></td>
        </tr>
        <tr style="background: #f8d7da;">
            <td class="label">Sisa Piutang</td>
            <td class="nilai"><?php echo format_rupiah($total_tagihan - $total_terbayar); ?></td>
        </tr>
        <tr>
            <td class="label">Persentase Pembayaran</td>
            <td class="nilai"><?php echo number_format($persentase_pembayaran, 1); ?>%</td>
        </tr>
    </table>

    <!-- Status Pembayaran -->
    <div class="status-section">
        <h3 style="margin: 20px 0 10px 0; border-bottom: 2px solid #000; padding-bottom: 5px;">
            💳 STATUS PEMBAYARAN
        </h3>
        
        <table class="status-table">
            <thead>
                <tr>
                    <th>Status</th>
                    <th>Jumlah</th>
                    <th>Persentase</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Terverifikasi</td>
                    <td><?php echo number_format($status_pembayaran['verified']); ?></td>
                    <td><?php echo $total_pembayaran > 0 ? number_format(($status_pembayaran['verified'] / $total_pembayaran) * 100, 1) : 0; ?>%</td>
                </tr>
                <tr>
                    <td>Menunggu Verifikasi</td>
                    <td><?php echo number_format($status_pembayaran['pending']); ?></td>
                    <td><?php echo $total_pembayaran > 0 ? number_format(($status_pembayaran['pending'] / $total_pembayaran) * 100, 1) : 0; ?>%</td>
                </tr>
                <tr>
                    <td>Ditolak</td>
                    <td><?php echo number_format($status_pembayaran['rejected']); ?></td>
                    <td><?php echo $total_pembayaran > 0 ? number_format(($status_pembayaran['rejected'] / $total_pembayaran) * 100, 1) : 0; ?>%</td>
                </tr>
                <tr style="background: #f0f0f0; font-weight: bold;">
                    <td>Total</td>
                    <td><?php echo number_format($total_pembayaran); ?></td>
                    <td>100.0%</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Kesimpulan -->
    <div style="margin: 30px 0; padding: 15px; border: 1px solid #ddd; background: #f8f9fa;">
        <h4 style="margin-bottom: 10px;">📝 KESIMPULAN</h4>
        <p style="margin-bottom: 8px;">
            Pada periode <?php echo $bulan_nama . ' ' . $tahun; ?>, PDAM telah melayani <?php echo number_format($total_pelanggan); ?> pelanggan 
            dengan tingkat pencatatan meter sebesar <?php echo number_format($persentase_pencatatan, 1); ?>%.
        </p>
        <p style="margin-bottom: 8px;">
            Total tagihan yang dikeluarkan mencapai <?php echo format_rupiah($total_tagihan); ?> dengan tingkat pembayaran 
            <?php echo number_format($persentase_pembayaran, 1); ?>% atau senilai <?php echo format_rupiah($total_terbayar); ?>.
        </p>
        <p>
            Sisa piutang yang harus ditagih adalah <?php echo format_rupiah($total_tagihan - $total_terbayar); ?>.
        </p>
    </div>

    <!-- Footer & TTD -->
    <div class="footer">
        <div class="ttd-section">
            <p>Mengetahui,<br>Kepala Bagian Operasional</p>
            <div class="nama">(_________________)</div>
        </div>
        
        <div class="ttd-section">
            <p><?php echo format_tanggal(date('Y-m-d')); ?><br>Pimpinan PDAM</p>
            <div class="nama">(_________________)</div>
        </div>
    </div>

    <div class="tanggal-cetak">
        Dicetak pada: <?php echo $tanggal_cetak; ?>
    </div>

    <script>
        // Auto print dialog on load (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>