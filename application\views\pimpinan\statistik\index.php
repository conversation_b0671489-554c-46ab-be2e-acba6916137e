<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">📈 Statistik & Analisis</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i> <?php echo date('Y'); ?>
            </button>
            <button type="button" class="btn btn-sm btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> Print Statistik
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Overview Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_pelanggan); ?></div>
                        <div class="text-xs">
                            <span class="text-success"><?php echo number_format($pelanggan_aktif); ?> aktif</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pembayaran</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo number_format($total_pembayaran_tahun_ini); ?></div>
                        <div class="text-xs">Tahun <?php echo date('Y'); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Nilai</div>
                        <div class="h6 mb-0 font-weight-bold"><?php echo format_rupiah($total_nilai_pembayaran); ?></div>
                        <div class="text-xs">Tahun <?php echo date('Y'); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Rata-rata/Pelanggan</div>
                        <div class="h6 mb-0 font-weight-bold"><?php echo format_rupiah($rata_rata_pembayaran); ?></div>
                        <div class="text-xs">Per tahun</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calculator fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performers -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top 5 Pelanggan Terbaik</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($top_pelanggan)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Nama Pelanggan</th>
                                <th class="text-center">Pembayaran</th>
                                <th class="text-end">Total Nilai</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_pelanggan as $index => $pelanggan): ?>
                            <tr>
                                <td>
                                    <?php if($index == 0): ?>
                                        <span class="badge bg-warning text-dark">🥇</span>
                                    <?php elseif($index == 1): ?>
                                        <span class="badge bg-secondary">🥈</span>
                                    <?php elseif($index == 2): ?>
                                        <span class="badge bg-warning">🥉</span>
                                    <?php else: ?>
                                        <span class="badge bg-primary"><?php echo $index + 1; ?></span>
                                    <?php endif; ?>
                                </td>
                                <td><strong><?php echo $pelanggan->nama_pelanggan; ?></strong></td>
                                <td class="text-center">
                                    <span class="badge bg-success"><?php echo $pelanggan->total_bayar; ?></span>
                                </td>
                                <td class="text-end">
                                    <strong><?php echo format_rupiah($pelanggan->total_nilai); ?></strong>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Belum ada data pelanggan</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Analisis Pembayaran</h5>
            </div>
            <div class="card-body">
                <!-- Performance Indicators -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="border rounded p-3 text-center">
                            <h5 class="text-success mb-0">
                                <?php echo $pelanggan_aktif > 0 ? number_format(($total_pembayaran_tahun_ini / $pelanggan_aktif), 1) : 0; ?>
                            </h5>
                            <small class="text-muted">Avg Pembayaran/Pelanggan</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="border rounded p-3 text-center">
                            <h5 class="text-info mb-0">
                                <?php echo $total_pembayaran_tahun_ini > 0 ? format_rupiah($total_nilai_pembayaran / $total_pembayaran_tahun_ini) : 'Rp 0'; ?>
                            </h5>
                            <small class="text-muted">Avg Nilai/Transaksi</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <!-- Quick Stats -->
                <h6 class="text-muted mb-3">Ringkasan Cepat</h6>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>Tingkat Aktivitas:</span>
                        <strong class="text-success">
                            <?php echo $pelanggan_aktif > 0 ? number_format(($pelanggan_aktif / $total_pelanggan) * 100, 1) : 0; ?>%
                        </strong>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>Bulan Terbaik:</span>
                        <strong class="text-primary">
                            <?php 
                            $max_bulan = 0;
                            $max_nilai = 0;
                            foreach($grafik_bulanan as $bulan) {
                                if($bulan['nilai'] > $max_nilai) {
                                    $max_nilai = $bulan['nilai'];
                                    $max_bulan = $bulan['bulan'];
                                }
                            }
                            echo $max_bulan ?: 'Belum ada';
                            ?>
                        </strong>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>Status Sistem:</span>
                        <span class="badge bg-success">Aktif</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-analytics me-2"></i>Insight & Rekomendasi</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="border-end">
                            <h6 class="text-primary">💡 Performance</h6>
                            <?php if($total_pembayaran_tahun_ini > 50): ?>
                                <p class="text-success mb-0">✅ Tingkat pembayaran sangat baik</p>
                            <?php elseif($total_pembayaran_tahun_ini > 20): ?>
                                <p class="text-warning mb-0">⚠️ Tingkat pembayaran cukup baik</p>
                            <?php else: ?>
                                <p class="text-danger mb-0">⚠️ Perlu peningkatan aktivitas</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-end">
                            <h6 class="text-success">💰 Revenue</h6>
                            <p class="text-muted mb-0">
                                Total pendapatan: <strong><?php echo format_rupiah($total_nilai_pembayaran); ?></strong>
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-info">📊 Growth</h6>
                        <p class="text-muted mb-0">
                            Target tahun ini: <strong>Meningkatkan pembayaran 20%</strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
const monthlyData = <?php echo json_encode($grafik_bulanan); ?>;

const monthlyChart = new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: monthlyData.map(item => item.bulan),
        datasets: [{
            label: 'Jumlah Pembayaran',
            data: monthlyData.map(item => item.total),
            backgroundColor: 'rgba(102, 126, 234, 0.8)',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 1,
            yAxisID: 'y'
        }, {
            label: 'Nilai Pembayaran',
            data: monthlyData.map(item => item.nilai),
            type: 'line',
            borderColor: 'rgba(40, 167, 69, 1)',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Jumlah Pembayaran'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Nilai (Rupiah)'
                },
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    callback: function(value) {
                        return 'Rp ' + value.toLocaleString('id-ID');
                    }
                }
            }
        }
    }
});

// Payment Method Chart
const methodCtx = document.getElementById('paymentMethodChart').getContext('2d');
const methodData = <?php echo json_encode($metode_pembayaran); ?>;

if (methodData && methodData.length > 0) {
    const methodChart = new Chart(methodCtx, {
        type: 'doughnut',
        data: {
            labels: methodData.map(item => item.metode_bayar.charAt(0).toUpperCase() + item.metode_bayar.slice(1)),
            datasets: [{
                data: methodData.map(item => item.total),
                backgroundColor: [
                    'rgba(102, 126, 234, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}
</script>