<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Flash Message Helper
 * Helper untuk menampilkan flash message
 */

if (!function_exists('show_flash_message')) {
    /**
     * <PERSON><PERSON><PERSON>an flash message dari session
     * @return string HTML flash message
     */
    function show_flash_message()
    {
        $CI =& get_instance();
        $output = '';
        
        // Success message
        if ($CI->session->flashdata('success')) {
            $output .= '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            $output .= '<i class="fas fa-check-circle me-2"></i>';
            $output .= $CI->session->flashdata('success');
            $output .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $output .= '</div>';
        }
        
        // Error message
        if ($CI->session->flashdata('error')) {
            $output .= '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            $output .= '<i class="fas fa-exclamation-circle me-2"></i>';
            $output .= $CI->session->flashdata('error');
            $output .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $output .= '</div>';
        }
        
        // Warning message
        if ($CI->session->flashdata('warning')) {
            $output .= '<div class="alert alert-warning alert-dismissible fade show" role="alert">';
            $output .= '<i class="fas fa-exclamation-triangle me-2"></i>';
            $output .= $CI->session->flashdata('warning');
            $output .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $output .= '</div>';
        }
        
        // Info message
        if ($CI->session->flashdata('info')) {
            $output .= '<div class="alert alert-info alert-dismissible fade show" role="alert">';
            $output .= '<i class="fas fa-info-circle me-2"></i>';
            $output .= $CI->session->flashdata('info');
            $output .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $output .= '</div>';
        }
        
        return $output;
    }
}