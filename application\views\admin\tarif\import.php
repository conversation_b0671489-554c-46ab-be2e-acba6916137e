<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-upload me-2"></i>Import Data Tarif
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="<?php echo base_url('admin/tarif'); ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Upload File</h5>
            </div>
            <div class="card-body">
                <?php echo form_open_multipart('admin/tarif/import'); ?>
                
                <div class="mb-3">
                    <label for="import_file" class="form-label">Pilih File Excel/CSV</label>
                    <input type="file" class="form-control" id="import_file" name="import_file" 
                           accept=".csv,.xlsx,.xls" required>
                    <div class="form-text">Format yang didukung: CSV, Excel (.xlsx, .xls). Maksimal 2MB.</div>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload & Import
                    </button>
                </div>
                
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Format File</h6>
            </div>
            <div class="card-body">
                <p class="small">File harus memiliki kolom dengan urutan:</p>
                <ol class="small">
                    <li>Nama Tarif</li>
                    <li>Tarif per m³</li>
                    <li>Batas Minimum</li>
                    <li>Biaya Admin</li>
                </ol>
                
                <p class="small text-muted mt-3">
                    <strong>Contoh:</strong><br>
                    Tarif Rumah Tangga,2500,10,5000<br>
                    Tarif Niaga,3500,15,7500
                </p>
            </div>
        </div>
    </div>
</div>

<?php $this->load->view('templates/footer'); ?>