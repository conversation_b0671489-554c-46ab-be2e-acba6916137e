<?php
// Tambahkan ke application/helpers/date_helper.php atau buat file baru

if (!function_exists('nama_bulan')) {
    function nama_bulan($bulan) {
        $bulan_indo = [
            1 => 'Jan<PERSON>ri',
            2 => 'Februari', 
            3 => '<PERSON><PERSON>',
            4 => 'April',
            5 => '<PERSON>',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember'
        ];
        
        return isset($bulan_indo[(int)$bulan]) ? $bulan_indo[(int)$bulan] : '';
    }
}

if (!function_exists('format_tanggal')) {
    function format_tanggal($tanggal, $with_time = false) {
        if (empty($tanggal) || $tanggal == '0000-00-00' || $tanggal == '0000-00-00 00:00:00') {
            return '-';
        }
        
        $timestamp = strtotime($tanggal);
        $hari = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Sabtu'];
        $bulan = ['', '<PERSON><PERSON><PERSON>', 'Februari', '<PERSON>t', 'April', 'Mei', 'Juni', 
                  'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
        
        $format = date('j', $timestamp) . ' ' . $bulan[date('n', $timestamp)] . ' ' . date('Y', $timestamp);
        
        if ($with_time) {
            $format .= ' ' . date('H:i', $timestamp);
        }
        
        return $format;
    }
}

if (!function_exists('format_rupiah')) {
    function format_rupiah($angka) {
        return 'Rp ' . number_format($angka, 0, ',', '.');
    }
}

if (!function_exists('show_flash_message')) {
    function show_flash_message() {
        $CI =& get_instance();
        $flash = '';
        
        if ($CI->session->flashdata('success')) {
            $flash .= '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            $flash .= '<i class="fas fa-check-circle me-2"></i>' . $CI->session->flashdata('success');
            $flash .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $flash .= '</div>';
        }
        
        if ($CI->session->flashdata('error')) {
            $flash .= '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
            $flash .= '<i class="fas fa-exclamation-circle me-2"></i>' . $CI->session->flashdata('error');
            $flash .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $flash .= '</div>';
        }
        
        if ($CI->session->flashdata('warning')) {
            $flash .= '<div class="alert alert-warning alert-dismissible fade show" role="alert">';
            $flash .= '<i class="fas fa-exclamation-triangle me-2"></i>' . $CI->session->flashdata('warning');
            $flash .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $flash .= '</div>';
        }
        
        if ($CI->session->flashdata('info')) {
            $flash .= '<div class="alert alert-info alert-dismissible fade show" role="alert">';
            $flash .= '<i class="fas fa-info-circle me-2"></i>' . $CI->session->flashdata('info');
            $flash .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $flash .= '</div>';
        }
        
        return $flash;
    }
}