<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pelanggan_model extends CI_Model {

    protected $table = 'pelanggan';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all pelanggan
     */
    public function get_all($status = null)
    {
        if ($status) {
            $this->db->where('status_langganan', $status);
        }
        $this->db->order_by('nama_pelanggan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get pelanggan by ID
     */
    public function get_by_id($id)
    {
        return $this->db->get_where($this->table, array('id' => $id))->row();
    }

    /**
     * Get pelanggan by nomor pelanggan
     */
    public function get_by_no_pelanggan($no_pelanggan)
    {
        return $this->db->get_where($this->table, array('no_pelanggan' => $no_pelanggan))->row();
    }

    /**
     * Insert pelanggan baru
     */
    public function insert($data)
    {
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update pelanggan
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete pelanggan
     */
    public function delete($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete($this->table);
    }

    /**
     * Count all pelanggan
     */
    public function count_all()
    {
        return $this->db->count_all($this->table);
    }

    /**
     * Count pelanggan by status
     */
    public function count_by_status($status)
    {
        $this->db->where('status_langganan', $status);
        return $this->db->count_all_results($this->table);
    }

    /**
     * Count pelanggan baru by month
     */
    public function count_new_by_month($bulan, $tahun)
    {
        $this->db->where('MONTH(tanggal_pemasangan)', $bulan);
        $this->db->where('YEAR(tanggal_pemasangan)', $tahun);
        return $this->db->count_all_results($this->table);
    }

    /**
     * Search pelanggan
     */
    public function search($keyword, $limit = 10, $offset = 0)
    {
        $this->db->like('no_pelanggan', $keyword);
        $this->db->or_like('nama_pelanggan', $keyword);
        $this->db->or_like('alamat', $keyword);
        $this->db->order_by('nama_pelanggan', 'ASC');
        $this->db->limit($limit, $offset);
        return $this->db->get($this->table)->result();
    }

    /**
     * Check if no_pelanggan exists
     */
    public function is_no_pelanggan_exists($no_pelanggan, $exclude_id = null)
    {
        $this->db->where('no_pelanggan', $no_pelanggan);
        if ($exclude_id) {
            $this->db->where('id !=', $exclude_id);
        }
        return $this->db->get($this->table)->num_rows() > 0;
    }

    /**
     * Get pelanggan dengan pagination
     */
    public function get_paginated($limit, $offset, $search = null, $status = null)
    {
        if ($search) {
            $this->db->group_start();
            $this->db->like('no_pelanggan', $search);
            $this->db->or_like('nama_pelanggan', $search);
            $this->db->or_like('alamat', $search);
            $this->db->or_like('no_hp', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('status_langganan', $status);
        }
        
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get($this->table)->result();
    }

    /**
     * Count total untuk pagination
     */
    public function count_total($search = null, $status = null)
    {
        if ($search) {
            $this->db->group_start();
            $this->db->like('no_pelanggan', $search);
            $this->db->or_like('nama_pelanggan', $search);
            $this->db->or_like('alamat', $search);
            $this->db->or_like('no_hp', $search);
            $this->db->or_like('email', $search);
            $this->db->group_end();
        }
        
        if ($status) {
            $this->db->where('status_langganan', $status);
        }
        
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get all pelanggan for export
     */
    public function get_all_for_export()
    {
        $this->db->order_by('no_pelanggan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get pelanggan statistics
     */
    public function get_statistics()
    {
        $stats = array();
        
        // Total by status
        $stats['total_aktif'] = $this->count_by_status('aktif');
        $stats['total_nonaktif'] = $this->count_by_status('nonaktif');
        $stats['total_putus'] = $this->count_by_status('putus');
        
        // Total all
        $stats['total_all'] = $this->count_all();
        
        // Pelanggan baru bulan ini
        $this->db->where('MONTH(created_at)', date('m'));
        $this->db->where('YEAR(created_at)', date('Y'));
        $stats['baru_bulan_ini'] = $this->db->count_all_results($this->table);
        
        // Pelanggan dengan email
        $this->db->where('email !=', '');
        $this->db->where('email IS NOT NULL');
        $stats['memiliki_email'] = $this->db->count_all_results($this->table);
        
        // Pelanggan dengan HP
        $this->db->where('no_hp !=', '');
        $this->db->where('no_hp IS NOT NULL');
        $stats['memiliki_hp'] = $this->db->count_all_results($this->table);
        
        return $stats;
    }

    /**
     * Get recent pelanggan
     */
    public function get_recent($limit = 5)
    {
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        return $this->db->get($this->table)->result();
    }

    /**
     * Bulk update status
     */
    public function bulk_update_status($ids, $status)
    {
        $this->db->where_in('id', $ids);
        return $this->db->update($this->table, array('status_langganan' => $status));
    }

    /**
     * Bulk delete pelanggan
     */
    public function bulk_delete($ids)
    {
        $this->db->where_in('id', $ids);
        return $this->db->delete($this->table);
    }

    /**
     * Get pelanggan by status
     */
    public function get_by_status($status)
    {
        $this->db->where('status_langganan', $status);
        $this->db->order_by('nama_pelanggan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get pelanggan untuk dropdown/select
     */
    public function get_for_dropdown($status = 'aktif')
    {
        $this->db->select('id, no_pelanggan, nama_pelanggan');
        $this->db->where('status_langganan', $status);
        $this->db->order_by('nama_pelanggan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Advanced search with multiple criteria
     */
    public function advanced_search($criteria)
    {
        if (!empty($criteria['no_pelanggan'])) {
            $this->db->like('no_pelanggan', $criteria['no_pelanggan']);
        }
        
        if (!empty($criteria['nama_pelanggan'])) {
            $this->db->like('nama_pelanggan', $criteria['nama_pelanggan']);
        }
        
        if (!empty($criteria['alamat'])) {
            $this->db->like('alamat', $criteria['alamat']);
        }
        
        if (!empty($criteria['status_langganan'])) {
            $this->db->where('status_langganan', $criteria['status_langganan']);
        }
        
        if (!empty($criteria['tanggal_pemasangan_from'])) {
            $this->db->where('tanggal_pemasangan >=', $criteria['tanggal_pemasangan_from']);
        }
        
        if (!empty($criteria['tanggal_pemasangan_to'])) {
            $this->db->where('tanggal_pemasangan <=', $criteria['tanggal_pemasangan_to']);
        }
        
        $this->db->order_by('nama_pelanggan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get pelanggan by area/wilayah
     */
    public function get_by_area($area_keyword)
    {
        $this->db->like('alamat', $area_keyword);
        $this->db->order_by('nama_pelanggan', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get top areas by pelanggan count
     */
    public function get_top_areas($limit = 10)
    {
        $this->db->select('SUBSTRING_INDEX(alamat, ",", 1) as area, COUNT(*) as total_pelanggan');
        $this->db->where('status_langganan', 'aktif');
        $this->db->group_by('area');
        $this->db->order_by('total_pelanggan', 'DESC');
        $this->db->limit($limit);
        return $this->db->get($this->table)->result();
    }

    /**
     * Get pelanggan registration trend (monthly)
     */
    public function get_registration_trend($months = 12)
    {
        $data = array();
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i months"));
            $bulan = date('m', strtotime($date));
            $tahun = date('Y', strtotime($date));
            
            $this->db->where('MONTH(created_at)', $bulan);
            $this->db->where('YEAR(created_at)', $tahun);
            $count = $this->db->count_all_results($this->table);
            
            $data[] = array(
                'bulan' => nama_bulan($bulan) . ' ' . $tahun,
                'bulan_num' => $bulan,
                'tahun' => $tahun,
                'total' => $count
            );
        }
        
        return $data;
    }

    /**
     * Check data integrity
     */
    public function check_data_integrity()
    {
        $issues = array();
        
        // Cek no_pelanggan duplikat
        $this->db->select('no_pelanggan, COUNT(*) as count');
        $this->db->group_by('no_pelanggan');
        $this->db->having('count > 1');
        $duplicates = $this->db->get($this->table)->result();
        
        if (!empty($duplicates)) {
            $issues['duplicate_no_pelanggan'] = $duplicates;
        }
        
        // Cek email duplikat (jika ada)
        $this->db->select('email, COUNT(*) as count');
        $this->db->where('email !=', '');
        $this->db->where('email IS NOT NULL');
        $this->db->group_by('email');
        $this->db->having('count > 1');
        $email_duplicates = $this->db->get($this->table)->result();
        
        if (!empty($email_duplicates)) {
            $issues['duplicate_email'] = $email_duplicates;
        }
        
        // Cek data tidak lengkap
        $this->db->where('nama_pelanggan', '');
        $this->db->or_where('alamat', '');
        $incomplete = $this->db->count_all_results($this->table);
        
        if ($incomplete > 0) {
            $issues['incomplete_data'] = $incomplete;
        }
        
        return $issues;
    }

    /**
     * Auto-generate no_pelanggan yang tersedia
     */
    public function generate_available_no_pelanggan()
    {
        do {
            $no_pelanggan = 'PLG' . date('Y') . sprintf('%06d', rand(100000, 999999));
        } while ($this->is_no_pelanggan_exists($no_pelanggan));
        
        return $no_pelanggan;
    }

    /**
     * Get pelanggan dengan info tunggakan
     */
    public function get_with_tunggakan()
    {
        $this->db->select('pelanggan.*, 
                          COALESCE(SUM(penggunaan_air.total_tagihan), 0) as total_tagihan,
                          COALESCE(SUM(pembayaran.jumlah_bayar), 0) as total_terbayar,
                          (COALESCE(SUM(penggunaan_air.total_tagihan), 0) - COALESCE(SUM(pembayaran.jumlah_bayar), 0)) as tunggakan');
        $this->db->from($this->table);
        $this->db->join('penggunaan_air', 'penggunaan_air.id_pelanggan = pelanggan.id', 'LEFT');
        $this->db->join('pembayaran', 'pembayaran.id_penggunaan = penggunaan_air.id AND pembayaran.status_verifikasi = "verified"', 'LEFT');
        $this->db->group_by('pelanggan.id');
        $this->db->having('tunggakan > 0');
        $this->db->order_by('tunggakan', 'DESC');
        
        return $this->db->get()->result();
    }
    
    public function get_with_tagihan()
    {
        $this->db->select('p.*, COUNT(pa.id) as total_tagihan');
        $this->db->from('pelanggan p');
        $this->db->join('penggunaan_air pa', 'p.id = pa.id_pelanggan', 'inner');
        $this->db->where('p.status_langganan', 'aktif');
        $this->db->where('p.email IS NOT NULL');
        $this->db->where('p.email !=', '');
        $this->db->group_by('p.id');
        $this->db->order_by('p.nama_pelanggan', 'ASC');
        
        return $this->db->get()->result();
    }
    
    
}