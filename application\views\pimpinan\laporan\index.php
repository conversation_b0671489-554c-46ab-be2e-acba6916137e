<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-chart-bar me-2"></i><PERSON><PERSON><PERSON></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('pimpinan/laporan/export?bulan=' . (isset($bulan) ? $bulan : date('n')) . '&tahun=' . (isset($tahun) ? $tahun : date('Y'))); ?>"
               class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Export CSV
            </a>
            <a href="<?php echo base_url('pimpinan/laporan/print?bulan=' . (isset($bulan) ? $bulan : date('n')) . '&tahun=' . (isset($tahun) ? $tahun : date('Y'))); ?>"
               class="btn btn-sm btn-info" target="_blank">
                <i class="fas fa-print"></i> Print
            </a>
        </div>
        <div class="btn-group">
            <a href="<?php echo base_url('pimpinan/laporan/tahunan'); ?>" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-chart-line"></i> Laporan Tahunan
            </a>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Laporan</h6>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Bulan</label>
                <select class="form-select" name="bulan">
                    <?php for ($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo ($bulan == $i) ? 'selected' : ''; ?>>
                            <?php echo nama_bulan($i); ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Tahun</label>
                <select class="form-select" name="tahun">
                    <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo ($tahun == $y) ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tampilkan Laporan
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?php echo base_url('dashboard'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Header Laporan -->
<div class="text-center mb-4">
    <h3>LAPORAN BULANAN PDAM TIRTA SEJAHTERA</h3>
    <h5><?php echo strtoupper(nama_bulan($bulan)) . ' ' . $tahun; ?></h5>
    <hr>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4><?php echo number_format($total_pelanggan); ?></h4>
                <small>Total Pelanggan</small>
                <div class="mt-1">
                    <small><?php echo number_format($pelanggan_aktif); ?> aktif</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body text-center">
                <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                <h4><?php echo number_format($total_pencatatan); ?></h4>
                <small>Pencatatan Meter</small>
                <div class="mt-1">
                    <small><?php echo number_format($total_pemakaian); ?> m³</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4><?php echo format_rupiah($total_tagihan); ?></h4>
                <small>Total Tagihan</small>
                <div class="mt-1">
                    <small><?php echo number_format($total_pembayaran); ?> pembayaran</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h4><?php echo format_rupiah($total_terbayar); ?></h4>
                <small>Terbayar</small>
                <div class="mt-1">
                    <?php 
                    $persentase = $total_tagihan > 0 ? ($total_terbayar / $total_tagihan) * 100 : 0;
                    ?>
                    <small><?php echo number_format($persentase, 1); ?>%</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detail Laporan -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Ringkasan Operasional</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <td class="fw-bold">Total Pelanggan Terdaftar</td>
                        <td class="text-end"><?php echo number_format($total_pelanggan); ?> pelanggan</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Pelanggan Aktif</td>
                        <td class="text-end"><?php echo number_format($pelanggan_aktif); ?> pelanggan</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Pencatatan Meter Bulan Ini</td>
                        <td class="text-end"><?php echo number_format($total_pencatatan); ?> pencatatan</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Total Pemakaian Air</td>
                        <td class="text-end"><?php echo number_format($total_pemakaian); ?> m³</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Rata-rata Pemakaian per Pelanggan</td>
                        <td class="text-end"><?php echo $total_pencatatan > 0 ? number_format($total_pemakaian / $total_pencatatan, 1) : 0; ?> m³</td>
                    </tr>
                    <tr class="table-warning">
                        <td class="fw-bold">Total Tagihan Dikeluarkan</td>
                        <td class="text-end fw-bold"><?php echo format_rupiah($total_tagihan); ?></td>
                    </tr>
                    <tr class="table-success">
                        <td class="fw-bold">Total Terbayar</td>
                        <td class="text-end fw-bold"><?php echo format_rupiah($total_terbayar); ?></td>
                    </tr>
                    <tr class="table-info">
                        <td class="fw-bold">Sisa Piutang</td>
                        <td class="text-end fw-bold"><?php echo format_rupiah($total_tagihan - $total_terbayar); ?></td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Persentase Pembayaran</td>
                        <td class="text-end"><?php echo number_format($persentase, 1); ?>%</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Status Pembayaran -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Status Pembayaran</h6>
            </div>
            <div class="card-body">
                <?php if (isset($status_pembayaran) && !empty($status_pembayaran)): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-success w-50">Verified</span>
                        <strong><?php echo number_format($status_pembayaran['verified']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-warning w-50">Pending</span>
                        <strong><?php echo number_format($status_pembayaran['pending']); ?></strong>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-danger w-50">Rejected</span>
                        <strong><?php echo number_format($status_pembayaran['rejected']); ?></strong>
                    </div>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="fas fa-info-circle fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">Belum ada data pembayaran untuk bulan ini</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Progress -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Progress</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Pencatatan Meter</small>
                    <div class="progress" style="height: 10px;">
                        <?php $progress_meter = $pelanggan_aktif > 0 ? ($total_pencatatan / $pelanggan_aktif) * 100 : 0; ?>
                        <div class="progress-bar bg-primary" style="width: <?php echo min($progress_meter, 100); ?>%"></div>
                    </div>
                    <small><?php echo number_format($progress_meter, 1); ?>%</small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Pembayaran</small>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" style="width: <?php echo min($persentase, 100); ?>%"></div>
                    </div>
                    <small><?php echo number_format($persentase, 1); ?>%</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer Laporan -->
<div class="row mt-4">
    <div class="col-12">
        <div class="text-center">
            <hr>
            <small class="text-muted">
                Laporan ini dibuat pada: <?php echo format_tanggal(date('Y-m-d'), true); ?><br>
                PDAM Tirta Sejahtera - Sistem Informasi Monitoring Pembayaran Air
            </small>
        </div>
    </div>
</div>

<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    transition: transform 0.2s;
}
.stats-card:hover {
    transform: translateY(-2px);
}

@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-header, .btn {
        display: none !important;
    }
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 1rem !important;
    }
    body {
        font-size: 12px !important;
    }
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
}
</style>