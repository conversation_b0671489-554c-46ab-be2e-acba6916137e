<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-chart-bar me-2"></i><PERSON>por<PERSON> B<PERSON>nan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">📋 Laporan Bulanan</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <form method="GET" class="d-flex gap-2">
                        <select name="bulan" class="form-select">
                            <?php for($i = 1; $i <= 12; $i++): ?>
                                <option value="<?php echo $i; ?>" <?php echo (isset($bulan) && $bulan == $i) ? 'selected' : ''; ?>>
                                    <?php echo nama_bulan($i); ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                        <select name="tahun" class="form-select">
                            <?php for($i = date('Y'); $i >= date('Y') - 3; $i--): ?>
                                <option value="<?php echo $i; ?>" <?php echo (isset($tahun) && $tahun == $i) ? 'selected' : ''; ?>>
                                    <?php echo $i; ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Tampilkan Laporan
                        </button>
                    </form>
                </div>
                <div class="btn-group">
                    <a href="<?php echo base_url('pimpinan/laporan/print?bulan=' . (isset($bulan) ? $bulan : date('n')) . '&tahun=' . (isset($tahun) ? $tahun : date('Y'))); ?>" 
                       class="btn btn-success" target="_blank">
                        <i class="fas fa-print"></i> Print
                    </a>
                    <a href="<?php echo base_url('pimpinan/laporan/tahunan'); ?>" class="btn btn-info">
                        <i class="fas fa-chart-line"></i> Laporan Tahunan
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Bulan</label>
                <select class="form-select" name="bulan">
                    <?php for ($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo sprintf('%02d', $i); ?>" 
                                <?php echo ($bulan == sprintf('%02d', $i)) ? 'selected' : ''; ?>>
                            <?php echo nama_bulan($i); ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Tahun</label>
                <select class="form-select" name="tahun">
                    <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                        <option value="<?php echo $y; ?>" <?php echo ($tahun == $y) ? 'selected' : ''; ?>>
                            <?php echo $y; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tampilkan Laporan
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Header Laporan -->
<div class="text-center mb-4">
    <h3>LAPORAN BULANAN PDAM TIRTA SEJAHTERA</h3>
    <h5><?php echo strtoupper(nama_bulan($bulan)) . ' ' . $tahun; ?></h5>
    <hr>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4><?php echo number_format($total_pelanggan); ?></h4>
                <small>Total Pelanggan</small>
                <div class="mt-1">
                    <small><?php echo number_format($pelanggan_aktif); ?> aktif</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body text-center">
                <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                <h4><?php echo number_format($total_pencatatan); ?></h4>
                <small>Pencatatan Meter</small>
                <div class="mt-1">
                    <small><?php echo number_format($total_pemakaian); ?> m³</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4><?php echo format_rupiah($total_tagihan); ?></h4>
                <small>Total Tagihan</small>
                <div class="mt-1">
                    <small><?php echo number_format($total_pembayaran); ?> pembayaran</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h4><?php echo format_rupiah($total_terbayar); ?></h4>
                <small>Terbayar</small>
                <div class="mt-1">
                    <?php 
                    $persentase = $total_tagihan > 0 ? ($total_terbayar / $total_tagihan) * 100 : 0;
                    ?>
                    <small><?php echo number_format($persentase, 1); ?>%</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detail Laporan -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Ringkasan Operasional</h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <td class="fw-bold">Total Pelanggan Terdaftar</td>
                        <td class="text-end"><?php echo number_format($total_pelanggan); ?> pelanggan</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Pelanggan Aktif</td>
                        <td class="text-end"><?php echo number_format($pelanggan_aktif); ?> pelanggan</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Pencatatan Meter Bulan Ini</td>
                        <td class="text-end"><?php echo number_format($total_pencatatan); ?> pencatatan</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Total Pemakaian Air</td>
                        <td class="text-end"><?php echo number_format($total_pemakaian); ?> m³</td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Rata-rata Pemakaian per Pelanggan</td>
                        <td class="text-end"><?php echo $total_pencatatan > 0 ? number_format($total_pemakaian / $total_pencatatan, 1) : 0; ?> m³</td>
                    </tr>
                    <tr class="table-warning">
                        <td class="fw-bold">Total Tagihan Dikeluarkan</td>
                        <td class="text-end fw-bold"><?php echo format_rupiah($total_tagihan); ?></td>
                    </tr>
                    <tr class="table-success">
                        <td class="fw-bold">Total Terbayar</td>
                        <td class="text-end fw-bold"><?php echo format_rupiah($total_terbayar); ?></td>
                    </tr>
                    <tr class="table-info">
                        <td class="fw-bold">Sisa Piutang</td>
                        <td class="text-end fw-bold"><?php echo format_rupiah($total_tagihan - $total_terbayar); ?></td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Persentase Pembayaran</td>
                        <td class="text-end"><?php echo number_format($persentase, 1); ?>%</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Status Pembayaran -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Status Pembayaran</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($status_pembayaran)): ?>
                    <?php foreach ($status_pembayaran as $status): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-<?php 
                            echo $status->status_verifikasi == 'verified' ? 'success' : 
                                ($status->status_verifikasi == 'pending' ? 'warning' : 'danger'); 
                        ?> w-50">
                            <?php echo ucfirst($status->status_verifikasi); ?>
                        </span>
                        <strong><?php echo number_format($status->jumlah); ?></strong>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="text-muted text-center">Belum ada pembayaran</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Progress -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Progress</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted">Pencatatan Meter</small>
                    <div class="progress" style="height: 10px;">
                        <?php $progress_meter = $pelanggan_aktif > 0 ? ($total_pencatatan / $pelanggan_aktif) * 100 : 0; ?>
                        <div class="progress-bar bg-primary" style="width: <?php echo min($progress_meter, 100); ?>%"></div>
                    </div>
                    <small><?php echo number_format($progress_meter, 1); ?>%</small>
                </div>
                
                <div class="mb-3">
                    <small class="text-muted">Pembayaran</small>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" style="width: <?php echo min($persentase, 100); ?>%"></div>
                    </div>
                    <small><?php echo number_format($persentase, 1); ?>%</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer Laporan -->
<div class="row mt-4">
    <div class="col-12">
        <div class="text-center">
            <hr>
            <small class="text-muted">
                Laporan ini dibuat pada: <?php echo format_tanggal(date('Y-m-d'), true); ?><br>
                PDAM Tirta Sejahtera - Sistem Informasi Monitoring Pembayaran Air
            </small>
        </div>
    </div>
</div>

<style>
@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-header {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    body {
        font-size: 12px !important;
    }
}
</style>

<?php $this->load->view('templates/footer'); ?>