<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-envelope me-2"></i>Pengaturan Email
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pengaturan'); ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <button type="button" class="btn btn-outline-info" onclick="sendTestEmail()">
                <i class="fas fa-paper-plane"></i> Test Email
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-lg-8">
        <!-- SMTP Configuration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-server me-2"></i>Konfigurasi SMTP
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open('admin/pengaturan/email', 'id="emailForm"'); ?>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="smtp_host" class="form-label">SMTP Host <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                               value="<?php echo $email_settings['smtp_host'] ?? 'mail.hostinger.com'; ?>" 
                               placeholder="mail.example.com" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="smtp_port" class="form-label">SMTP Port <span class="text-danger">*</span></label>
                        <select class="form-select" id="smtp_port" name="smtp_port" required>
                            <option value="587" <?php echo (($email_settings['smtp_port'] ?? '587') == '587') ? 'selected' : ''; ?>>587 (TLS)</option>
                            <option value="465" <?php echo (($email_settings['smtp_port'] ?? '') == '465') ? 'selected' : ''; ?>>465 (SSL)</option>
                            <option value="25" <?php echo (($email_settings['smtp_port'] ?? '') == '25') ? 'selected' : ''; ?>>25 (Non-secure)</option>
                        </select>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="smtp_user" class="form-label">Username Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="smtp_user" name="smtp_user" 
                               value="<?php echo $email_settings['smtp_user'] ?? '<EMAIL>'; ?>" 
                               placeholder="<EMAIL>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="smtp_pass" class="form-label">Password Email <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="smtp_pass" name="smtp_pass" 
                                   value="<?php echo $email_settings['smtp_pass'] ?? 'Apps-Marketing123'; ?>" 
                                   placeholder="••••••••" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="smtp_crypto" class="form-label">Enkripsi</label>
                    <select class="form-select" id="smtp_crypto" name="smtp_crypto">
                        <option value="tls" <?php echo (($email_settings['smtp_crypto'] ?? 'tls') == 'tls') ? 'selected' : ''; ?>>TLS</option>
                        <option value="ssl" <?php echo (($email_settings['smtp_crypto'] ?? '') == 'ssl') ? 'selected' : ''; ?>>SSL</option>
                        <option value="" <?php echo (($email_settings['smtp_crypto'] ?? '') == '') ? 'selected' : ''; ?>>Tidak ada</option>
                    </select>
                </div>
                
                <hr>
                
                <!-- Email Identity -->
                <h6 class="text-muted mb-3">Identitas Pengirim</h6>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email_from_name" class="form-label">Nama Pengirim</label>
                        <input type="text" class="form-control" id="email_from_name" name="email_from_name" 
                               value="<?php echo $email_settings['email_from_name'] ?? 'PDAM System'; ?>" 
                               placeholder="PDAM Tirta Sejahtera">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="email_from_address" class="form-label">Email Pengirim</label>
                        <input type="email" class="form-control" id="email_from_address" name="email_from_address" 
                               value="<?php echo $email_settings['email_from_address'] ?? '<EMAIL>'; ?>" 
                               placeholder="<EMAIL>">
                    </div>
                </div>
                
                <hr>
                
                <!-- Email Templates -->
                <h6 class="text-muted mb-3">Template Email</h6>
                
                <div class="mb-3">
                    <label for="email_template_header" class="form-label">Header Template</label>
                    <textarea class="form-control" id="email_template_header" name="email_template_header" rows="3" 
                              placeholder="HTML header untuk email..."><?php echo $email_settings['email_template_header'] ?? '<div style="background: #667eea; color: white; padding: 20px; text-align: center;"><h2>PDAM Tirta Sejahtera</h2></div>'; ?></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="email_template_footer" class="form-label">Footer Template</label>
                    <textarea class="form-control" id="email_template_footer" name="email_template_footer" rows="3" 
                              placeholder="HTML footer untuk email..."><?php echo $email_settings['email_template_footer'] ?? '<div style="background: #f8f9fa; padding: 15px; text-align: center; font-size: 12px; color: #666;">© 2025 PDAM Tirta Sejahtera. Email otomatis, jangan dibalas.</div>'; ?></textarea>
                </div>
                
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetToDefault()">
                        <i class="fas fa-undo"></i> Reset Default
                    </button>
                    <div>
                        <button type="button" class="btn btn-outline-info me-2" onclick="previewTemplate()">
                            <i class="fas fa-eye"></i> Preview Template
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Pengaturan
                        </button>
                    </div>
                </div>
                
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Connection Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-wifi me-2"></i>Status Koneksi
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>SMTP Server:</span>
                    <span class="badge bg-success" id="smtp-status">
                        <i class="fas fa-check"></i> Connected
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Last Test:</span>
                    <small class="text-muted" id="last-test">Belum pernah</small>
                </div>
                
                <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="checkConnection()">
                    <i class="fas fa-sync"></i> Check Connection
                </button>
            </div>
        </div>
        
        <!-- Quick Test -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-paper-plane me-2"></i>Test Email
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="test_email" class="form-label">Email Tujuan</label>
                    <input type="email" class="form-control" id="test_email" 
                           placeholder="<EMAIL>" value="<?php echo $this->session->userdata('email') ?? ''; ?>">
                </div>
                <button type="button" class="btn btn-success btn-sm w-100" onclick="sendTestEmail()">
                    <i class="fas fa-envelope"></i> Kirim Test Email
                </button>
            </div>
        </div>
        
        <!-- Email Statistics -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Statistik Email
                </h6>
            </div>
            <div class="card-body">
                <?php 
                // Get real email statistics from database
                $this->db->select('COUNT(*) as total_sent');
                $this->db->where('status', 'sent');
                $email_sent = $this->db->get('email_logs')->row()->total_sent ?? 0;
                
                $this->db->select('COUNT(*) as total_failed');
                $this->db->where('status', 'failed');
                $email_failed = $this->db->get('email_logs')->row()->total_failed ?? 0;
                ?>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-success"><?php echo $email_sent; ?></h4>
                        <small class="text-muted">Email Terkirim</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger"><?php echo $email_failed; ?></h4>
                        <small class="text-muted">Email Gagal</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <small class="text-muted">Total keseluruhan</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Preview Template Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
// Form submission
document.getElementById('emailForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
    submitBtn.disabled = true;
    
    // Simulate save
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        showAlert('success', 'Pengaturan email berhasil disimpan!');
        
        // Submit the actual form
        this.submit();
    }, 1500);
});

// Toggle password visibility
function togglePassword() {
    const passwordField = document.getElementById('smtp_pass');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordField.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Check SMTP connection
function checkConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    button.disabled = true;
    
    // Simulate connection check
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Update status
        document.getElementById('smtp-status').innerHTML = '<i class="fas fa-check"></i> Connected';
        document.getElementById('smtp-status').className = 'badge bg-success';
        document.getElementById('last-test').textContent = new Date().toLocaleString();
        
        showAlert('success', 'Koneksi SMTP berhasil!');
    }, 2000);
}

// Send test email - FIXED VERSION
function sendTestEmail() {
    const email = document.getElementById('test_email').value;
    
    if (!email) {
        showAlert('warning', 'Masukkan email tujuan terlebih dahulu');
        return;
    }
    
    if (!validateEmail(email)) {
        showAlert('error', 'Format email tidak valid');
        return;
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    button.disabled = true;
    
    // Create form data
    const formData = new FormData();
    formData.append('test_email', email);
    
    // AJAX call to test email endpoint
    fetch('<?php echo base_url("admin/pengaturan/test_email"); ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(text => {
        try {
            const data = JSON.parse(text);
            button.innerHTML = originalText;
            button.disabled = false;
            
            if (data.success) {
                showAlert('success', data.message);
                // Update last test time
                document.getElementById('last-test').textContent = new Date().toLocaleString('id-ID');
            } else {
                showAlert('error', data.message);
                // Update status to failed
                document.getElementById('smtp-status').innerHTML = '<i class="fas fa-times"></i> Failed';
                document.getElementById('smtp-status').className = 'badge bg-danger';
            }
        } catch (e) {
            button.innerHTML = originalText;
            button.disabled = false;
            showAlert('error', 'Response tidak valid dari server: ' + text);
        }
    })
    .catch(error => {
        button.innerHTML = originalText;
        button.disabled = false;
        showAlert('error', 'Terjadi kesalahan jaringan: ' + error.message);
        console.error('Error:', error);
    });
}

// Preview email template
function previewTemplate() {
    const header = document.getElementById('email_template_header').value;
    const footer = document.getElementById('email_template_footer').value;
    const fromName = document.getElementById('email_from_name').value;
    
    const sampleContent = `
        <h3>Pengingat Pembayaran Air</h3>
        <p>Yth. Budi Santoso,</p>
        <p>Tagihan air Anda untuk periode Januari 2025 sebesar <strong>Rp 87.500</strong> akan jatuh tempo pada <strong>31 Januari 2025</strong>.</p>
        <p>Mohon segera lakukan pembayaran melalui:</p>
        <ul>
            <li>Transfer Bank BCA: 123-456-789</li>
            <li>Kantor PDAM terdekat</li>
            <li>Mobile Banking/Internet Banking</li>
        </ul>
        <p>Terima kasih atas perhatian Anda.</p>
        <p>Hormat kami,<br><strong>${fromName}</strong></p>
    `;
    
    const fullTemplate = `
        <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
            ${header}
            <div style="padding: 20px;">
                ${sampleContent}
            </div>
            ${footer}
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = fullTemplate;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Reset to default
function resetToDefault() {
    if (confirm('Reset semua pengaturan ke nilai default?')) {
        document.getElementById('smtp_host').value = 'mail.hostinger.com';    // ← UBAH INI
        document.getElementById('smtp_port').value = '587';
        document.getElementById('smtp_user').value = '<EMAIL>';
        document.getElementById('smtp_pass').value = 'Apps-Marketing123';
        document.getElementById('smtp_crypto').value = 'tls';
        document.getElementById('email_from_name').value = 'PDAM System';
        document.getElementById('email_from_address').value = '<EMAIL>';
        
        showAlert('info', 'Pengaturan telah direset ke nilai default');
    }
}

// Utility functions
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert at top of page
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-save draft
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(() => {
        const formData = new FormData(document.getElementById('emailForm'));
        localStorage.setItem('email_settings_draft', JSON.stringify(Object.fromEntries(formData)));
    }, 30000); // Save every 30 seconds
}

// Load draft on page load
window.addEventListener('load', () => {
    const draft = localStorage.getItem('email_settings_draft');
    if (draft) {
        // Option to load draft
        if (confirm('Ditemukan draft pengaturan email yang belum disimpan. Muat draft?')) {
            const draftData = JSON.parse(draft);
            Object.keys(draftData).forEach(key => {
                const element = document.querySelector(`[name="${key}"]`);
                if (element) {
                    element.value = draftData[key];
                }
            });
        }
    }
    
    // Start auto-save
    startAutoSave();
});

// Clear draft when form is successfully submitted
document.getElementById('emailForm').addEventListener('submit', () => {
    localStorage.removeItem('email_settings_draft');
    clearInterval(autoSaveInterval);
});

// Port change handler
document.getElementById('smtp_port').addEventListener('change', function() {
    const port = this.value;
    const cryptoSelect = document.getElementById('smtp_crypto');
    
    if (port === '465') {
        cryptoSelect.value = 'ssl';
    } else if (port === '587') {
        cryptoSelect.value = 'tls';
    } else {
        cryptoSelect.value = '';
    }
});
</script>

<?php $this->load->view('templates/footer'); ?>