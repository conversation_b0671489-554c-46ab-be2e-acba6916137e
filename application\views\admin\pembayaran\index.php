<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-money-bill-wave me-2"></i>Data Pembayaran</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pembayaran/add'); ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Input Pembayaran
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Verifikasi</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_pending); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Terverifikasi</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo number_format($total_verified); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Terbayar</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($total_terbayar); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Periode</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo nama_bulan($bulan_filter) . ' ' . $tahun_filter; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filter & Search -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter & Pencarian</h5>
    </div>
    <div class="card-body">
        <?php echo form_open('', ['method' => 'GET', 'class' => 'row g-3']); ?>
        
        <div class="col-md-2">
            <label for="bulan" class="form-label">Bulan</label>
            <select class="form-select" id="bulan" name="bulan">
                <?php for ($i = 1; $i <= 12; $i++): ?>
                    <option value="<?php echo str_pad($i, 2, '0', STR_PAD_LEFT); ?>" 
                            <?php echo ($bulan_filter == str_pad($i, 2, '0', STR_PAD_LEFT)) ? 'selected' : ''; ?>>
                        <?php echo nama_bulan($i); ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-2">
            <label for="tahun" class="form-label">Tahun</label>
            <select class="form-select" id="tahun" name="tahun">
                <?php for ($i = date('Y') - 2; $i <= date('Y') + 1; $i++): ?>
                    <option value="<?php echo $i; ?>" <?php echo ($tahun_filter == $i) ? 'selected' : ''; ?>>
                        <?php echo $i; ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="col-md-2">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="all" <?php echo ($status_filter == 'all') ? 'selected' : ''; ?>>Semua Status</option>
                <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>Pending</option>
                <option value="verified" <?php echo ($status_filter == 'verified') ? 'selected' : ''; ?>>Verified</option>
                <option value="rejected" <?php echo ($status_filter == 'rejected') ? 'selected' : ''; ?>>Rejected</option>
            </select>
        </div>
        
        <div class="col-md-4">
            <label for="search" class="form-label">Pencarian</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="<?php echo $search; ?>" placeholder="Nama pelanggan, no pelanggan, atau no kwitansi">
        </div>
        
        <div class="col-md-2">
            <label class="form-label">&nbsp;</label>
            <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
            </div>
        </div>
        
        <?php echo form_close(); ?>
    </div>
</div>

<!-- Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Daftar Pembayaran 
            <span class="badge bg-secondary"><?php echo count($pembayaran); ?> data</span>
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (!empty($pembayaran)): ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th width="50">No</th>
                        <th>Pelanggan</th>
                        <th>Periode</th>
                        <th>No. Kwitansi</th>
                        <th>Jumlah Bayar</th>
                        <th>Metode</th>
                        <th>Status</th>
                        <th>Tanggal</th>
                        <th width="150">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($pembayaran as $index => $p): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td>
                            <strong><?php echo $p->nama_pelanggan; ?></strong><br>
                            <small class="text-muted"><?php echo $p->no_pelanggan; ?></small>
                        </td>
                        <td><?php echo nama_bulan($p->bulan) . ' ' . $p->tahun; ?></td>
                        <td>
                            <span class="text-primary fw-bold"><?php echo $p->no_kwitansi; ?></span>
                        </td>
                        <td>
                            <strong><?php echo format_rupiah($p->jumlah_bayar); ?></strong><br>
                            <small class="text-muted">Tagihan: <?php echo format_rupiah($p->total_tagihan); ?></small>
                        </td>
                        <td>
                            <?php
                            $metode_class = '';
                            switch ($p->metode_bayar) {
                                case 'tunai': $metode_class = 'success'; break;
                                case 'transfer': $metode_class = 'primary'; break;
                                case 'qris': $metode_class = 'info'; break;
                                default: $metode_class = 'secondary'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $metode_class; ?>"><?php echo ucfirst($p->metode_bayar); ?></span>
                        </td>
                        <td>
                            <?php
                            $status_class = '';
                            switch ($p->status_verifikasi) {
                                case 'pending': $status_class = 'warning'; break;
                                case 'verified': $status_class = 'success'; break;
                                case 'rejected': $status_class = 'danger'; break;
                            }
                            ?>
                            <span class="badge bg-<?php echo $status_class; ?>"><?php echo ucfirst($p->status_verifikasi); ?></span>
                        </td>
                        <td><?php echo format_tanggal($p->tanggal_bayar); ?></td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo base_url('admin/pembayaran/detail/' . $p->id); ?>" 
                                   class="btn btn-sm btn-info" title="Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo base_url('admin/pembayaran/edit/' . $p->id); ?>" 
                                   class="btn btn-sm btn-warning" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo base_url('admin/pembayaran/delete/' . $p->id); ?>" 
                                   class="btn btn-sm btn-danger" title="Hapus"
                                   onclick="return confirm('Yakin ingin menghapus pembayaran ini?')">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Tidak ada data pembayaran</h5>
            <p class="text-muted">Belum ada pembayaran untuk periode dan filter yang dipilih</p>
            <a href="<?php echo base_url('admin/pembayaran/add'); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Input Pembayaran Pertama
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php $this->load->view('templates/footer'); ?>
