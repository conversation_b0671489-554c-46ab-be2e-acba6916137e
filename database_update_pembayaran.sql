-- Update tabel pembayaran untuk menambahkan kolom yang hilang
-- Jalankan script ini di phpMyAdmin atau MySQL client

-- <PERSON>bah<PERSON> kolom keterangan jika belum ada
ALTER TABLE `pembayaran` 
ADD COLUMN `keterangan` TEXT NULL COMMENT 'Keterangan tambahan pembayaran' AFTER `tanggal_bayar`;

-- <PERSON><PERSON><PERSON> kolom petugas_input jika belum ada
ALTER TABLE `pembayaran` 
ADD COLUMN `petugas_input` INT(11) NULL COMMENT 'ID user yang input pembayaran' AFTER `status_verifikasi`;

-- <PERSON><PERSON><PERSON> kolom created_at jika belum ada
ALTER TABLE `pembayaran` 
ADD COLUMN `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Tanggal dibuat' AFTER `petugas_input`;

-- <PERSON><PERSON><PERSON> kolom updated_at jika belum ada
ALTER TABLE `pembayaran` 
ADD COLUMN `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Tanggal diupdate' AFTER `created_at`;

-- Tambahkan foreign key untuk petugas_input jika tabel users ada
-- ALTER TABLE `pembayaran` 
-- ADD CONSTRAINT `fk_pembayaran_petugas_input` 
-- FOREIGN KEY (`petugas_input`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Lihat struktur tabel setelah update
DESCRIBE `pembayaran`;
