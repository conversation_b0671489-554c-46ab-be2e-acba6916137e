<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-receipt me-2"></i>Detail Pembayaran</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/pembayaran'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo base_url('admin/pembayaran/edit/' . $pembayaran->id); ?>" class="btn btn-sm btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <button onclick="window.print()" class="btn btn-sm btn-info">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-md-8">
        <!-- Detail Pembayaran -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Detail Pembayaran</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted">No. Kwitansi:</td>
                                <td><strong class="text-primary"><?php echo $pembayaran->no_kwitansi; ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Pelanggan:</td>
                                <td>
                                    <strong><?php echo $pembayaran->nama_pelanggan; ?></strong><br>
                                    <small class="text-muted"><?php echo $pembayaran->no_pelanggan; ?></small>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Alamat:</td>
                                <td><?php echo $pembayaran->alamat; ?></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Periode:</td>
                                <td><strong><?php echo nama_bulan($pembayaran->bulan) . ' ' . $pembayaran->tahun; ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Pemakaian:</td>
                                <td><span class="badge bg-primary"><?php echo $pembayaran->pemakaian; ?> m³</span></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted">Total Tagihan:</td>
                                <td><strong class="text-success"><?php echo format_rupiah($pembayaran->total_tagihan); ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Jumlah Bayar:</td>
                                <td><strong class="text-primary"><?php echo format_rupiah($pembayaran->jumlah_bayar); ?></strong></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Selisih:</td>
                                <td>
                                    <?php 
                                    $selisih = $pembayaran->jumlah_bayar - $pembayaran->total_tagihan;
                                    if ($selisih > 0) {
                                        echo '<span class="text-success">+' . format_rupiah($selisih) . ' (Lebih)</span>';
                                    } elseif ($selisih < 0) {
                                        echo '<span class="text-danger">' . format_rupiah($selisih) . ' (Kurang)</span>';
                                    } else {
                                        echo '<span class="text-muted">Rp 0 (Pas)</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Metode Bayar:</td>
                                <td>
                                    <?php
                                    $metode_class = '';
                                    $metode_icon = '';
                                    switch ($pembayaran->metode_bayar) {
                                        case 'tunai': 
                                            $metode_class = 'success'; 
                                            $metode_icon = '💵';
                                            break;
                                        case 'transfer': 
                                            $metode_class = 'primary'; 
                                            $metode_icon = '🏦';
                                            break;
                                        case 'qris': 
                                            $metode_class = 'info'; 
                                            $metode_icon = '📱';
                                            break;
                                        case 'debit': 
                                            $metode_class = 'warning'; 
                                            $metode_icon = '💳';
                                            break;
                                        default: 
                                            $metode_class = 'secondary'; 
                                            $metode_icon = '💰';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $metode_class; ?>">
                                        <?php echo $metode_icon . ' ' . ucfirst($pembayaran->metode_bayar); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Tanggal Bayar:</td>
                                <td><strong><?php echo format_tanggal($pembayaran->tanggal_bayar); ?></strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <?php if ($pembayaran->keterangan): ?>
                <hr>
                <div class="row">
                    <div class="col-12">
                        <strong class="text-muted">Keterangan:</strong>
                        <p class="mt-2"><?php echo nl2br(htmlspecialchars($pembayaran->keterangan)); ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Status & Verifikasi -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-check-circle me-2"></i>Status & Verifikasi</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="text-muted">Status:</td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_icon = '';
                                    switch ($pembayaran->status_verifikasi) {
                                        case 'pending': 
                                            $status_class = 'warning'; 
                                            $status_icon = '⏳';
                                            break;
                                        case 'verified': 
                                            $status_class = 'success'; 
                                            $status_icon = '✅';
                                            break;
                                        case 'rejected': 
                                            $status_class = 'danger'; 
                                            $status_icon = '❌';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $status_class; ?> fs-6">
                                        <?php echo $status_icon . ' ' . ucfirst($pembayaran->status_verifikasi); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">Petugas Input:</td>
                                <td><?php echo $pembayaran->petugas_input ?? 'System'; ?></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Tanggal Input:</td>
                                <td><?php echo format_tanggal_waktu($pembayaran->created_at); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <?php if ($pembayaran->petugas_verifikasi): ?>
                            <tr>
                                <td class="text-muted">Verifikator:</td>
                                <td><?php echo $pembayaran->verifikator_nama ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <td class="text-muted">Tanggal Verifikasi:</td>
                                <td><?php echo $pembayaran->tanggal_verifikasi ? format_tanggal_waktu($pembayaran->tanggal_verifikasi) : '-'; ?></td>
                            </tr>
                            <?php if ($pembayaran->catatan): ?>
                            <tr>
                                <td class="text-muted">Catatan Verifikasi:</td>
                                <td><?php echo nl2br(htmlspecialchars($pembayaran->catatan)); ?></td>
                            </tr>
                            <?php endif; ?>
                            <?php else: ?>
                            <tr>
                                <td colspan="2" class="text-center text-muted">
                                    <i class="fas fa-clock me-2"></i>Belum diverifikasi
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('admin/pembayaran/edit/' . $pembayaran->id); ?>" 
                       class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit Pembayaran
                    </a>
                    
                    <?php if ($pembayaran->status_verifikasi == 'pending'): ?>
                    <a href="<?php echo base_url('penagih/pembayaran/verifikasi/' . $pembayaran->id); ?>" 
                       class="btn btn-primary">
                        <i class="fas fa-check me-2"></i>Verifikasi Pembayaran
                    </a>
                    <?php endif; ?>
                    
                    <button onclick="window.print()" class="btn btn-info">
                        <i class="fas fa-print me-2"></i>Print Kwitansi
                    </button>
                    
                    <a href="<?php echo base_url('admin/pembayaran/delete/' . $pembayaran->id); ?>" 
                       class="btn btn-danger"
                       onclick="return confirm('Yakin ingin menghapus pembayaran ini?')">
                        <i class="fas fa-trash me-2"></i>Hapus
                    </a>
                </div>
            </div>
        </div>

        <!-- Info Pembayaran -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Info Pembayaran</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info p-3">
                    <ul class="mb-0" style="font-size: 14px;">
                        <li><strong>Pending:</strong> Menunggu verifikasi</li>
                        <li><strong>Verified:</strong> Pembayaran valid</li>
                        <li><strong>Rejected:</strong> Pembayaran ditolak</li>
                        <li>Selisih positif = Lebih bayar</li>
                        <li>Selisih negatif = Kurang bayar</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn-toolbar, .navbar, .sidebar, .border-bottom, .card-header, .col-md-4 {
        display: none !important;
    }
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    .col-md-8 {
        width: 100% !important;
    }
}
</style>

<?php $this->load->view('templates/footer'); ?>
