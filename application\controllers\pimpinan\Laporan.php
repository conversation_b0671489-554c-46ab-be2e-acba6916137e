<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>poran extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->helper(['format', 'flash_message']);
        
        // Cek login
        if (!$this->session->userdata('user_id')) {
            redirect('auth/login');
        }
        
        // Cek role pimpinan
        if ($this->session->userdata('role') !== 'pimpinan') {
            $this->session->set_flashdata('error', 'Akses ditolak!');
            redirect('dashboard');
        }
    }

    public function index()
    {
        $bulan = $this->input->get('bulan') ?: date('n');
        $tahun = $this->input->get('tahun') ?: date('Y');

        $data = [
            'title' => 'Laporan Bulanan PDAM',
            'bulan' => $bulan,
            'tahun' => $tahun
        ];

        // Data statistik - SIMPLE
        $data['total_pelanggan'] = $this->db->count_all('pelanggan');
        $data['pelanggan_aktif'] = $this->db->where('status_langganan', 'aktif')->count_all_results('pelanggan');
        
        // Reset untuk query berikutnya
        $this->db->reset_query();
        
        // Data penggunaan bulan ini
        $penggunaan_bulan_ini = $this->db->where('bulan', $bulan)
                                         ->where('tahun', $tahun)
                                         ->get('penggunaan_air')
                                         ->result_array();
        
        $data['total_pencatatan'] = count($penggunaan_bulan_ini);
        $data['total_pemakaian'] = 0;
        $data['total_tagihan'] = 0;
        
        foreach($penggunaan_bulan_ini as $p) {
            $data['total_pemakaian'] += $p['pemakaian'];
            $data['total_tagihan'] += $p['total_tagihan'];
        }
        
        // Data pembayaran bulan ini
        $pembayaran_bulan_ini = $this->db->select('pembayaran.*')
                                         ->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan')
                                         ->where('penggunaan_air.bulan', $bulan)
                                         ->where('penggunaan_air.tahun', $tahun)
                                         ->get('pembayaran')
                                         ->result_array();
        
        $data['total_terbayar'] = 0;
        foreach($pembayaran_bulan_ini as $p) {
            $data['total_terbayar'] += $p['jumlah_bayar'];
        }
        
        $data['total_pembayaran'] = count($pembayaran_bulan_ini);
        
        // Hitung persentase
        $data['persentase_pembayaran'] = $data['total_tagihan'] > 0 ? 
            ($data['total_terbayar'] / $data['total_tagihan']) * 100 : 0;
        $data['persentase_pencatatan'] = $data['total_pelanggan'] > 0 ? 
            ($data['total_pencatatan'] / $data['total_pelanggan']) * 100 : 0;

        // Status pembayaran detail
        $verified = 0; $pending = 0; $rejected = 0;
        foreach($pembayaran_bulan_ini as $p) {
            if($p['status_verifikasi'] == 'verified') $verified++;
            elseif($p['status_verifikasi'] == 'pending') $pending++;
            elseif($p['status_verifikasi'] == 'rejected') $rejected++;
        }
        
        $data['status_pembayaran'] = [
            'verified' => $verified,
            'pending' => $pending,
            'rejected' => $rejected
        ];

        $this->load->view('templates/header', $data);
        $this->load->view('templates/sidebar');
        $this->load->view('pimpinan/laporan/index', $data);
        $this->load->view('templates/footer');
    }

    public function print()
    {
        $bulan = $this->input->get('bulan') ?: date('n');
        $tahun = $this->input->get('tahun') ?: date('Y');

        $data = [
            'title' => 'Cetak Laporan Bulanan PDAM',
            'bulan' => $bulan,
            'tahun' => $tahun,
            'bulan_nama' => nama_bulan($bulan),
            'tanggal_cetak' => date('d/m/Y H:i:s')
        ];

        // Data statistik - SIMPLE
        $data['total_pelanggan'] = $this->db->count_all('pelanggan');
        $data['pelanggan_aktif'] = $this->db->where('status_langganan', 'aktif')->count_all_results('pelanggan');
        
        // Reset untuk query berikutnya
        $this->db->reset_query();
        
        // Data penggunaan bulan ini
        $penggunaan_bulan_ini = $this->db->where('bulan', $bulan)
                                         ->where('tahun', $tahun)
                                         ->get('penggunaan_air')
                                         ->result_array();
        
        $data['total_pencatatan'] = count($penggunaan_bulan_ini);
        $data['total_pemakaian'] = 0;
        $data['total_tagihan'] = 0;
        
        foreach($penggunaan_bulan_ini as $p) {
            $data['total_pemakaian'] += $p['pemakaian'];
            $data['total_tagihan'] += $p['total_tagihan'];
        }
        
        // Data pembayaran bulan ini
        $pembayaran_bulan_ini = $this->db->select('pembayaran.*')
                                         ->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan')
                                         ->where('penggunaan_air.bulan', $bulan)
                                         ->where('penggunaan_air.tahun', $tahun)
                                         ->get('pembayaran')
                                         ->result_array();
        
        $data['total_terbayar'] = 0;
        foreach($pembayaran_bulan_ini as $p) {
            $data['total_terbayar'] += $p['jumlah_bayar'];
        }
        
        $data['total_pembayaran'] = count($pembayaran_bulan_ini);
        
        // Hitung persentase
        $data['persentase_pembayaran'] = $data['total_tagihan'] > 0 ? 
            ($data['total_terbayar'] / $data['total_tagihan']) * 100 : 0;
        $data['persentase_pencatatan'] = $data['total_pelanggan'] > 0 ? 
            ($data['total_pencatatan'] / $data['total_pelanggan']) * 100 : 0;

        // Status pembayaran detail
        $verified = 0; $pending = 0; $rejected = 0;
        foreach($pembayaran_bulan_ini as $p) {
            if($p['status_verifikasi'] == 'verified') $verified++;
            elseif($p['status_verifikasi'] == 'pending') $pending++;
            elseif($p['status_verifikasi'] == 'rejected') $rejected++;
        }
        
        $data['status_pembayaran'] = [
            'verified' => $verified,
            'pending' => $pending,
            'rejected' => $rejected
        ];

        // Pengaturan perusahaan
        $perusahaan = $this->db->select('nilai')->where('nama_setting', 'nama_perusahaan')->get('pengaturan')->row();
        $data['nama_perusahaan'] = $perusahaan ? $perusahaan->nilai : 'PDAM TIRTA SEJAHTERA';
        
        $alamat = $this->db->select('nilai')->where('nama_setting', 'alamat_perusahaan')->get('pengaturan')->row();
        $data['alamat_perusahaan'] = $alamat ? $alamat->nilai : 'Jl. Merdeka No. 123, Kota';

        $this->load->view('pimpinan/laporan/print', $data);
    }

    public function tahunan()
    {
        $tahun = $this->input->get('tahun') ?: date('Y');
        $tahun_sebelumnya = $tahun - 1;

        $data = [
            'title' => 'Laporan Tahunan PDAM',
            'tahun' => $tahun,
            'tahun_sebelumnya' => $tahun_sebelumnya
        ];

        // Data statistik tahunan
        $data['total_pelanggan'] = $this->db->count_all('pelanggan');
        $data['pelanggan_baru_tahun_ini'] = $this->db->where('YEAR(tanggal_pemasangan)', $tahun)->count_all_results('pelanggan');
        
        // Reset query
        $this->db->reset_query();
        
        // Ringkasan per bulan dalam tahun
        $data['ringkasan_bulanan'] = [];
        for ($bulan = 1; $bulan <= 12; $bulan++) {
            // Query penggunaan per bulan
            $penggunaan = $this->db->where('bulan', $bulan)
                                   ->where('tahun', $tahun)
                                   ->get('penggunaan_air')
                                   ->result_array();
            
            // Query pembayaran per bulan
            $pembayaran = $this->db->select('pembayaran.*')
                                   ->join('penggunaan_air', 'penggunaan_air.id = pembayaran.id_penggunaan')
                                   ->where('penggunaan_air.bulan', $bulan)
                                   ->where('penggunaan_air.tahun', $tahun)
                                   ->get('pembayaran')
                                   ->result_array();
            
            $total_pemakaian = 0;
            $total_tagihan = 0;
            foreach($penggunaan as $p) {
                $total_pemakaian += $p['pemakaian'];
                $total_tagihan += $p['total_tagihan'];
            }
            
            $total_terbayar = 0;
            foreach($pembayaran as $p) {
                $total_terbayar += $p['jumlah_bayar'];
            }
            
            $data['ringkasan_bulanan'][] = [
                'bulan' => $bulan,
                'nama_bulan' => nama_bulan($bulan),
                'pencatatan' => count($penggunaan),
                'total_pemakaian' => $total_pemakaian,
                'total_tagihan' => $total_tagihan,
                'total_terbayar' => $total_terbayar,
                'jumlah_pembayaran' => count($pembayaran)
            ];
        }

        // Total tahunan
        $data['total_tahun_ini'] = [
            'pencatatan' => 0,
            'pemakaian' => 0,
            'tagihan' => 0,
            'terbayar' => 0,
            'pembayaran' => 0
        ];
        
        foreach($data['ringkasan_bulanan'] as $bulan_data) {
            $data['total_tahun_ini']['pencatatan'] += $bulan_data['pencatatan'];
            $data['total_tahun_ini']['pemakaian'] += $bulan_data['total_pemakaian'];
            $data['total_tahun_ini']['tagihan'] += $bulan_data['total_tagihan'];
            $data['total_tahun_ini']['terbayar'] += $bulan_data['total_terbayar'];
            $data['total_tahun_ini']['pembayaran'] += $bulan_data['jumlah_pembayaran'];
        }

        // Perbandingan dengan tahun sebelumnya - SIMPLE
        $data['total_tahun_lalu'] = [
            'pencatatan' => 0,
            'pemakaian' => 0,
            'tagihan' => 0,
            'terbayar' => 0,
            'pembayaran' => 0
        ];

        // Hitung pertumbuhan
        $data['pertumbuhan'] = [
            'pencatatan' => 100,
            'pemakaian' => 100,
            'tagihan' => 100,
            'terbayar' => 100,
            'pembayaran' => 100
        ];

        // Top performing months
        $sorted_months = $data['ringkasan_bulanan'];
        usort($sorted_months, function($a, $b) {
            return $b['total_terbayar'] <=> $a['total_terbayar'];
        });
        $data['top_months'] = array_slice($sorted_months, 0, 3);

        // Rata-rata per bulan
        $data['rata_rata_bulanan'] = [
            'pencatatan' => $data['total_tahun_ini']['pencatatan'] / 12,
            'pemakaian' => $data['total_tahun_ini']['pemakaian'] / 12,
            'tagihan' => $data['total_tahun_ini']['tagihan'] / 12,
            'terbayar' => $data['total_tahun_ini']['terbayar'] / 12
        ];

        $this->load->view('templates/header', $data);
        $this->load->view('templates/sidebar');
        $this->load->view('pimpinan/laporan/tahunan', $data);
        $this->load->view('templates/footer');
    }
}