<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Pelanggan extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->library('auth_lib');
        $this->auth_lib->require_role('admin'); // Hanya admin yang bisa akses
        
        $this->load->model('Pelanggan_model');
        $this->load->model('Penggunaan_model');
        $this->load->model('Pembayaran_model');
        $this->load->library('pagination');
    }

    /**
     * Halaman utama data pelanggan
     */
    public function index()
    {
        $data['title'] = 'Data Pelanggan - PDAM System';
        
        // Pagination config
        $config['base_url'] = base_url('admin/pelanggan/index');
        $config['total_rows'] = $this->Pelanggan_model->count_total();
        $config['per_page'] = 15;
        $config['uri_segment'] = 4;
        
        // Pagination styling
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_link'] = '&laquo; First';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_link'] = 'Last &raquo;';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $config['next_link'] = 'Next &raquo;';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['prev_link'] = '&laquo; Previous';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><a class="page-link" href="#">';
        $config['cur_tag_close'] = '</a></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');

        $this->pagination->initialize($config);
        
        $page = ($this->uri->segment(4)) ? $this->uri->segment(4) : 0;
        $search = $this->input->get('search');
        $status = $this->input->get('status');
        
        $data['pelanggan'] = $this->Pelanggan_model->get_paginated($config['per_page'], $page, $search, $status);
        $data['pagination'] = $this->pagination->create_links();
        $data['search'] = $search;
        $data['status_filter'] = $status;
        
        // Statistics
        $data['total_pelanggan'] = $this->Pelanggan_model->count_all();
        $data['pelanggan_aktif'] = $this->Pelanggan_model->count_by_status('aktif');
        $data['pelanggan_nonaktif'] = $this->Pelanggan_model->count_by_status('nonaktif');
        $data['pelanggan_putus'] = $this->Pelanggan_model->count_by_status('putus');
        
        $this->load->view('admin/pelanggan/index', $data);
    }

    /**
     * Form tambah pelanggan baru
     */
    public function add()
    {
        $data['title'] = 'Tambah Pelanggan - PDAM System';
        $data['action'] = 'add';
        
        if ($this->input->post()) {
            $this->form_validation->set_rules('no_pelanggan', 'No Pelanggan', 'required|trim|is_unique[pelanggan.no_pelanggan]|max_length[20]');
            $this->form_validation->set_rules('nama_pelanggan', 'Nama Pelanggan', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('alamat', 'Alamat', 'required|trim');
            $this->form_validation->set_rules('no_hp', 'No HP', 'trim|max_length[15]');
            $this->form_validation->set_rules('email', 'Email', 'trim|valid_email|max_length[100]');
            $this->form_validation->set_rules('tanggal_pemasangan', 'Tanggal Pemasangan', 'required');

            if ($this->form_validation->run() == TRUE) {
                $pelanggan_data = array(
                    'no_pelanggan' => $this->input->post('no_pelanggan'),
                    'nama_pelanggan' => $this->input->post('nama_pelanggan'),
                    'alamat' => $this->input->post('alamat'),
                    'no_hp' => $this->input->post('no_hp'),
                    'email' => $this->input->post('email'),
                    'tanggal_pemasangan' => $this->input->post('tanggal_pemasangan'),
                    'status_langganan' => 'aktif'
                );

                if ($this->Pelanggan_model->insert($pelanggan_data)) {
                    // Log activity
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Menambah pelanggan baru: ' . $pelanggan_data['no_pelanggan'],
                        'pelanggan'
                    );
                    
                    set_flash_message('success', 'Pelanggan berhasil ditambahkan');
                    redirect('admin/pelanggan');
                } else {
                    set_flash_message('error', 'Gagal menambahkan pelanggan');
                }
            }
        }

        // Generate nomor pelanggan otomatis jika kosong
        $data['auto_no_pelanggan'] = $this->generate_no_pelanggan();
        
        $this->load->view('admin/pelanggan/form', $data);
    }

    /**
     * Form edit pelanggan
     */
    public function edit($id = null)
    {
        if (!$id) {
            show_404();
        }

        $pelanggan = $this->Pelanggan_model->get_by_id($id);
        if (!$pelanggan) {
            show_404();
        }

        $data['title'] = 'Edit Pelanggan - PDAM System';
        $data['action'] = 'edit';
        $data['pelanggan'] = $pelanggan;

        if ($this->input->post()) {
            $this->form_validation->set_rules('no_pelanggan', 'No Pelanggan', 'required|trim|max_length[20]|callback_check_no_pelanggan_exists[' . $id . ']');
            $this->form_validation->set_rules('nama_pelanggan', 'Nama Pelanggan', 'required|trim|max_length[100]');
            $this->form_validation->set_rules('alamat', 'Alamat', 'required|trim');
            $this->form_validation->set_rules('no_hp', 'No HP', 'trim|max_length[15]');
            $this->form_validation->set_rules('email', 'Email', 'trim|valid_email|max_length[100]');
            $this->form_validation->set_rules('tanggal_pemasangan', 'Tanggal Pemasangan', 'required');
            $this->form_validation->set_rules('status_langganan', 'Status Langganan', 'required|in_list[aktif,nonaktif,putus]');

            if ($this->form_validation->run() == TRUE) {
                $pelanggan_data = array(
                    'no_pelanggan' => $this->input->post('no_pelanggan'),
                    'nama_pelanggan' => $this->input->post('nama_pelanggan'),
                    'alamat' => $this->input->post('alamat'),
                    'no_hp' => $this->input->post('no_hp'),
                    'email' => $this->input->post('email'),
                    'tanggal_pemasangan' => $this->input->post('tanggal_pemasangan'),
                    'status_langganan' => $this->input->post('status_langganan')
                );

                if ($this->Pelanggan_model->update($id, $pelanggan_data)) {
                    // Log activity
                    $this->auth_lib->log_activity(
                        $this->auth_lib->get_user_data('user_id'),
                        'Mengedit pelanggan: ' . $pelanggan_data['no_pelanggan'],
                        'pelanggan',
                        $id
                    );
                    
                    set_flash_message('success', 'Data pelanggan berhasil diupdate');
                    redirect('admin/pelanggan');
                } else {
                    set_flash_message('error', 'Gagal mengupdate data pelanggan');
                }
            }
        }

        $this->load->view('admin/pelanggan/form', $data);
    }

    /**
     * Detail pelanggan
     */
    public function detail($id = null)
    {
        if (!$id) {
            show_404();
        }

        $pelanggan = $this->Pelanggan_model->get_by_id($id);
        if (!$pelanggan) {
            show_404();
        }

        $data['title'] = 'Detail Pelanggan - PDAM System';
        $data['pelanggan'] = $pelanggan;
        
        // Riwayat penggunaan air (6 bulan terakhir)
        $data['riwayat_penggunaan'] = $this->Penggunaan_model->get_by_pelanggan($id, 6);
        
        // Riwayat pembayaran (10 terakhir)
        $data['riwayat_pembayaran'] = $this->Pembayaran_model->get_by_pelanggan($id, 10);
        
        // Statistik
        $data['total_tagihan'] = $this->Penggunaan_model->sum_tagihan_by_pelanggan($id);
        $data['total_terbayar'] = $this->Pembayaran_model->sum_by_pelanggan($id);
        $data['tunggakan'] = $data['total_tagihan'] - $data['total_terbayar'];
        
        $this->load->view('admin/pelanggan/detail', $data);
    }

    /**
     * Hapus pelanggan
     */
    public function delete($id = null)
    {
        if (!$id) {
            show_404();
        }

        $pelanggan = $this->Pelanggan_model->get_by_id($id);
        if (!$pelanggan) {
            show_404();
        }

        // Cek apakah ada data penggunaan/pembayaran
        $has_usage = $this->Penggunaan_model->count_by_pelanggan($id) > 0;
        
        if ($has_usage) {
            set_flash_message('error', 'Tidak dapat menghapus pelanggan. Masih ada data penggunaan air yang terkait.');
            redirect('admin/pelanggan');
        }

        if ($this->Pelanggan_model->delete($id)) {
            // Log activity
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Menghapus pelanggan: ' . $pelanggan->no_pelanggan,
                'pelanggan',
                $id
            );
            
            set_flash_message('success', 'Pelanggan berhasil dihapus');
        } else {
            set_flash_message('error', 'Gagal menghapus pelanggan');
        }

        redirect('admin/pelanggan');
    }

    /**
     * Toggle status pelanggan
     */
    public function toggle_status($id = null)
    {
        if (!$id) {
            show_404();
        }

        $pelanggan = $this->Pelanggan_model->get_by_id($id);
        if (!$pelanggan) {
            show_404();
        }

        $new_status = ($pelanggan->status_langganan == 'aktif') ? 'nonaktif' : 'aktif';

        if ($this->Pelanggan_model->update($id, array('status_langganan' => $new_status))) {
            // Log activity
            $this->auth_lib->log_activity(
                $this->auth_lib->get_user_data('user_id'),
                'Mengubah status pelanggan ' . $pelanggan->no_pelanggan . ' menjadi ' . $new_status,
                'pelanggan',
                $id
            );
            
            set_flash_message('success', 'Status pelanggan berhasil diubah menjadi ' . $new_status);
        } else {
            set_flash_message('error', 'Gagal mengubah status pelanggan');
        }

        redirect('admin/pelanggan');
    }

    /**
     * Import pelanggan dari Excel
     */
    public function import()
    {
        $data['title'] = 'Import Pelanggan - PDAM System';
        
        if ($this->input->post()) {
            $config['upload_path'] = './uploads/import/';
            $config['allowed_types'] = 'xlsx|xls|csv';
            $config['max_size'] = 2048; // 2MB
            $config['file_name'] = 'import_pelanggan_' . time();

            // Buat folder jika belum ada
            if (!is_dir($config['upload_path'])) {
                mkdir($config['upload_path'], 0755, true);
            }

            $this->load->library('upload', $config);

            if ($this->upload->do_upload('file_import')) {
                $file_data = $this->upload->data();
                $file_path = $file_data['full_path'];

                // Process Excel file
                $import_result = $this->process_import_file($file_path);
                
                // Delete uploaded file
                unlink($file_path);
                
                if ($import_result['success']) {
                    set_flash_message('success', 'Import berhasil! ' . $import_result['imported'] . ' data berhasil diimport.');
                } else {
                    set_flash_message('error', 'Import gagal: ' . $import_result['message']);
                }
                
                redirect('admin/pelanggan');
            } else {
                $data['upload_error'] = $this->upload->display_errors();
            }
        }
        
        $this->load->view('admin/pelanggan/import', $data);
    }

    /**
     * Export pelanggan ke Excel
     */
    public function export()
    {
        $pelanggan = $this->Pelanggan_model->get_all_for_export();
        
        // Set headers untuk download
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="data_pelanggan_' . date('Y-m-d') . '.xls"');
        
        echo "<table border='1'>";
        echo "<tr>";
        echo "<th>No</th>";
        echo "<th>No Pelanggan</th>";
        echo "<th>Nama Pelanggan</th>";
        echo "<th>Alamat</th>";
        echo "<th>No HP</th>";
        echo "<th>Email</th>";
        echo "<th>Tanggal Pemasangan</th>";
        echo "<th>Status</th>";
        echo "<th>Dibuat</th>";
        echo "</tr>";
        
        $no = 1;
        foreach ($pelanggan as $p) {
            echo "<tr>";
            echo "<td>" . $no++ . "</td>";
            echo "<td>" . $p->no_pelanggan . "</td>";
            echo "<td>" . $p->nama_pelanggan . "</td>";
            echo "<td>" . $p->alamat . "</td>";
            echo "<td>" . $p->no_hp . "</td>";
            echo "<td>" . $p->email . "</td>";
            echo "<td>" . format_tanggal($p->tanggal_pemasangan) . "</td>";
            echo "<td>" . ucfirst($p->status_langganan) . "</td>";
            echo "<td>" . format_tanggal($p->created_at) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }

    /**
     * Custom validation untuk check no_pelanggan exists
     */
    public function check_no_pelanggan_exists($no_pelanggan, $exclude_id)
    {
        if ($this->Pelanggan_model->is_no_pelanggan_exists($no_pelanggan, $exclude_id)) {
            $this->form_validation->set_message('check_no_pelanggan_exists', 'Nomor pelanggan sudah digunakan');
            return FALSE;
        }
        return TRUE;
    }

    /**
     * Generate nomor pelanggan otomatis
     */
    private function generate_no_pelanggan()
    {
        do {
            $no_pelanggan = 'PLG' . date('Y') . sprintf('%06d', rand(1, 999999));
        } while ($this->Pelanggan_model->is_no_pelanggan_exists($no_pelanggan));
        
        return $no_pelanggan;
    }

    /**
     * Process import file
     */
    private function process_import_file($file_path)
    {
        // Simplified Excel processing
        // In real implementation, use PhpSpreadsheet library
        
        $result = array(
            'success' => false,
            'imported' => 0,
            'message' => ''
        );
        
        try {
            // Mock import process
            // Real implementation would parse Excel/CSV file
            $result['success'] = true;
            $result['imported'] = 0;
            $result['message'] = 'Import feature ready for implementation';
            
        } catch (Exception $e) {
            $result['message'] = $e->getMessage();
        }
        
        return $result;
    }

    /**
     * AJAX search pelanggan
     */
    public function ajax_search()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $search = $this->input->post('search');
        $pelanggan = $this->Pelanggan_model->search($search, 10);

        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($pelanggan));
    }

    /**
     * Bulk operations
     */
    public function bulk_action()
    {
        if (!$this->input->post()) {
            redirect('admin/pelanggan');
        }

        $action = $this->input->post('bulk_action');
        $selected_ids = $this->input->post('selected_ids');

        if (empty($selected_ids)) {
            set_flash_message('error', 'Tidak ada data yang dipilih');
            redirect('admin/pelanggan');
        }

        switch ($action) {
            case 'activate':
                $this->Pelanggan_model->bulk_update_status($selected_ids, 'aktif');
                set_flash_message('success', count($selected_ids) . ' pelanggan berhasil diaktifkan');
                break;
                
            case 'deactivate':
                $this->Pelanggan_model->bulk_update_status($selected_ids, 'nonaktif');
                set_flash_message('success', count($selected_ids) . ' pelanggan berhasil dinonaktifkan');
                break;
                
            case 'delete':
                // Check for dependencies first
                $can_delete = true;
                foreach ($selected_ids as $id) {
                    if ($this->Penggunaan_model->count_by_pelanggan($id) > 0) {
                        $can_delete = false;
                        break;
                    }
                }
                
                if ($can_delete) {
                    $this->Pelanggan_model->bulk_delete($selected_ids);
                    set_flash_message('success', count($selected_ids) . ' pelanggan berhasil dihapus');
                } else {
                    set_flash_message('error', 'Beberapa pelanggan tidak dapat dihapus karena masih memiliki data terkait');
                }
                break;
                
            default:
                set_flash_message('error', 'Aksi tidak valid');
        }

        redirect('admin/pelanggan');
    }
}