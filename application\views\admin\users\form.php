<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <?php echo ($action == 'add') ? 'Tambah User' : 'Edit User'; ?>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="<?php echo base_url('admin/users'); ?>" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-<?php echo ($action == 'add') ? 'plus' : 'edit'; ?>"></i>
                    <?php echo ($action == 'add') ? 'Tambah User Baru' : 'Edit Data User'; ?>
                </h5>
            </div>
            <div class="card-body">
                <?php echo form_open('', 'id="userForm"'); ?>
                
                <div class="row">
                    <!-- Username -->
                    <div class="col-md-6 mb-3">
                        <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control <?php echo form_error('username') ? 'is-invalid' : ''; ?>" 
                                   id="username" name="username" 
                                   value="<?php echo set_value('username', isset($user) ? $user->username : ''); ?>"
                                   placeholder="Masukkan username" required>
                        </div>
                        <?php echo form_error('username', '<div class="invalid-feedback">', '</div>'); ?>
                        <div class="form-text">Username harus unik dan minimal 3 karakter</div>
                    </div>
                    
                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control <?php echo form_error('email') ? 'is-invalid' : ''; ?>" 
                                   id="email" name="email" 
                                   value="<?php echo set_value('email', isset($user) ? $user->email : ''); ?>"
                                   placeholder="Masukkan email" required>
                        </div>
                        <?php echo form_error('email', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Password -->
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">
                            Password 
                            <?php if ($action == 'add'): ?>
                                <span class="text-danger">*</span>
                            <?php else: ?>
                                <small class="text-muted">(Kosongkan jika tidak ingin mengubah)</small>
                            <?php endif; ?>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control <?php echo form_error('password') ? 'is-invalid' : ''; ?>" 
                                   id="password" name="password" 
                                   placeholder="Masukkan password"
                                   <?php echo ($action == 'add') ? 'required' : ''; ?>>
                            <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <?php echo form_error('password', '<div class="invalid-feedback">', '</div>'); ?>
                        <div class="form-text">Password minimal 6 karakter</div>
                    </div>
                    
                    <!-- Confirm Password -->
                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">
                            Konfirmasi Password
                            <?php if ($action == 'add'): ?>
                                <span class="text-danger">*</span>
                            <?php endif; ?>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control <?php echo form_error('confirm_password') ? 'is-invalid' : ''; ?>" 
                                   id="confirm_password" name="confirm_password" 
                                   placeholder="Konfirmasi password"
                                   <?php echo ($action == 'add') ? 'required' : ''; ?>>
                        </div>
                        <?php echo form_error('confirm_password', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                </div>
                
                <!-- Nama Lengkap -->
                <div class="mb-3">
                    <label for="nama_lengkap" class="form-label">Nama Lengkap <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-id-card"></i>
                        </span>
                        <input type="text" class="form-control <?php echo form_error('nama_lengkap') ? 'is-invalid' : ''; ?>" 
                               id="nama_lengkap" name="nama_lengkap" 
                               value="<?php echo set_value('nama_lengkap', isset($user) ? $user->nama_lengkap : ''); ?>"
                               placeholder="Masukkan nama lengkap" required>
                    </div>
                    <?php echo form_error('nama_lengkap', '<div class="invalid-feedback">', '</div>'); ?>
                </div>
                
                <div class="row">
                    <!-- Role -->
                    <div class="col-md-6 mb-3">
                        <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user-tag"></i>
                            </span>
                            <select class="form-select <?php echo form_error('role') ? 'is-invalid' : ''; ?>" 
                                    id="role" name="role" required>
                                <option value="">Pilih Role</option>
                                <option value="admin" <?php echo set_select('role', 'admin', isset($user) && $user->role == 'admin'); ?>>
                                    Admin
                                </option>
                                <option value="penagih" <?php echo set_select('role', 'penagih', isset($user) && $user->role == 'penagih'); ?>>
                                    Penagih
                                </option>
                                <option value="pimpinan" <?php echo set_select('role', 'pimpinan', isset($user) && $user->role == 'pimpinan'); ?>>
                                    Pimpinan
                                </option>
                            </select>
                        </div>
                        <?php echo form_error('role', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                    
                    <!-- Status (hanya untuk edit) -->
                    <?php if ($action == 'edit'): ?>
                    <div class="col-md-6 mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-toggle-on"></i>
                            </span>
                            <select class="form-select <?php echo form_error('status') ? 'is-invalid' : ''; ?>" 
                                    id="status" name="status" required>
                                <option value="aktif" <?php echo set_select('status', 'aktif', isset($user) && $user->status == 'aktif'); ?>>
                                    Aktif
                                </option>
                                <option value="nonaktif" <?php echo set_select('status', 'nonaktif', isset($user) && $user->status == 'nonaktif'); ?>>
                                    Nonaktif
                                </option>
                            </select>
                        </div>
                        <?php echo form_error('status', '<div class="invalid-feedback">', '</div>'); ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- No HP -->
                <div class="mb-3">
                    <label for="no_hp" class="form-label">No HP</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-phone"></i>
                        </span>
                        <input type="tel" class="form-control <?php echo form_error('no_hp') ? 'is-invalid' : ''; ?>" 
                               id="no_hp" name="no_hp" 
                               value="<?php echo set_value('no_hp', isset($user) ? $user->no_hp : ''); ?>"
                               placeholder="Masukkan nomor HP">
                    </div>
                    <?php echo form_error('no_hp', '<div class="invalid-feedback">', '</div>'); ?>
                </div>
                
                <!-- Alamat -->
                <div class="mb-3">
                    <label for="alamat" class="form-label">Alamat</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-map-marker-alt"></i>
                        </span>
                        <textarea class="form-control <?php echo form_error('alamat') ? 'is-invalid' : ''; ?>" 
                                  id="alamat" name="alamat" rows="3" 
                                  placeholder="Masukkan alamat lengkap"><?php echo set_value('alamat', isset($user) ? $user->alamat : ''); ?></textarea>
                    </div>
                    <?php echo form_error('alamat', '<div class="invalid-feedback">', '</div>'); ?>
                </div>
                
                <hr>
                
                <!-- Submit Buttons -->
                <div class="d-flex justify-content-between">
                    <a href="<?php echo base_url('admin/users'); ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Batal
                    </a>
                    <div>
                        <button type="reset" class="btn btn-outline-warning me-2">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            <?php echo ($action == 'add') ? 'Simpan' : 'Update'; ?>
                        </button>
                    </div>
                </div>
                
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
    
    <!-- Sidebar Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Informasi
                </h6>
            </div>
            <div class="card-body">
                <h6>Role Penjelasan:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <span class="badge bg-primary me-2">Admin</span>
                        Mengelola seluruh sistem, user, dan pengaturan
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-success me-2">Penagih</span>
                        Input penggunaan air dan verifikasi pembayaran
                    </li>
                    <li class="mb-2">
                        <span class="badge bg-info me-2">Pimpinan</span>
                        Melihat laporan dan statistik sistem
                    </li>
                </ul>
                
                <hr>
                
                <h6>Catatan Keamanan:</h6>
                <ul class="small text-muted">
                    <li>Password minimal 6 karakter</li>
                    <li>Username harus unik</li>
                    <li>Email harus valid dan unik</li>
                    <li>Data yang diberi tanda * wajib diisi</li>
                </ul>
                
                <?php if ($action == 'edit' && isset($user)): ?>
                <hr>
                <h6>Info User:</h6>
                <ul class="small">
                    <li><strong>Dibuat:</strong> <?php echo format_tanggal($user->created_at, 'd/m/Y H:i'); ?></li>
                    <li><strong>Diupdate:</strong> <?php echo format_tanggal($user->updated_at, 'd/m/Y H:i'); ?></li>
                    <li><strong>Status:</strong> 
                        <span class="badge bg-<?php echo ($user->status == 'aktif') ? 'success' : 'danger'; ?>">
                            <?php echo ucfirst($user->status); ?>
                        </span>
                    </li>
                </ul>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if ($action == 'edit' && isset($user)): ?>
                    <a href="<?php echo base_url('admin/users/reset_password/' . $user->id); ?>" 
                       class="btn btn-outline-warning btn-sm"
                       onclick="return confirm('Yakin ingin reset password user ini?')">
                        <i class="fas fa-key"></i> Reset Password
                    </a>
                    <a href="<?php echo base_url('admin/users/toggle_status/' . $user->id); ?>" 
                       class="btn btn-outline-info btn-sm"
                       onclick="return confirm('Yakin ingin mengubah status user ini?')">
                        <i class="fas fa-toggle-on"></i> Toggle Status
                    </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo base_url('admin/users/add'); ?>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus"></i> Tambah User Lain
                    </a>
                    <a href="<?php echo base_url('admin/users'); ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list"></i> Lihat Semua User
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Form validation
document.getElementById('userForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password && password !== confirmPassword) {
        e.preventDefault();
        alert('Password dan konfirmasi password tidak sama!');
        return false;
    }
    
    if (password && password.length < 6) {
        e.preventDefault();
        alert('Password minimal 6 karakter!');
        return false;
    }
});

// Auto format phone number
document.getElementById('no_hp').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, ''); // Remove non-digits
    if (value.startsWith('0')) {
        value = '62' + value.substring(1); // Convert 08 to 628
    }
    this.value = value;
});

// Username validation
document.getElementById('username').addEventListener('input', function() {
    this.value = this.value.toLowerCase().replace(/[^a-z0-9_]/g, '');
});
</script>

<?php $this->load->view('templates/footer'); ?>