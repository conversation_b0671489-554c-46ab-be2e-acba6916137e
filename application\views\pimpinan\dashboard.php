<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    transition: transform 0.2s;
}
.stats-card:hover {
    transform: translateY(-2px);
}
.chart-placeholder {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    color: #6c757d;
}
</style>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard Pimpinan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i> <?php echo format_tanggal(date('Y-m-d')); ?>
            </button>
            <a href="<?php echo base_url('pimpinan/laporan/export'); ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-download"></i> Export Laporan
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Overview Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo isset($total_pelanggan) ? number_format($total_pelanggan) : '0'; ?></div>
                        <div class="text-xs">
                            <span class="text-success"><?php echo isset($pelanggan_aktif) ? number_format($pelanggan_aktif) : '0'; ?> aktif</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Tagihan</div>
                        <div class="h6 mb-0 font-weight-bold"><?php echo isset($total_tagihan_bulan_ini) ? format_rupiah($total_tagihan_bulan_ini) : 'Rp 0'; ?></div>
                        <div class="text-xs">Bulan <?php echo nama_bulan(date('m')); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Terbayar</div>
                        <div class="h6 mb-0 font-weight-bold"><?php echo isset($total_terbayar_bulan_ini) ? format_rupiah($total_terbayar_bulan_ini) : 'Rp 0'; ?></div>
                        <div class="text-xs">Bulan <?php echo nama_bulan(date('m')); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Persentase Bayar</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo isset($persentase_pembayaran) ? number_format($persentase_pembayaran, 1) : '0.0'; ?>%</div>
                        <div class="progress mt-2" style="height: 4px;">
                            <div class="progress-bar bg-white" style="width: <?php echo isset($persentase_pembayaran) ? $persentase_pembayaran : 0; ?>%"></div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional KPIs -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Pencatatan Bulan Ini</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo isset($total_pencatatan_bulan_ini) ? number_format($total_pencatatan_bulan_ini) : '0'; ?></div>
                        <div class="text-xs text-muted"><?php echo nama_bulan(date('m')) . ' ' . date('Y'); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pembayaran Pending</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo isset($pembayaran_pending) ? number_format($pembayaran_pending) : '0'; ?></div>
                        <div class="text-xs text-muted">Menunggu verifikasi</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Pelanggan Baru</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo isset($pelanggan_baru_bulan_ini) ? number_format($pelanggan_baru_bulan_ini) : '0'; ?></div>
                        <div class="text-xs text-muted">Bulan ini</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Sisa Piutang</div>
                        <?php
                        $sisa_piutang = isset($total_tagihan_bulan_ini) && isset($total_terbayar_bulan_ini) ?
                            $total_tagihan_bulan_ini - $total_terbayar_bulan_ini : 0;
                        ?>
                        <div class="h6 mb-0 font-weight-bold text-gray-800"><?php echo format_rupiah($sisa_piutang); ?></div>
                        <div class="text-xs text-muted">Belum terbayar</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-table me-2"></i>Data Pembayaran 6 Bulan Terakhir</h5>
            </div>
            <div class="card-body">
                <?php if (isset($grafik_pembayaran) && !empty($grafik_pembayaran)): ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Bulan</th>
                                <th class="text-end">Total Pembayaran</th>
                                <th class="text-center">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($grafik_pembayaran as $data): ?>
                            <tr>
                                <td><strong><?php echo $data['bulan']; ?></strong></td>
                                <td class="text-end">
                                    <span class="badge bg-success"><?php echo format_rupiah($data['total']); ?></span>
                                </td>
                                <td class="text-center">
                                    <?php if ($data['total'] > 0): ?>
                                        <i class="fas fa-check-circle text-success"></i>
                                    <?php else: ?>
                                        <i class="fas fa-minus-circle text-muted"></i>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="chart-placeholder">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <h5>Belum Ada Data Pembayaran</h5>
                    <p class="mb-0">Data pembayaran akan muncul setelah ada transaksi yang diverifikasi</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Status Pembayaran</h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-success mb-0"><?php echo isset($total_terbayar_bulan_ini) ? format_rupiah($total_terbayar_bulan_ini) : 'Rp 0'; ?></h4>
                            <small class="text-muted">Terbayar</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <?php
                        $tunggakan = 0;
                        if (isset($total_tagihan_bulan_ini) && isset($total_terbayar_bulan_ini)) {
                            $tunggakan = $total_tagihan_bulan_ini - $total_terbayar_bulan_ini;
                        }
                        ?>
                        <h4 class="text-danger mb-0"><?php echo format_rupiah($tunggakan); ?></h4>
                        <small class="text-muted">Tunggakan</small>
                    </div>
                </div>

                <div class="progress mb-2" style="height: 10px;">
                    <?php
                    $persentase_terbayar = 0;
                    if (isset($total_tagihan_bulan_ini) && $total_tagihan_bulan_ini > 0) {
                        $persentase_terbayar = ($total_terbayar_bulan_ini / $total_tagihan_bulan_ini) * 100;
                    }
                    ?>
                    <div class="progress-bar bg-success" style="width: <?php echo $persentase_terbayar; ?>%"></div>
                </div>
                <small class="text-muted">
                    <?php echo number_format($persentase_terbayar, 1); ?>% dari total tagihan terbayar
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-analytics me-2"></i>Key Performance Indicators</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo isset($persentase_pembayaran) ? number_format($persentase_pembayaran, 1) : '0.0'; ?>%</h4>
                            <p class="text-muted mb-0">Collection Rate</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> Target: 85%
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <?php 
                            $avg_payment = 0;
                            if (isset($total_pelanggan) && $total_pelanggan > 0 && isset($total_terbayar_bulan_ini)) {
                                $avg_payment = $total_terbayar_bulan_ini / $total_pelanggan;
                            }
                            ?>
                            <h4 class="text-success"><?php echo format_rupiah($avg_payment); ?></h4>
                            <p class="text-muted mb-0">Avg Payment/Customer</p>
                            <small class="text-muted">Per bulan ini</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-info"><?php echo isset($pelanggan_aktif) ? number_format($pelanggan_aktif) : '0'; ?></h4>
                            <p class="text-muted mb-0">Active Customers</p>
                            <small class="text-success">
                                <?php 
                                $persentase_aktif = 0;
                                if (isset($total_pelanggan) && $total_pelanggan > 0 && isset($pelanggan_aktif)) {
                                    $persentase_aktif = ($pelanggan_aktif / $total_pelanggan) * 100;
                                }
                                echo number_format($persentase_aktif, 1); 
                                ?>% dari total
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <?php 
                        $current_month = date('n');
                        $last_month = $current_month - 1;
                        if ($last_month == 0) $last_month = 12;
                        ?>
                        <h4 class="text-warning">
                            <i class="fas fa-clock"></i>
                        </h4>
                        <p class="text-muted mb-0">Data Update</p>
                        <small class="text-muted">Real-time</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Simple dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have data
    const hasData = <?php echo (isset($total_pelanggan) && $total_pelanggan > 0) ? 'true' : 'false'; ?>;

    if (!hasData) {
        console.warn('Dashboard: No data available. Please check database connection.');
    }

    // Simple hover effects for tables
    document.querySelectorAll('.table tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // Auto dismiss alerts
    setTimeout(function() {
        document.querySelectorAll('.alert').forEach(alert => {
            if (alert.classList.contains('alert-dismissible')) {
                alert.style.display = 'none';
            }
        });
    }, 5000);

    // Simple progress bar animation
    setTimeout(() => {
        document.querySelectorAll('.progress-bar').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    }, 1000);
});

// Auto refresh every 15 minutes
setInterval(function() {
    if (document.hasFocus()) {
        location.reload();
    }
}, 900000);
</script>

<?php $this->load->view('templates/footer'); ?>