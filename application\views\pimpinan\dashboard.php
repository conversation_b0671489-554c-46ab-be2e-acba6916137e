<?php $this->load->view('templates/header'); ?>
<?php $this->load->view('templates/sidebar'); ?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Dashboard Pimpinan</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-calendar"></i> <?php echo format_tanggal(date('Y-m-d')); ?>
            </button>
            <a href="<?php echo base_url('pimpinan/laporan/export'); ?>" class="btn btn-sm btn-primary">
                <i class="fas fa-download"></i> Export Laporan
            </a>
        </div>
    </div>
</div>

<?php echo show_flash_message(); ?>

<!-- Overview Stats -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Pelanggan</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo isset($total_pelanggan) ? number_format($total_pelanggan) : '0'; ?></div>
                        <div class="text-xs">
                            <span class="text-success"><?php echo isset($pelanggan_aktif) ? number_format($pelanggan_aktif) : '0'; ?> aktif</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Tagihan</div>
                        <div class="h6 mb-0 font-weight-bold"><?php echo isset($total_tagihan_bulan_ini) ? format_rupiah($total_tagihan_bulan_ini) : 'Rp 0'; ?></div>
                        <div class="text-xs">Bulan <?php echo nama_bulan(date('m')); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Terbayar</div>
                        <div class="h6 mb-0 font-weight-bold"><?php echo isset($total_terbayar_bulan_ini) ? format_rupiah($total_terbayar_bulan_ini) : 'Rp 0'; ?></div>
                        <div class="text-xs">Bulan <?php echo nama_bulan(date('m')); ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card text-white" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Persentase Bayar</div>
                        <div class="h5 mb-0 font-weight-bold"><?php echo isset($persentase_pembayaran) ? number_format($persentase_pembayaran, 1) : '0.0'; ?>%</div>
                        <div class="progress mt-2" style="height: 4px;">
                            <div class="progress-bar bg-white" style="width: <?php echo isset($persentase_pembayaran) ? $persentase_pembayaran : 0; ?>%"></div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Tren Pembayaran 6 Bulan Terakhir</h5>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Status Pembayaran</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="150"></canvas>
                
                <div class="mt-3">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h5 class="text-success mb-0"><?php echo isset($total_terbayar_bulan_ini) ? format_rupiah($total_terbayar_bulan_ini) : 'Rp 0'; ?></h5>
                                <small class="text-muted">Terbayar</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <?php 
                            $tunggakan = 0;
                            if (isset($total_tagihan_bulan_ini) && isset($total_terbayar_bulan_ini)) {
                                $tunggakan = $total_tagihan_bulan_ini - $total_terbayar_bulan_ini;
                            }
                            ?>
                            <h5 class="text-danger mb-0"><?php echo format_rupiah($tunggakan); ?></h5>
                            <small class="text-muted">Tunggakan</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Regional Performance -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Top Performing Areas</h5>
            </div>
            <div class="card-body">
                <?php if (isset($top_areas) && !empty($top_areas)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Area</th>
                                <th class="text-center">Pembayaran</th>
                                <th class="text-end">Total Nilai</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_areas as $index => $area): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-primary me-2"><?php echo $index + 1; ?></span>
                                        <strong><?php echo $area->area; ?></strong>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-success"><?php echo $area->total_pembayaran; ?></span>
                                </td>
                                <td class="text-end">
                                    <strong><?php echo format_rupiah($area->total_nilai); ?></strong>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Belum ada data pembayaran bulan ini</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Sistem Overview</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-6">
                        <div class="border rounded p-3 text-center">
                            <i class="fas fa-database fa-2x text-primary mb-2"></i>
                            <h6>Database</h6>
                            <span class="badge bg-success">Online</span>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="border rounded p-3 text-center">
                            <i class="fas fa-envelope fa-2x text-info mb-2"></i>
                            <h6>Email Service</h6>
                            <span class="badge bg-success">Active</span>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <h6 class="text-muted mb-3">Quick Actions</h6>
                <div class="d-grid gap-2">
                    <a href="<?php echo base_url('pimpinan/laporan'); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-file-alt me-2"></i>Generate Laporan
                    </a>
                    <a href="<?php echo base_url('pimpinan/statistik'); ?>" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-2"></i>Detail Statistik
                    </a>
                    <a href="<?php echo base_url('admin/pengaturan'); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>Pengaturan Sistem
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Key Metrics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-analytics me-2"></i>Key Performance Indicators</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-primary"><?php echo isset($persentase_pembayaran) ? number_format($persentase_pembayaran, 1) : '0.0'; ?>%</h4>
                            <p class="text-muted mb-0">Collection Rate</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i> Target: 85%
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <?php 
                            $avg_payment = 0;
                            if (isset($total_pelanggan) && $total_pelanggan > 0 && isset($total_terbayar_bulan_ini)) {
                                $avg_payment = $total_terbayar_bulan_ini / $total_pelanggan;
                            }
                            ?>
                            <h4 class="text-success"><?php echo format_rupiah($avg_payment); ?></h4>
                            <p class="text-muted mb-0">Avg Payment/Customer</p>
                            <small class="text-muted">Per bulan ini</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border-end">
                            <h4 class="text-info"><?php echo isset($pelanggan_aktif) ? number_format($pelanggan_aktif) : '0'; ?></h4>
                            <p class="text-muted mb-0">Active Customers</p>
                            <small class="text-success">
                                <?php 
                                $persentase_aktif = 0;
                                if (isset($total_pelanggan) && $total_pelanggan > 0 && isset($pelanggan_aktif)) {
                                    $persentase_aktif = ($pelanggan_aktif / $total_pelanggan) * 100;
                                }
                                echo number_format($persentase_aktif, 1); 
                                ?>% dari total
                            </small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <?php 
                        $current_month = date('n');
                        $last_month = $current_month - 1;
                        if ($last_month == 0) $last_month = 12;
                        ?>
                        <h4 class="text-warning">
                            <i class="fas fa-clock"></i>
                        </h4>
                        <p class="text-muted mb-0">Data Update</p>
                        <small class="text-muted">Real-time</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Payment Trend Chart
const paymentCtx = document.getElementById('paymentChart').getContext('2d');
const paymentData = <?php echo isset($grafik_pembayaran) ? json_encode($grafik_pembayaran) : '[]'; ?>;

if (paymentData.length > 0) {
    const paymentChart = new Chart(paymentCtx, {
        type: 'line',
        data: {
            labels: paymentData.map(item => item.bulan),
            datasets: [{
                label: 'Total Pembayaran',
                data: paymentData.map(item => item.total),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString('id-ID');
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Total: Rp ' + context.parsed.y.toLocaleString('id-ID');
                        }
                    }
                }
            }
        }
    });
} else {
    // Show no data message
    paymentCtx.fillStyle = '#666';
    paymentCtx.font = '16px Arial';
    paymentCtx.textAlign = 'center';
    paymentCtx.fillText('Tidak ada data grafik', paymentCtx.canvas.width/2, paymentCtx.canvas.height/2);
}

// Status Chart (Pie)
const statusCtx = document.getElementById('statusChart').getContext('2d');
const terbayar = <?php echo isset($total_terbayar_bulan_ini) ? $total_terbayar_bulan_ini : 0; ?>;
const tunggakan = <?php echo isset($total_tagihan_bulan_ini) && isset($total_terbayar_bulan_ini) ? ($total_tagihan_bulan_ini - $total_terbayar_bulan_ini) : 0; ?>;

if (terbayar > 0 || tunggakan > 0) {
    const statusChart = new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Terbayar', 'Tunggakan'],
            datasets: [{
                data: [terbayar, tunggakan],
                backgroundColor: [
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = 'Rp ' + context.parsed.toLocaleString('id-ID');
                            return label + ': ' + value;
                        }
                    }
                }
            }
        }
    });
} else {
    // Show no data message
    statusCtx.fillStyle = '#666';
    statusCtx.font = '16px Arial';
    statusCtx.textAlign = 'center';
    statusCtx.fillText('Tidak ada data', statusCtx.canvas.width/2, statusCtx.canvas.height/2);
}

// Auto refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);

// Add loading states and error handling
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have data
    const hasData = <?php echo (isset($total_pelanggan) && $total_pelanggan > 0) ? 'true' : 'false'; ?>;
    
    if (!hasData) {
        console.warn('Dashboard: No data available. Please check database connection.');
    }
    
    // Add smooth animations
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.transform = 'translateY(0)';
            card.style.opacity = '1';
        }, index * 100);
    });
});
</script>

<?php $this->load->view('templates/footer'); ?>