<?php
// ========================================
// File: application/models/Tarif_model.php
// ========================================

defined('BASEPATH') OR exit('No direct script access allowed');

class Tarif_model extends CI_Model {

    private $table = 'tarif';
    private $primary_key = 'id';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all tarif records
     */
    public function get_all($status = null)
    {
        if ($status !== null) {
            $this->db->where('status', $status);
        }
        
        $this->db->order_by('created_at', 'DESC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get active tarif only
     */
    public function get_active()
    {
        $this->db->where('status', 'aktif');
        $this->db->order_by('nama_tarif', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get tarif by ID
     */
    public function get_by_id($id)
    {
        $this->db->where($this->primary_key, $id);
        return $this->db->get($this->table)->row();
    }

    /**
     * Get tarif by name
     */
    public function get_by_name($nama_tarif)
    {
        $this->db->where('nama_tarif', $nama_tarif);
        return $this->db->get($this->table)->row();
    }

    /**
     * Insert new tarif
     */
    public function insert($data)
    {
        // Add timestamp
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert($this->table, $data);
    }

    /**
     * Update tarif
     */
    public function update($id, $data)
    {
        // Add updated timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where($this->primary_key, $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Delete tarif
     */
    public function delete($id)
    {
        $this->db->where($this->primary_key, $id);
        return $this->db->delete($this->table);
    }

    /**
     * Toggle status tarif
     */
    public function toggle_status($id)
    {
        $tarif = $this->get_by_id($id);
        if (!$tarif) {
            return false;
        }
        
        $new_status = ($tarif->status == 'aktif') ? 'nonaktif' : 'aktif';
        return $this->update($id, array('status' => $new_status));
    }

    /**
     * Count all tarif
     */
    public function count_all($status = null)
    {
        if ($status !== null) {
            $this->db->where('status', $status);
        }
        
        return $this->db->count_all_results($this->table);
    }

    /**
     * Get tarif with pagination
     */
    public function get_paginated($limit = 10, $offset = 0, $search = '', $status = null)
    {
        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('nama_tarif', $search);
            $this->db->or_like('tarif_per_m3', $search);
            $this->db->group_end();
        }
        
        // Filter by status
        if ($status !== null && $status !== '') {
            $this->db->where('status', $status);
        }
        
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);
        return $this->db->get($this->table)->result();
    }

    /**
     * Count paginated results
     */
    public function count_paginated($search = '', $status = null)
    {
        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('nama_tarif', $search);
            $this->db->or_like('tarif_per_m3', $search);
            $this->db->group_end();
        }
        
        // Filter by status
        if ($status !== null && $status !== '') {
            $this->db->where('status', $status);
        }
        
        return $this->db->count_all_results($this->table);
    }

    /**
     * Check if tarif name exists (for validation)
     */
    public function is_name_exists($nama_tarif, $exclude_id = null)
    {
        $this->db->where('nama_tarif', $nama_tarif);
        
        if ($exclude_id !== null) {
            $this->db->where($this->primary_key . ' !=', $exclude_id);
        }
        
        return $this->db->count_all_results($this->table) > 0;
    }

    /**
     * Get tarif statistics
     */
    public function get_statistics()
    {
        $stats = array();
        
        // Total tarif
        $stats['total'] = $this->count_all();
        
        // Active tarif
        $stats['aktif'] = $this->count_all('aktif');
        
        // Inactive tarif
        $stats['nonaktif'] = $this->count_all('nonaktif');
        
        // Average tarif per m3
        $this->db->select_avg('tarif_per_m3');
        $this->db->where('status', 'aktif');
        $avg_result = $this->db->get($this->table)->row();
        $stats['rata_rata_tarif'] = $avg_result->tarif_per_m3 ?? 0;
        
        // Highest tarif
        $this->db->select_max('tarif_per_m3');
        $this->db->where('status', 'aktif');
        $max_result = $this->db->get($this->table)->row();
        $stats['tarif_tertinggi'] = $max_result->tarif_per_m3 ?? 0;
        
        // Lowest tarif
        $this->db->select_min('tarif_per_m3');
        $this->db->where('status', 'aktif');
        $min_result = $this->db->get($this->table)->row();
        $stats['tarif_terendah'] = $min_result->tarif_per_m3 ?? 0;
        
        return $stats;
    }

    /**
     * Calculate bill amount based on usage
     */
    public function calculate_bill($tarif_id, $pemakaian)
    {
        $tarif = $this->get_by_id($tarif_id);
        if (!$tarif) {
            return false;
        }
        
        // Use minimum usage if actual usage is below minimum
        $usage = max($pemakaian, $tarif->batas_minimum);
        
        // Calculate bill
        $subtotal = $usage * $tarif->tarif_per_m3;
        $total = $subtotal + $tarif->biaya_admin;
        
        return array(
            'tarif_info' => $tarif,
            'pemakaian_aktual' => $pemakaian,
            'pemakaian_ditagih' => $usage,
            'subtotal' => $subtotal,
            'biaya_admin' => $tarif->biaya_admin,
            'total_tagihan' => $total
        );
    }

    /**
     * Get tarif for dropdown/select options
     */
    public function get_dropdown_options($include_inactive = false)
    {
        if (!$include_inactive) {
            $this->db->where('status', 'aktif');
        }
        
        $this->db->order_by('nama_tarif', 'ASC');
        $tarifs = $this->db->get($this->table)->result();
        
        $options = array();
        foreach ($tarifs as $tarif) {
            $label = $tarif->nama_tarif . ' (Rp ' . number_format($tarif->tarif_per_m3, 0, ',', '.') . '/m³)';
            $options[$tarif->id] = $label;
        }
        
        return $options;
    }

    /**
     * Get most used tarif
     */
    public function get_most_used($limit = 5)
    {
        $this->db->select('t.*, COUNT(p.id) as usage_count');
        $this->db->from($this->table . ' t');
        $this->db->join('penggunaan_air p', 't.id = p.id_tarif', 'left');
        $this->db->where('t.status', 'aktif');
        $this->db->group_by('t.id');
        $this->db->order_by('usage_count', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get least used tarif
     */
    public function get_least_used($limit = 5)
    {
        $this->db->select('t.*, COUNT(p.id) as usage_count');
        $this->db->from($this->table . ' t');
        $this->db->join('penggunaan_air p', 't.id = p.id_tarif', 'left');
        $this->db->where('t.status', 'aktif');
        $this->db->group_by('t.id');
        $this->db->order_by('usage_count', 'ASC');
        $this->db->limit($limit);
        
        return $this->db->get()->result();
    }

    /**
     * Get tarif usage report
     */
    public function get_usage_report($year = null, $month = null)
    {
        $this->db->select('t.nama_tarif, t.tarif_per_m3, COUNT(p.id) as total_usage, SUM(p.total_tagihan) as total_revenue');
        $this->db->from($this->table . ' t');
        $this->db->join('penggunaan_air p', 't.id = p.id_tarif', 'inner');
        
        if ($year) {
            $this->db->where('p.tahun', $year);
        }
        
        if ($month) {
            $this->db->where('p.bulan', $month);
        }
        
        $this->db->group_by('t.id');
        $this->db->order_by('total_revenue', 'DESC');
        
        return $this->db->get()->result();
    }

    /**
     * Bulk update status
     */
    public function bulk_update_status($ids, $status)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }
        
        $data = array(
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->where_in($this->primary_key, $ids);
        return $this->db->update($this->table, $data);
    }

    /**
     * Bulk delete
     */
    public function bulk_delete($ids)
    {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }
        
        // Check if any tarif is being used
        $this->db->where_in('id_tarif', $ids);
        $usage_count = $this->db->count_all_results('penggunaan_air');
        
        if ($usage_count > 0) {
            return array('success' => false, 'message' => 'Tidak dapat menghapus tarif yang sedang digunakan');
        }
        
        $this->db->where_in($this->primary_key, $ids);
        $deleted = $this->db->delete($this->table);
        
        return array('success' => $deleted, 'message' => $deleted ? 'Berhasil menghapus tarif' : 'Gagal menghapus tarif');
    }

    /**
     * Export tarif data
     */
    public function export_data($format = 'array')
    {
        $this->db->order_by('nama_tarif', 'ASC');
        $tarifs = $this->db->get($this->table)->result();
        
        if ($format == 'csv') {
            $csv_data = array();
            $csv_data[] = array('Nama Tarif', 'Tarif per m³', 'Batas Minimum', 'Biaya Admin', 'Status', 'Dibuat');
            
            foreach ($tarifs as $tarif) {
                $csv_data[] = array(
                    $tarif->nama_tarif,
                    $tarif->tarif_per_m3,
                    $tarif->batas_minimum,
                    $tarif->biaya_admin,
                    $tarif->status,
                    $tarif->created_at
                );
            }
            
            return $csv_data;
        }
        
        return $tarifs;
    }

    /**
     * Search tarif
     */
    public function search($keyword)
    {
        $this->db->group_start();
        $this->db->like('nama_tarif', $keyword);
        $this->db->or_like('tarif_per_m3', $keyword);
        $this->db->or_like('batas_minimum', $keyword);
        $this->db->or_like('biaya_admin', $keyword);
        $this->db->group_end();
        
        $this->db->order_by('nama_tarif', 'ASC');
        return $this->db->get($this->table)->result();
    }

    /**
     * Get tarif by price range
     */
    public function get_by_price_range($min_price = null, $max_price = null)
    {
        if ($min_price !== null) {
            $this->db->where('tarif_per_m3 >=', $min_price);
        }
        
        if ($max_price !== null) {
            $this->db->where('tarif_per_m3 <=', $max_price);
        }
        
        $this->db->where('status', 'aktif');
        $this->db->order_by('tarif_per_m3', 'ASC');
        
        return $this->db->get($this->table)->result();
    }

    /**
     * Get latest tarif updates
     */
    public function get_recent_updates($limit = 10)
    {
        $this->db->order_by('updated_at', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get($this->table)->result();
    }

    /**
     * Validate tarif data
     */
    public function validate_data($data, $id = null)
    {
        $errors = array();
        
        // Check nama_tarif
        if (empty($data['nama_tarif'])) {
            $errors[] = 'Nama tarif wajib diisi';
        } elseif ($this->is_name_exists($data['nama_tarif'], $id)) {
            $errors[] = 'Nama tarif sudah digunakan';
        }
        
        // Check tarif_per_m3
        if (empty($data['tarif_per_m3']) || !is_numeric($data['tarif_per_m3']) || $data['tarif_per_m3'] <= 0) {
            $errors[] = 'Tarif per m³ harus berupa angka positif';
        }
        
        // Check batas_minimum
        if (empty($data['batas_minimum']) || !is_numeric($data['batas_minimum']) || $data['batas_minimum'] <= 0) {
            $errors[] = 'Batas minimum harus berupa angka positif';
        }
        
        // Check biaya_admin
        if (!isset($data['biaya_admin']) || !is_numeric($data['biaya_admin']) || $data['biaya_admin'] < 0) {
            $errors[] = 'Biaya admin harus berupa angka non-negatif';
        }
        
        // Check status
        if (empty($data['status']) || !in_array($data['status'], array('aktif', 'nonaktif'))) {
            $errors[] = 'Status harus aktif atau nonaktif';
        }
        
        return $errors;
    }

    /**
     * Get default tarif (most commonly used)
     */
    public function get_default()
    {
        $this->db->select('t.*, COUNT(p.id) as usage_count');
        $this->db->from($this->table . ' t');
        $this->db->join('penggunaan_air p', 't.id = p.id_tarif', 'left');
        $this->db->where('t.status', 'aktif');
        $this->db->group_by('t.id');
        $this->db->order_by('usage_count', 'DESC');
        $this->db->limit(1);
        
        return $this->db->get()->row();
    }
}